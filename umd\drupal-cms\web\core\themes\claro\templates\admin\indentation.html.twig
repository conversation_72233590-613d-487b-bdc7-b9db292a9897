{#
/**
 * @file
 * Theme override for a set of indentation divs.
 *
 * These <div> tags are used for drag and drop tables.
 *
 * Available variables:
 * - size: Optional. The number of indentations to create.
 */
#}
{% if size > 0 %}{% for i in 1..size %}<div class="js-indentation indentation">
  <svg
    xmlns="http://www.w3.org/2000/svg"
    class="tree"
    width="25"
    height="25"
    viewBox="0 0 25 25">
    <path
      class="tree__item tree__item-child-ltr tree__item-child-last-ltr tree__item-horizontal tree__item-horizontal-right"
      d="M12,12.5 H25"
      stroke="#888"/>
    <path
      class="tree__item tree__item-child-rtl tree__item-child-last-rtl tree__item-horizontal tree__horizontal-left"
      d="M0,12.5 H13"
      stroke="#888"/>
    <path
      class="tree__item tree__item-child-ltr tree__item-child-rtl tree__item-child-last-ltr tree__item-child-last-rtl tree__vertical tree__vertical-top"
      d="M12.5,12 v-99"
      stroke="#888"/>
    <path
      class="tree__item tree__item-child-ltr tree__item-child-rtl tree__vertical tree__vertical-bottom"
      d="M12.5,12 v99"
      stroke="#888"/>
  </svg>
</div>{% endfor %}{% endif %}
