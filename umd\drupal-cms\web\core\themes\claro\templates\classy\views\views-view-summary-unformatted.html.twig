{#
/**
 * @file
 * Theme override for unformatted summary links.
 *
 * Available variables:
 * - rows: The rows contained in this view.
 *   - url: The URL to this row's content.
 *   - count: The number of items this summary item represents.
 *   - separator: A separator between each row.
 *   - attributes: HTML attributes for a row.
 *   - active: A flag indicating whether the row is active.
 * - options: Flags indicating how each row should be displayed. This contains:
 *   - count: A flag indicating whether the row's 'count' should be displayed.
 *   - inline: A flag indicating whether the item should be wrapped in an inline
 *     or block level HTML element.
 *
 * @see template_preprocess_views_view_summary_unformatted()
 */
#}
{% for row in rows %}
  {{ options.inline ? '<span' : '<div' }} class="views-summary views-summary-unformatted">
  {% if row.separator -%}
    {{ row.separator }}
  {%- endif %}
  <a href="{{ row.url }}"{{ row.attributes.addClass(row.active ? 'is-active')|without('href') }}>{{ row.link }}</a>
  {% if options.count %}
    ({{ row.count }})
  {% endif %}
  {{ options.inline ? '</span>' : '</div>' }}
{% endfor %}
