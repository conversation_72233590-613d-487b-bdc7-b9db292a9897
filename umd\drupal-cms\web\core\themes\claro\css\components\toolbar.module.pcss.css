/**
 * @file toolbar.module.css
 *
 *
 * Aggressive resets so we can achieve a consistent look in hostile CSS
 * environments.
 *
 * If Claro is the admin theme, this stylesheet will be used by the active theme
 * even if the active theme is not Claro.
 */
#toolbar-administration,
#toolbar-administration * {
  box-sizing: border-box;
}
#toolbar-administration {
  margin: 0;
  padding: 0;
  vertical-align: baseline;
  font-size: small;
  line-height: 1;
}

@media print {
  #toolbar-administration {
    display: none;
  }
}
.toolbar-loading #toolbar-administration {
  overflow: hidden;
}
/**
 * Very specific overrides for Drupal system CSS.
 */
.toolbar li,
.toolbar .item-list,
.toolbar .item-list li,
.toolbar .menu-item,
.toolbar .menu-item--expanded {
  list-style-type: none;
  list-style-image: none;
}
.toolbar {
  & .menu-item {
    padding-block-start: 0;
  }

  & .toolbar-bar .toolbar-tab,
  & .menu-item {
    display: block;
  }
  & .toolbar-bar {
    & .toolbar-tab {
      &.hidden {
        display: none;
      }
    }
  }
  & a {
    display: block;
    line-height: 1;
  }
  & .toolbar-bar,
  & .toolbar-tray {
    position: relative;
    z-index: 1250;
  }
  & .toolbar-tray {
    z-index: 501;
    display: none;
  }
}

.toolbar-horizontal {
  & .toolbar-tray {
    position: fixed;
    inset-inline-start: 0;
    width: 100%;
  }
}
/* Position the admin toolbar absolutely when the configured standard breakpoint
 * is active. The toolbar container, that contains the bar and the trays, is
 * position absolutely so that it scrolls with the page. Otherwise, on smaller
 * screens, the components of the admin toolbar are positioned statically. */
.toolbar-oriented {
  & .toolbar-bar {
    position: absolute;
    inset-block-start: 0;
    inset-inline: 0;
  }
  & .toolbar-tray {
    position: absolute;
    inset-inline: 0;
  }
  & .toolbar-bar {
    z-index: 502; /* Layer the bar just above the trays and above contextual link triggers. */
  }
  & .toolbar-tray-vertical {
    position: absolute;
    inset-inline-start: -100%;
    width: 15rem;
  }
}
/* .toolbar-loading is required by Toolbar JavaScript to pre-render markup
 * style to avoid extra reflow & flicker. */
@media (min-width: 61em) {
  .toolbar-loading.toolbar-horizontal .toolbar .toolbar-bar .toolbar-tab:last-child .toolbar-tray {
    display: none;
  }
}

/* Position the admin toolbar fixed when the configured standard breakpoint is
 * active. */
.toolbar-fixed .toolbar-oriented .toolbar-bar {
  position: fixed;
}
/* When the configured narrow breakpoint is active, the toolbar is sized to wrap
 * around the trays in order to provide a context for scrolling tray content
 * that is taller than the viewport. */
.toolbar-tray-open.toolbar-fixed.toolbar-vertical .toolbar-oriented {
  inset-block-end: 0;
  width: 15rem;
}

/* Present the admin toolbar tabs horizontally as a default on user agents that
 * do not understand media queries or on user agents where JavaScript is
 * disabled. */
.toolbar-loading.toolbar-horizontal .toolbar .toolbar-tray .toolbar-menu > li,
.toolbar .toolbar-bar .toolbar-tab,
.toolbar .toolbar-tray-horizontal li {
  float: left; /* LTR */
}
[dir="rtl"] {
  & .toolbar-loading.toolbar-horizontal .toolbar .toolbar-tray .toolbar-menu > li,
  & .toolbar .toolbar-bar .toolbar-tab,
  & .toolbar .toolbar-tray-horizontal li {
    float: right;
  }
}
/* Present the admin toolbar tabs vertically by default on user agents that
 * that understand media queries. This will be the small screen default. */
@media only screen {
  .toolbar .toolbar-bar .toolbar-tab,
  .toolbar .toolbar-tray-horizontal li {
    float: none; /* LTR */
  }
}
/* This min-width media query is meant to provide basic horizontal layout to
 * the main menu tabs when JavaScript is disabled on user agents that understand
 * media queries. */
@media (min-width: 16.5em) {
  .toolbar .toolbar-bar .toolbar-tab,
  .toolbar .toolbar-tray-horizontal li {
    float: left; /* LTR */
  }
  [dir="rtl"] {
    & .toolbar .toolbar-bar .toolbar-tab,
    & .toolbar .toolbar-tray-horizontal li {
      float: right;
    }
  }
}
/* Present the admin toolbar tabs horizontally when the configured narrow
 * breakpoint is active. */
.toolbar-oriented .toolbar-bar .toolbar-tab,
.toolbar-oriented .toolbar-tray-horizontal li {
  float: left; /* LTR */
}
[dir="rtl"] {
  & .toolbar-oriented .toolbar-bar .toolbar-tab,
  & .toolbar-oriented .toolbar-tray-horizontal li {
    float: right;
  }
}

.toolbar .toolbar-tray-vertical > .toolbar-lining {
  min-height: 100%;
}
/* Layer the links just above the toolbar-tray. */
.toolbar .toolbar-bar .toolbar-tab > .toolbar-icon {
  position: relative;
  z-index: 502;
}
/* Hide secondary menus when the tray is horizontal. */
.toolbar-oriented .toolbar-tray-horizontal .menu-item ul {
  display: none;
}
/* When the configured standard breakpoint is active and the tray is in a
 * vertical position, the tray does not scroll with the page. The contents of
 * the tray scroll within the confines of the viewport.
 */
.toolbar .toolbar-tray-vertical.is-active,
.toolbar-fixed .toolbar .toolbar-tray-vertical {
  position: fixed;
  overflow-x: hidden;
  overflow-y: auto;
  height: 100%;
}
.toolbar .toolbar-tray.is-active {
  display: block;
}
/* Bring the tray into the viewport. By default it is just off-screen. */
.toolbar-oriented {
  & .toolbar-tray-vertical {
    &.is-active {
      inset-inline-start: 0;
    }
  }
}
/* When the configured standard breakpoint is active, the tray appears to push
 * the page content away from the edge of the viewport. */
.toolbar-tray-open.toolbar-vertical.toolbar-fixed {
  margin-inline-start: 15rem;
}

@media print {
  .toolbar-tray-open.toolbar-vertical.toolbar-fixed {
    margin-inline-start: 0;
  }
}

/**
 * ToolBar tray orientation toggle.
 */
/* Hide the orientation toggle when the configured narrow breakpoint is not
 * active. */
.toolbar {
  & .toolbar-tray {
    & .toolbar-toggle-orientation {
      display: none;
    }
  }
}
/* Show the orientation toggle when the configured narrow breakpoint is
 * active. */
.toolbar-oriented {
  & .toolbar-tray {
    & .toolbar-toggle-orientation {
      display: block;
    }
  }
  & .toolbar-tray-horizontal {
    & .toolbar-toggle-orientation {
      position: absolute;
      inset-block-start: auto;
      inset-inline-end: 0;
      inset-block-end: 0;
    }
  }
  & .toolbar-tray-vertical {
    & .toolbar-toggle-orientation {
      float: right; /* LTR */
      width: 100%;
    }
  }
}
[dir="rtl"] {
  & .toolbar-oriented .toolbar-tray-vertical .toolbar-toggle-orientation {
    float: left;
  }
}

/**
 * Toolbar home button toggle.
 */
.toolbar {
  & .toolbar-bar {
    & .home-toolbar-tab {
      display: none;
    }
  }
}

.path-admin {
  & .toolbar-bar {
    & .home-toolbar-tab {
      display: block;
    }
  }
}

/* Anti flicker styling. */
.toolbar-anti-flicker.toolbar-loading.toolbar-fixed body {
  padding-top: 2.4375rem;
}
.toolbar-anti-flicker.toolbar-loading.toolbar-fixed.toolbar-horizontal.toolbar-tray-open body {
  padding-top: 4.91331rem;
}
.toolbar-anti-flicker.toolbar-vertical.toolbar-tray-open .menu-item + .menu-item {
  border-top: 1px solid #ddd;
}

.toolbar-loading.toolbar-vertical.toolbar-tray-open .toolbar-tray {
  position: fixed;
  z-index: -1;
  top: 2.4375rem;
  bottom: 0;
  display: block;
  width: 15rem;
  inset-inline-start: 0;
}
.toolbar-tray-lazy-placeholder-link {
  position: relative;
  z-index: 0;
  display: block;
}
.toolbar-tray-open.toolbar-fixed.toolbar-vertical #toolbar-administration {
  margin-inline-start: -15rem;
}
.toolbar .toolbar-tray-vertical > .toolbar-lining::before {
  width: 100%;
}
.toolbar-oriented .toolbar-tray-vertical > .toolbar-lining::before {
  position: fixed;
  z-index: -1;
  top: 0;
  bottom: 0;
  display: block;
  width: 15rem;
  content: "";
  inset-inline-start: 0;
}
