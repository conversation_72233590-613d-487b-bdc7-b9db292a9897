{{attach_library('unilevelmlm/unilevelmlm')}}
<a class="scroll-to-top rounded" href="#">
    <i class="fas fa-angle-up"></i>
</a>
<div id="let-wrapper">
    <ul class="navbar-nav bg-gradient sidebar accordion" id="accordionSidebar">

        <a class="sidebar-brand let-d-flex let-align-items-center let-justify-content-center" href="#">
            <div class="sidebar-brand-icon ">
                <i class="fas fa-users"></i>
            </div>
            <div class="sidebar-brand-text let-mx-3">{{'UMP Dashboard' |t}}</div>
        </a>
        <hr class="sidebar-divider let-my-0">
         
        {% for value in menu_array %}
        {% if value.sub_list is not empty %}
        <li class="nav-item ">
            <a class="nav-link collapsed" href="#{{value.id}}" data-bs-toggle="collapse" aria-expanded="true"
                aria-controls="{{value.id}}">
                <i class="{{value.icon}}"></i>
                <span>{{value.title}}</span>
            </a>
            <div id="{{value.id}}" class="collapse">
                <div class="let-bg-warning let-py-2 collapse-inner let-rounded">
                    <h5 class="collapse-header">{{value.title}}</h5>
                    {% for sub in value.sub_list %}
                    <a class="collapse-item" href="{{sub.link}}">{{sub.title}}</a>
                    {% endfor %}
                </div>
            </div>
        </li>
        {% else %}
        <li class="nav-item">
            <a class="nav-link" href="{{value.link}}">
                <i class="{{value.icon}}"></i>
                <span>{{value.title}}</span></a>
        </li>
        {% endif %}
        {% endfor %}
        <div class="let-text-center let-d-none let-d-md-inline">
            <button class="let-rounded-circle let-border-0" id="sidebarToggle"></button>
        </div>
    </ul>
    <div id="let-content-wrapper" class="let-d-flex let-flex-column">
        <div id="let-content">
            <nav class="navbar navbar-expand navbar-light let-bg-white topbar let-mb-4 static-top shadow">
                <button id="sidebarToggleTop" class="let-btn let-btn-link let-d-md-none let-rounded-circle let-mr-3">
                    <i class="fa fa-bars"></i>
                </button>
                <div class="ump_page_title">{{current_user_name}}</div>
                <ul class="navbar-nav ml-auto">

                    <li class="nav-item dropdown no-arrow">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                            data-bs-toggle="dropdown" aria-haspopup="false" aria-expanded="false">
                            <span
                                class="let-m-2 let-let-d-none let-let-d-lg-inline let-text-dark let-let-small">{{current_user_name}}</span>
                            <img class="img-profile let-rounded-circle" src="{{profile_image}}">
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--grow-in"
                            aria-labelledby="userDropdown">
                            <!-- <a class="dropdown-item" href="#ump-launch-user-update-model" data-bs-toggle="modal">
                                <i class="fas fa-user fa-sm fa-fw let-mr-2 let-text-gray-400"></i>
                                {{'Profile' |t}}
                            </a> -->

                            <!-- <div class="dropdown-divider"></div> -->
                            <a class="dropdown-item" href="{{logout}}">
                                <i class="fas fa-sign-out-alt fa-sm fa-fw let-mr-2 let-text-gray-400"></i>
                                {{'Logout' |t}} </a>
                        </div>
                    </li>
                </ul>
            </nav>
            <div class="let-container-fluid">
                <!-- 
    user update profile
 -->
            <!-- <div class="let-loader-layer" id="image_upload_loader">
                <div class="let-pic-loader"></div>
            </div>
            <div class="modal fade" id="ump-launch-user-update-model" tabindex="-1" role="dialog" aria-labelledby="ump-launch-user-update-modelModalCenterTitle" aria-hidden="true">
                <div class="modal-dialog let-modal-dialog-centered" role="document">
                    <div class="let-modal-content">
                        <div class="let-modal-header">
                            <h5 class="let-modal-title" id="ump-launch-user-update-modelModalLongTitle"><img src="{{profile_image}}" width="150" height="150" class="let-text-center user_update_image let-img-round"> </h5>
        
                        </div>
                        <div class="let-modal-body">
                            <form id="ump_user_update_form" enctype="multipart/form-data">
                                <p class="let-text-center ">Update {{current_user_name}} Infomation</p>
                                <div class="let-form-group">        
                                    <input type="file" name="ump_user_image" accept="image/*" placeholder="{{'upload your image' |t }}" id="ump_user_image" class="let-form-control let-rounded-0 ">
                                    <div id="ump_user_image_message"></div>
                                </div>
                           
                                <div class="let-form-group">
        
                                    <input type="email" name="ump_user_email" placeholder="{{'Enter your email.' |t }}" id="ump_user_email" class="let-form-control let-rounded-0 " value="">
                                    <div id="ump_user_email_message"></div>
        
                                </div>
                                <div class=" let-form-group">
        
                                    <input type="text" name="ump_user_name" placeholder="{{'Enter your name...'|t }}" id="ump_user_name" class="let-form-control let-rounded-0 " value="{{current_user_name}}">
                                    <div id="ump_user_name_message"></div>
        
                                </div>
        
        
                                <div class="let-form-group let-p-2 let-text-center">
                                    <button type="submit" class="let-btn let-btn-sm let-btn-success">{{ 'Update' |t }}</button>
                                </div>
        
                            </form>
                        </div>
                        <div class="let-modal-footer">
                        </div>
                    </div>
                </div>
            </div> -->