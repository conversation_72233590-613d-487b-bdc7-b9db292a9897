{% include '@unilevelmlm/unilevelmlm-sidebar-template.html.twig' %}
<div class="let-card shadow let-mb-4">
    <div class="let-card-header ump-bg let-py-3">
        <h6 class="let-m-0 let-font-weight-bold ">{{'Join Commission List' |t}}</h6>
    </div>
    <div class="let-card-body">
        <div class="let-table-responsive">
            <table class="let-table let-table-bordered let-table-striped" id="payout_dataTable"
                width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>{{'# Payout ID' |t}}</th>
                        <th>{{'User Name' |t}}</th>
                        <th>{{'Child Name' |t}}</th>
                        <th>{{'Amount' |t}}</th>
                        <th>{{'Date' |t}}</th>
                    </tr>
                </thead>
                <tbody>
                    {% if join_commission %}
                    {% for value in join_commission %}
                    <tr>
                        <td>{{value.payout_id}}</td>
                        <td>{{value.user_name}}</td>
                        <td>{{value.child_name}}</td>
                        <td>{{value.amount}}</td>
                        <td>{{value.date_notified}}</td>
                    </tr>
                    {% endfor %}
                    {% else %}
                    <tr>No Data is Availble</tr>
                    {% endif %}
                </tbody>
                <tfoot>
                    <tr>
                        <th>{{'# Payout ID' |t}}</th>
                        <th>{{'User Name' |t}}</th>
                        <th>{{'Child Name' |t}}</th>
                        <th>{{'Amount' |t}}</th>
                        <th>{{'Date' |t}}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>
{% include '@unilevelmlm/unilevelmlm-footer-template.html.twig' %}