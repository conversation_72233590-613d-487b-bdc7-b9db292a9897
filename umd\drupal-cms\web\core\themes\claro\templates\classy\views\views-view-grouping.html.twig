{#
/**
 * @file
 * Theme override to display a single views grouping.
 *
 * Available variables:
 * - view: The view object.
 * - grouping: The grouping instruction.
 * - grouping_level: A number indicating the hierarchical level of the grouping.
 * - title: The group heading.
 * - content: The content to be grouped.
 * - rows: The rows returned from the view.
 *
 * @see template_preprocess_views_view_grouping()
 */
#}
<div class="view-grouping">
  <div class="view-grouping-header">{{ title }}</div>
  <div class="view-grouping-content">{{ content }}</div>
</div>
