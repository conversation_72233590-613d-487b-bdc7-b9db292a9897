unilevelmlm.settings.general_settings:
  title: General
  route_name: unilevelmlm.settings.general_settings
  description: 'General Settings '
  base_route: unilevelmlm.settings.general_settings

unilevelmlm.settings.distribute:
  title: Distribute
  route_name: unilevelmlm.settings.distribute
  description: 'Distribute'
  base_route: unilevelmlm.settings.general_settings

unilevelmlm.settings.run_payout:
  title: Run payout
  route_name: unilevelmlm.settings.run_payout
  description: 'Run Payout '
  base_route: unilevelmlm.settings.general_settings

unilevelmlm.settings.user_report:
  title: User Report
  route_name: unilevelmlm.settings.user_report
  description: 'User Report'
  base_route: unilevelmlm.settings.general_settings

unilevelmlm.settings.pay_report:
  title: Payout Report
  route_name: unilevelmlm.settings.pay_report
  description: 'Payout Report'
  base_route: unilevelmlm.settings.general_settings

unilevelmlm.settings.genealogy_function:
  title: Genealogy
  route_name: unilevelmlm.settings.genealogy_function
  description: 'Genealogy'
  base_route: unilevelmlm.settings.general_settings

unilevelmlm.settings.display_epins:
  title: ePins List
  route_name: unilevelmlm.settings.display_epins
  description: 'ePins List'
  base_route: unilevelmlm.settings.general_settings

unilevelmlm.settings.withdrawal:
  title: Withdrwal List
  route_name: unilevelmlm.settings.withdrawal
  description: 'User can withdrawal amount '
  base_route: unilevelmlm.settings.general_settings

unilevelmlm.settings.rebate_discount:
  title: Rebate/Discount
  route_name: unilevelmlm.settings.rebate_discount
  description: 'Define Rebate and Discount Rules'
  base_route: unilevelmlm.settings.general_settings