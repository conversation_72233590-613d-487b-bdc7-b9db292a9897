# This theme is marked as @internal. It is intended to evolve and change over
# minor releases.
# Change record https://www.drupal.org/node/2582945.
# As the UI of Drupal improves between minor versions, the markup and assets
# in the Claro theme will change. The Claro theme is not backwards compatible.
# If you wish to modify the output or assets of Claro you can:
# 1. Copy the whole of Claro and rename it as your own administration theme. You
# will need to manually manage your own updates if you want to stay up to
# date with <PERSON>laro's bug fixes and feature support.
#
# 2. Sub-theme Claro. This is only recommended if you want to make minor tweaks
# and understand that Clar<PERSON> could break your modifications as it changes.
name: Claro
type: theme
base theme: false
description: A clean, accessible, and powerful Drupal administration theme.
alt text: Screenshot of Claro, Drupal administration theme.
package: Core
version: VERSION
libraries:
  - core/drupal.message
  - core/normalize
  - claro/global-styling
libraries-override:
  core/drupal.tabledrag:
    css:
      component:
        misc/components/tabledrag.module.css: css/components/tabledrag.css

  core/drupal.ajax:
    css:
      component:
        misc/components/ajax-progress.module.css: css/components/ajax-progress.module.css

  core/drupal.autocomplete:
    css:
      component:
        misc/components/autocomplete-loading.module.css: css/components/autocomplete-loading.module.css

  system/base:
    css:
      component:
        css/components/system-status-counter.css: css/components/system-status-counter.css
        css/components/system-status-report-counters.css: css/components/system-status-report-counters.css
        css/components/system-status-report-general-info.css: css/components/system-status-report-general-info.css

  system/admin:
    css:
      theme:
        css/system.admin.css: false

  core/drupal.dropbutton:
    css:
      component:
        misc/dropbutton/dropbutton.css: css/components/dropbutton.css

  core/drupal.vertical-tabs:
    css:
      component:
        misc/vertical-tabs.css: false

  core/internal.jquery_ui:
    css:
      theme:
        assets/vendor/jquery.ui/themes/base/theme.css: false

  core/drupal.dialog:
    css:
      component:
        assets/vendor/jquery.ui/themes/base/dialog.css: false

  user/drupal.user:
    css:
      component:
        css/user.module.css: false

  field_ui/drupal.field_ui:
    css:
      theme:
        css/field_ui.admin.css: css/theme/field-ui.admin.css

  node/drupal.node:
    css:
      layout:
        css/node.module.css: false

  node/form:
    css:
      layout:
        css/node.module.css: false

  toolbar/toolbar:
    css:
      component:
        css/toolbar.module.css: css/components/toolbar.module.css
      theme:
        css/toolbar.theme.css: css/theme/toolbar.theme.css
        css/toolbar.icons.theme.css: css/theme/toolbar.icons.theme.css

  toolbar/toolbar.menu:
    css:
      state:
        css/toolbar.menu.css: css/state/toolbar.menu.css

  views_ui/admin.styling:
    css:
      component:
        css/views_ui.admin.css: css/components/views_ui.admin.css
      theme:
        css/views_ui.admin.theme.css: css/theme/views_ui.admin.theme.css

libraries-extend:
  core/drupal.collapse:
    - claro/details-focus
  core/drupal.dialog:
    - claro/claro.drupal.dialog
  core/drupal.dropbutton:
    - claro/dropbutton
  core/drupal.checkbox:
    - claro/checkbox
  core/drupal.message:
    - claro/messages
  core/drupal.progress:
    - claro/progress
  core/drupal.tabbingmanager:
    - claro/tabbingmanager
  core/drupal.tabledrag:
    - claro/claro.tabledrag
  core/drupal.tableselect:
    - claro/tableselect
  core/drupal.vertical-tabs:
    - claro/vertical-tabs
  file/drupal.file:
    - claro/file
  filter/drupal.filter.admin:
    - claro/filter
  filter/drupal.filter:
    - claro/filter
  system/admin:
    - claro/system.admin
  core/drupal.autocomplete:
    - claro/autocomplete
  shortcut/drupal.shortcut:
    - claro/drupal.shortcut
  core/drupal.ajax:
    - claro/ajax
  user/drupal.user:
    - claro/form.password-confirm
  node/drupal.node.preview:
    - claro/drupal.node.preview
  views/views.module:
    - claro/views
  views_ui/admin.styling:
    - claro/views_ui
  media/media_embed_ckeditor_theme:
    - claro/classy.media_embed_ckeditor_theme
  media_library/ui:
    - claro/media_library.ui
  media_library/view:
    - claro/media_library.theme
  media_library/widget:
    - claro/media_library.theme
  image/admin:
    - claro/image.admin

regions:
  header: Header
  pre_content: Pre-content
  breadcrumb: Breadcrumb
  highlighted: Highlighted
  help: Help
  content: Content
  page_top: Page top
  page_bottom: Page bottom
  sidebar_first: First sidebar
regions_hidden:
  - sidebar_first
