<?php

// phpcs:ignoreFile

declare(strict_types=1);

namespace Drupal\Tests\Component\Foo {

  use Drupal\BuildTests\Framework\BuildTestBase;
  use Drupal\FunctionalJavascriptTests\WebDriverTestBase;
  use Drupal\KernelTests\KernelTestBase;
  use Drupal\Tests\BrowserTestBase;
  use Drupal\Tests\UnitTestCase;
  use PHPUnit\Framework\TestCase;

  final class FooTest extends TestCase {
  }

  final class UnitTest extends UnitTestCase {
  }

  final class BuildTest extends BuildTestBase {
  }

  final class KernelTest extends KernelTestBase {
  }

  final class FunctionalTest extends BrowserTestBase {
  }

  final class FunctionalJavascriptTest extends WebDriverTestBase {
  }

}

namespace Drupal\Tests\Core\Foo {

  use Drupal\BuildTests\Framework\BuildTestBase;
  use Drupal\FunctionalJavascriptTests\WebDriverTestBase;
  use Drupal\KernelTests\KernelTestBase;
  use Drupal\Tests\BrowserTestBase;
  use Drupal\Tests\UnitTestCase;
  use PHPUnit\Framework\TestCase;

  final class FooTest extends TestCase {
  }

  final class UnitTest extends UnitTestCase {
  }

  final class BuildTest extends BuildTestBase {
  }

  final class KernelTest extends KernelTestBase {
  }

  final class FunctionalTest extends BrowserTestBase {
  }

  final class FunctionalJavascriptTest extends WebDriverTestBase {
  }

}
