name: Blog
type: Drupal CMS
description: Adds a blog post content type and listing page.
recipes:
  - drupal_cms_page
install:
  - add_content_by_bundle
  - menu_link_content
  - selective_better_exposed_filters
config:
  strict: false
  import:
    system:
      - system.menu.main
  actions:
    user.role.content_editor:
      grantPermissions:
        - 'create blog content'
        - 'delete blog revisions'
        - 'delete any blog content'
        - 'edit any blog content'
    workflows.workflow.basic_editorial:
      addNodeTypes: ['blog']
