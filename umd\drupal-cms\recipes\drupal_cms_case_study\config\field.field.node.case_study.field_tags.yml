langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_tags
    - node.type.case_study
    - taxonomy.vocabulary.tags
id: node.case_study.field_tags
field_name: field_tags
entity_type: node
bundle: case_study
label: Tags
description: 'Include tags for relevant topics.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      tags: tags
    sort:
      field: _none
    auto_create: true
field_type: entity_reference
