function drawOrganizationChart(b){function h(h,d){function D(a){return a.split(',').map(function(a){return'<li><div class="tag">'+a+'</div></li>  ';}).join('');}function x(b){var a='';return a+='  <div class="customTooltip">',a+='  <div class="tool-container">',a+='  <div class="tool-user-profile">',a+='  <div class="tool-avatar-container">',a+='  <img src="'+b.imageUrl+'" alt="" class="tool-avatar" />',a+='  </div><h2 class="tool-user-name">'+b.name+'</h2>',a+='  <ul class="tool-hobbies">',a+='  <li class="tool-hobby">'+b.userData.sponsor+'<br/>Sponsor</li>',a+='  <li class="tool-hobby">'+b.userData.parent+'<br/>Parent</li>',a+='  <li class="tool-hobby">'+b.userData.position+'<br/>Position</li>',a+='  </ul></div>',a+='  <div class="tool-user-info">',a+='  <div class="tool-followers">',a+='  <h3>'+b.userData.total_earning+'</h3><small>Earning</small></div>',a+='  <div class="tool-following"><h3>'+b.userData.downlines+'</h3> <small>Ref...</small></div>',a+='  <div class="tool-number-of-posts"><h3>'+b.userData.total_widral+'</h3> <small>Withdraw</small> </div>',a+='  </div>',a+=' <div class="tool-latest-tweet">',b.userData.is_admin!=1&&(a+='  <ul class="tool-add-box">',b.userData.left_link!=0&&(a+='  <li class="tool-hobby"><a href="'+b.userData.left_link+'" class="tool-btn"><i class="fa fa-plus"></i>Left</a></li>'),b.userData.right_link!=0&&(a+='  <li class="tool-hobby"><a href="'+b.userData.right_link+'" class="tool-btn"><i class="fa fa-plus"></i>Right</a></li>'),a+='  </ul>'),a+=' </div></div>',a+='  </div>',a;}function y(b){var a=x(b);f.html(a),f.transition().duration(500).style('opacity','1').style('display','block'),d3.select(this).attr('cursor','pointer').attr('stroke-width',50);var c=d3.event.pageY;var d=d3.event.pageX;console.log(d3.event.pageY),console.log(d3.event.pageX),f.style({bottom:'700px',right:'500px'});}function A(){f.transition().duration(200).style('opacity','0').style('display','none'),d3.select(this).attr('stroke-width',5);}function C(){return this==d3.event.target;}var i=r.nodes(a.root).reverse(),t=r.links(i);i.forEach(function(b){b.y=b.depth*a.linkLineSize;});var o=g.selectAll('g.node').data(i,function(b){return b.id||(b.id=++a.index);});var s=o.enter().append('g').attr('class','node').attr('transform',function(a){return'translate('+h.x0+','+h.y0+')';});var b=s.append('g').attr('class','node-group');b.append('rect').attr('width',a.nodeWidth).attr('height',a.nodeHeight).attr('data-node-group-id',function(a){return a.uniqueIdentifier;}).attr('class',function(b){var a='';return b.isLoggedUser&&(a+='nodeRepresentsCurrentUser '),a+=b._children||b.children?'nodeHasChildren':'nodeDoesNotHaveChildren',a;});var p=s.append('g').attr('class','collapse-pos').attr('data-id',function(a){return a.uniqueIdentifier;});var u=p.append('circle').attr('class','node-collapse').attr('cx',a.nodeWidth-a.collapseCircleRadius).attr('cy',a.nodeHeight-7).attr('',M);u.attr('r',function(b){return b.children||b._children?a.collapseCircleRadius:0;}).attr('height',a.collapseCircleRadius),p.append('text').attr('class','text-collapse').attr('x',a.nodeWidth-a.collapseCircleRadius).attr('y',a.nodeHeight-3).attr('width',a.collapseCircleRadius).attr('height',a.collapseCircleRadius).style('font-size',a.collapsibleFontSize).attr('text-anchor','middle').style('font-family','FontAwesome').style('transform','translate(-96px,7px);').text(function(a){return a.collapseText;}),p.on('click',z),b.append('text').attr('x',c.nodeTextLeftMargin).attr('y',a.nodePadding+10).attr('class','user-name').attr('text-anchor','left').text(function(a){return a.name.trim();}).call(B,a.nodeWidth),b.append('text').attr('x',c.nodeTextLeftMargin).attr('y',c.nodeUserKeyTopMargin).attr('class','user-key').attr('dy','.35em').attr('text-anchor','left').text(function(b){var a=b.userKey.substring(0,27);return a.length<b.userKey.length&&(a=a.substring(0,24)+'...'),a;}),b.append('text').attr('x',c.nodeTextLeftMargin).attr('y',a.nodePadding+10+c.nodeImageHeight/4*2).attr('class','userData').attr('dy','.35em').attr('text-anchor','left').text(function(a){return a.userData.sponsor;}),b.append('text').attr('x',c.nodeTextLeftMargin).attr('y',c.nodeChildCountTopMargin).attr('class','emp-count-icon').attr('text-anchor','left').style('font-family','FontAwesome').text(function(b){return b.children||b._children?a.userIcon:void 0;}),b.append('text').attr('x',c.nodeTextLeftMargin+13).attr('y',c.nodeChildCountTopMargin).attr('class','emp-count').attr('text-anchor','left').text(function(a){return a.children?a.children.length:a._children?a._children.length:void 0;}),b.append('defs').append('svg:clipPath').attr('id','clip').append('svg:rect').attr('id','clip-rect').attr('rx',3).attr('x',a.nodePadding).attr('y',2+a.nodePadding).attr('width',c.nodeImageWidth).attr('fill','none').attr('height',c.nodeImageHeight-4),b.append('svg:image').attr('y',2+a.nodePadding).attr('x',a.nodePadding).attr('preserveAspectRatio','none').attr('width',c.nodeImageWidth).attr('height',c.nodeImageHeight-4).attr('clip-path','url(#clip)').attr('xlink:href',function(a){return a.imageUrl;});var v=o.transition().duration(a.duration).attr('transform',function(a){return'translate('+a.x+','+a.y+')';});v.select('rect').attr('width',a.nodeWidth).attr('height',a.nodeHeight).attr('rx',3).attr('stroke',function(b){return d&&b.uniqueIdentifier==d.locate?'#a1ceed':a.nodeStroke;}).attr('stroke-width',function(b){return d&&b.uniqueIdentifier==d.locate?6:a.nodeStrokeWidth;});var w=o.exit().transition().duration(a.duration).attr('transform',function(a){return'translate('+h.x+','+h.y+')';}).remove();w.select('rect').attr('width',a.nodeWidth).attr('height',a.nodeHeight);var q=g.selectAll('path.link').data(t,function(a){return a.target.id;});if(q.enter().insert('path','g').attr('class','link').attr('x',a.nodeWidth/2).attr('y',a.nodeHeight/2).attr('d',function(b){var a={x:h.x0,y:h.y0};return k({source:a,target:a});}),q.transition().duration(a.duration).attr('d',k),q.exit().transition().duration(a.duration).attr('d',function(b){var a={x:h.x,y:h.y};return k({source:a,target:a});}).remove(),i.forEach(function(a){a.x0=a.x,a.y0=a.y;}),d&&d.locate){var m;var n;i.forEach(function(a){a.uniqueIdentifier==d.locate&&(m=a.x,n=a.y);});var j=-m+window.innerWidth/2;var l=-n+window.innerHeight/2;g.attr('transform','translate('+j+','+l+')'),e.translate([j,l]),e.scale(1);}if(d&&d.centerMySelf){var m;var n;i.forEach(function(a){a.isLoggedUser&&(m=a.x,n=a.y);});var j=-m+window.innerWidth/2;var l=-n+window.innerHeight/2;g.attr('transform','translate('+j+','+l+')'),e.translate([j,l]),e.scale(1);}b.on('click',y),b.on('dblclick',A),d3.select('body').on('click',function(){var a=f.filter(C).empty();a&&f.style('opacity','0').style('display','none');});}function z(b){d3.select(this).select('text').text(function(b){return b.collapseText==a.EXPAND_SYMBOL?b.collapseText=a.COLLAPSE_SYMBOL:b.children&&(b.collapseText=a.EXPAND_SYMBOL),b.collapseText;}),b.children?(b._children=b.children,b.children=null):(b.children=b._children,b._children=null),h(b);}function A(){g.attr('transform','translate('+d3.event.translate+')'+' scale('+d3.event.scale+')');}function B(a,b){a.each(function(){var a=d3.select(this),i=a.text().split(/\s+/).reverse(),e,c=[],j=0,k=1.1,f=a.attr('x'),g=a.attr('y'),h=0,d=a.text(null).append('tspan').attr('x',f).attr('y',g).attr('dy',h+'em');while(e=i.pop())c.push(e),d.text(c.join(' ')),d.node().getComputedTextLength()>b&&(c.pop(),d.text(c.join(' ')),c=[e],d=a.append('tspan').attr('x',f).attr('y',g).attr('dy',++j*k+h+'em').text(e));});}function l(b,c,a){a[b]?a[b]=a[b]+' '+c(a):a[b]=c(a),a.children&&a.children.forEach(function(a){l(b,c,a);}),a._children&&a._children.forEach(function(a){l(b,c,a);});}function C(g){if(p(['.customTooltip-wrapper']),g.type=='department'&&b.mode!='department'){var h=!1;var i=b.pristinaData.children;parentLoop:for(var c=0;c<i.length;c++){var e=i[c];var j=e.children?e.children:e._children;for(var f=0;f<j.length;f++){var a=j[f];if(a.unit.value.trim()==g.value.trim()){q(b.selector),p(['.btn-action']),u(['.btn-action.btn-back','.btn-action.btn-fullscreen','.department-information']),d('.dept-name',g.value),d('.dept-emp-count','Employees Quantity - '+D(a)),d('.dept-description',a.unit.desc),b.oldData=b.data,b.data=t(a),h=!0;break parentLoop;}}}h&&(b.mode='department',b.funcs.closeSearchBox(),drawOrganizationChart(b));}}function D(c){function b(c){var d=c.children?c.children:c._children;d&&d.forEach(function(c){a++,b(c);});}var a=1;return b(c),a;}function E(h){var a=h.map(function(b){var a='';return a+='         <div class="list-item">',a+='         <div class="let-col-md-12 let-col-xs-12 let-row let-m-0 let-p-0">',a+='         <div class="let-col-md-4 let-col-xs-12">',a+='              <img class="image let-m-2" src="'+b.imageUrl+'"/>',a+='            </div>',a+='         <div class="let-col-md-4 let-col-xs-12">',a+='         <div class="let-m-2">',a+='              <p class="name">'+b.name+'</p>',a+='               <p class="position-name">'+b.userKey+'</p>',a+='               <p class="userData">'+b.userData.sponsor+'</p>',a+='            </div>',a+='            </div>',a+='         <div class="let-col-md-4 let-col-xs-12">',a+='         <div class="let-m-2">',a+="              <a target='_blank' href='"+b.profileUrl+"'><button class='let-btn let-btn-danger let-btn-sm let-rounded-0 let-w-100'>View</button></a>",a+="              <br/><button class='let-btn let-btn-success let-btn-sm let-rounded-0 let-w-100' onclick='params.funcs.locate("+b.uniqueIdentifier+")'>Locate </button>",a+='            </div>',a+='            </div>',a+='        </div>',a;});var e=a.join('');b.funcs.clearResult();var c=w('.result-list');var f=c.innerHTML;var g=e+f;c.innerHTML=g,d('.user-search-box .result-header','RESULT - '+a.length);}function F(){d('.result-list','<div class="buffer" ></div>'),d('.user-search-box .result-header','RESULT');}function G(){var a=w('.user-search-box .search-input');a.addEventListener('input',function(){var c=a.value?a.value.trim():'';if(c.length<3)b.funcs.clearResult();else{var d=b.funcs.findInTree(b.data,c);b.funcs.reflectResults(d);}});}function H(){d3.selectAll('.user-search-box').transition().duration(250).style('display','block').style('width','350px');}function I(){d3.selectAll('.user-search-box').transition().duration(250).style('width','0px').style('display','none').each('end',function(){b.funcs.clearResult(),q('.search-input');});}function J(e,b){function d(e){(e.name.match(a)||e.userData.position.match(a)||e.userData.sponsor.match(a)||e.userKey.match(a))&&c.push(e);var f=e.children?e.children:e._children;f&&f.forEach(function(a){d(a,b);});}var c=[];var a=new RegExp(b,'i');return d(e,b),c;}function K(){u(['.btn-action']),p(['.customTooltip-wrapper','.btn-action.btn-back','.department-information']),q(b.selector),b.mode='full',b.data=t(b.pristinaData),drawOrganizationChart(b);}function L(){j(root),h(root);}function j(b){b.children&&b.children.forEach(j),b._children&&(b.children=b._children,b.children.forEach(j),b._children=null),b.children&&m(b,a.COLLAPSE_SYMBOL);}function i(b){b._children&&b._children.forEach(i),b.children&&(b._children=b.children,b._children.forEach(i),b.children=null),b._children&&m(b,a.EXPAND_SYMBOL);}function M(b){b._children?b.collapseText=a.EXPAND_SYMBOL:b.children&&(b.collapseText=a.COLLAPSE_SYMBOL);}function m(a,b){a.collapseText=b,d3.select("*[data-id='"+a.uniqueIdentifier+"']").select('text').text(b);}function n(a){a.isLoggedUser?s(a):a._children?a._children.forEach(function(b){b.parent=a,n(b);}):a.children&&a.children.forEach(function(b){b.parent=a,n(b);});}function o(a,b){a.uniqueIdentifier==b?s(a):a._children?a._children.forEach(function(c){c.parent=a,o(c,b);}):a.children&&a.children.forEach(function(c){c.parent=a,o(c,b);});}function s(b){while(b.parent)b=b.parent,b.children||(b.children=b._children,b._children=null,m(b,a.COLLAPSE_SYMBOL));}function N(){document.fullScreenElement&&document.fullScreenElement!==null||!(document.mozFullScreen||document.webkitIsFullScreen)?(document.documentElement.requestFullScreen?document.documentElement.requestFullScreen():document.documentElement.mozRequestFullScreen?document.documentElement.mozRequestFullScreen():document.documentElement.webkitRequestFullScreen&&document.documentElement.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT),d3.select(b.selector+' svg').attr('width',screen.width).attr('height',screen.height)):(document.cancelFullScreen?document.cancelFullScreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.webkitCancelFullScreen&&document.webkitCancelFullScreen(),d3.select(b.selector+' svg').attr('width',b.chartWidth).attr('height',b.chartHeight));}function O(){a.root.children||a.root.isLoggedUser||(a.root.children=a.root._children),a.root.children&&(a.root.children.forEach(i),a.root.children.forEach(n)),h(a.root,{centerMySelf:!0});}function P(b){a.root.children||!a.root.uniqueIdentifier==b&&(a.root.children=a.root._children),a.root.children&&(a.root.children.forEach(i),a.root.children.forEach(function(a){o(a,b);})),h(a.root,{locate:b});}function t(a){return JSON.parse(JSON.stringify(a));}function u(a){v(a,'initial');}function p(a){v(a,'none');}function v(a,b){a.forEach(function(c){var a=x(c);a.forEach(function(a){a.style.display=b;});});}function d(c,a){var b=x(c);b.forEach(function(b){b.innerHTML=a,b.value=a;});}function q(a){d(a,'');}function w(a){return document.querySelector(a);}function x(a){return document.querySelectorAll(a);}G(),b.funcs.showMySelf=O,b.funcs.expandAll=L,b.funcs.search=H,b.funcs.closeSearchBox=I,b.funcs.findInTree=J,b.funcs.clearResult=F,b.funcs.reflectResults=E,b.funcs.departmentClick=C,b.funcs.back=K,b.funcs.toggleFullScreen=N,b.funcs.locate=P;var a={EXPAND_SYMBOL:'\uF067',COLLAPSE_SYMBOL:'\uF068',selector:b.selector,root:b.data,width:b.chartWidth,height:b.chartHeight,index:0,nodePadding:9,collapseCircleRadius:7,nodeHeight:80,nodeWidth:210,duration:750,rootNodeTopMargin:20,minMaxZoomProportions:[0.05,3],linkLineSize:180,collapsibleFontSize:'10px',userIcon:'\uF007',nodeStroke:'#ccc',nodeStrokeWidth:'1px'};var c={};c.nodeImageWidth=a.nodeHeight*100/140,c.nodeImageHeight=a.nodeHeight-2*a.nodePadding,c.nodeTextLeftMargin=a.nodePadding*2+c.nodeImageWidth,c.rootNodeLeftMargin=a.width/2,c.nodeUserKeyTopMargin=a.nodePadding+8+c.nodeImageHeight/4*1,c.nodeChildCountTopMargin=a.nodePadding+14+c.nodeImageHeight/4*3;var r=d3.layout.tree().nodeSize([a.nodeWidth+40,a.nodeHeight]);var k=d3.svg.diagonal().projection(function(b){return[b.x+a.nodeWidth/2,b.y+a.nodeHeight/2];});var e=d3.behavior.zoom().scaleExtent(a.minMaxZoomProportions).on('zoom',A);var g=d3.select(a.selector).append('svg').attr('width',a.width).attr('height',a.height).call(e).append('g').attr('transform','translate('+(a.width-240)/2+','+20+')');if(e.translate([c.rootNodeLeftMargin,a.rootNodeTopMargin]),a.root.x0=0,a.root.y0=c.rootNodeLeftMargin,b.mode!='department'){var y=1;l('uniqueIdentifier',function(a){return y++;},a.root);}j(a.root),a.root.children&&a.root.children.forEach(i),h(a.root),d3.select(a.selector).style('height',a.height);var f=d3.select('#svgChart').append('div').attr('class','customTooltip-wrapper');}var genealogy_url=WPURLS.plugin_url+'/includes/BMW-genealogy-json.php';var params={selector:'#svgChart',dataLoadUrl:genealogy_url,chartWidth:$('#svgChart').innerWidth()-10,chartHeight:$(window).height()-100,funcs:{showMySelf:null,search:null,closeSearchBox:null,clearResult:null,findInTree:null,reflectResults:null,departmentClick:null,back:null,toggleFullScreen:null,locate:null},data:null};d3.json(params.dataLoadUrl,function(a){params.data=a,params.pristinaData=JSON.parse(JSON.stringify(a)),drawOrganizationChart(params);});