/*
 * DO NOT EDIT THIS FILE.
 * See the following change record for more information,
 * https://www.drupal.org/node/3084859
 * @preserve
 */
/**
 * @file toolbar.theme.css
 *
 * If Claro is the admin theme, this stylesheet will be used by the active theme
 * even if the active theme is not Claro.
 */
.toolbar {
  font-family: "Source Sans Pro", "Lucida Grande", Verdana, sans-serif;
  /* Set base font size to 13px based on root ems. */
  font-size: 0.8125rem;
  -moz-tap-highlight-color: rgba(0, 0, 0, 0);
  -o-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  tap-highlight-color: rgba(0, 0, 0, 0);
  -moz-touch-callout: none;
  -o-touch-callout: none;
  -webkit-touch-callout: none;
  touch-callout: none;
}
.toolbar .toolbar-item {
  padding: 1em 1.3333em;
  cursor: pointer;
  -webkit-text-decoration: none;
  text-decoration: none;
  line-height: 1em;
}
.toolbar .toolbar-item:hover,
.toolbar .toolbar-item:focus {
  -webkit-text-decoration: underline;
  text-decoration: underline;
}
/**
 * Toolbar bar.
 */
.toolbar .toolbar-bar {
  color: #ddd;
  background-color: #0f0f0f;
  box-shadow: -1px 0 3px 1px rgba(0, 0, 0, 0.3333); /* LTR */
}
[dir="rtl"] .toolbar .toolbar-bar {
  box-shadow: 1px 0 3px 1px rgba(0, 0, 0, 0.3333);
}
.toolbar .toolbar-bar .toolbar-item {
  color: #fff;
}
.toolbar .toolbar-bar .toolbar-tab > .toolbar-item {
  font-weight: bold;
}
.toolbar .toolbar-bar .toolbar-tab > .toolbar-item:hover,
.toolbar .toolbar-bar .toolbar-tab > .toolbar-item:focus {
  background-image: linear-gradient(rgba(255, 255, 255, 0.125) 20%, transparent 200%);
}
.toolbar .toolbar-bar .toolbar-tab > .toolbar-item.is-active {
  color: #000;
  border-bottom: 1px solid #ddd;
  background-color: #fff;
}
/**
 * Toolbar tray.
 */
.toolbar .toolbar-tray {
  background-color: #fff;
}
.toolbar-horizontal .toolbar-tray > .toolbar-lining {
  padding-right: 5em; /* LTR */
}
[dir="rtl"] .toolbar-horizontal .toolbar-tray > .toolbar-lining {
  padding-right: 0;
  padding-left: 5em;
}
.toolbar .toolbar-tray-vertical {
  border-right: 1px solid #aaa; /* LTR */
  background-color: #f5f5f5;
  box-shadow: -1px 0 5px 2px rgba(0, 0, 0, 0.3333); /* LTR */
}
[dir="rtl"] .toolbar .toolbar-tray-vertical {
  border-right: 0 none;
  border-left: 1px solid #aaa;
  box-shadow: 1px 0 5px 2px rgba(0, 0, 0, 0.3333);
}
.toolbar-horizontal .toolbar-tray {
  border-bottom: 1px solid #aaa;
  box-shadow: -2px 1px 3px 1px rgba(0, 0, 0, 0.3333); /* LTR */
}
[dir="rtl"] .toolbar-horizontal .toolbar-tray {
  box-shadow: 2px 1px 3px 1px rgba(0, 0, 0, 0.3333);
}
.toolbar .toolbar-tray-horizontal .toolbar-tray {
  background-color: #f5f5f5;
}
.toolbar-tray a,
.toolbar-tray a:visited {
  padding: 1em 1.3333em;
  cursor: pointer;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: #565656;
}
.toolbar-tray a:hover,
.toolbar-tray a:active,
.toolbar-tray a:focus,
.toolbar-tray a.is-active {
  -webkit-text-decoration: underline;
  text-decoration: underline;
  color: #000;
}
.toolbar .toolbar-menu {
  background-color: #fff;
}
.toolbar-horizontal .toolbar-tray .menu-item + .menu-item {
  border-left: 1px solid #ddd; /* LTR */
}
[dir="rtl"] .toolbar-horizontal .toolbar-tray .menu-item + .menu-item {
  border-right: 1px solid #ddd;
  border-left: 0 none;
}
.toolbar-horizontal .toolbar-tray .menu-item:last-child {
  border-right: 1px solid #ddd; /* LTR */
}
[dir="rtl"] .toolbar-horizontal .toolbar-tray .menu-item:last-child {
  border-left: 1px solid #ddd;
}
.toolbar .toolbar-tray-vertical .menu-item + .menu-item {
  border-top: 1px solid #ddd;
}
.toolbar .toolbar-tray-vertical .menu-item:last-child {
  border-bottom: 1px solid #ddd;
}
.toolbar .toolbar-tray-vertical .menu-item .menu-item {
  border: 0 none;
}
.toolbar .toolbar-tray-vertical .toolbar-menu ul ul {
  border-top: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
}
.toolbar .toolbar-tray-vertical .menu-item:last-child > ul {
  border-bottom: 0;
}
.toolbar .toolbar-tray-vertical .toolbar-menu .toolbar-menu .toolbar-menu .toolbar-menu {
  margin-left: 0.25em; /* LTR */
}
[dir="rtl"] .toolbar .toolbar-tray-vertical .toolbar-menu .toolbar-menu .toolbar-menu .toolbar-menu {
  margin-right: 0.25em;
  margin-left: 0;
}
.toolbar .toolbar-menu .toolbar-menu a {
  color: #434343;
}
/**
 * Orientation toggle.
 */
.toolbar .toolbar-toggle-orientation {
  height: 100%;
  padding: 0;
  background-color: #f5f5f5;
}
.toolbar-horizontal .toolbar-tray .toolbar-toggle-orientation {
  border-left: 1px solid #c9c9c9; /* LTR */
}
[dir="rtl"] .toolbar-horizontal .toolbar-tray .toolbar-toggle-orientation {
  border-right: 1px solid #c9c9c9;
  border-left: 0 none;
}
.toolbar .toolbar-toggle-orientation > .toolbar-lining {
  float: right; /* LTR */
}
[dir="rtl"] .toolbar .toolbar-toggle-orientation > .toolbar-lining {
  float: left;
}
.toolbar .toolbar-toggle-orientation button {
  display: inline-block;
  cursor: pointer;
}
@media (forced-colors: active) {
  .toolbar-horizontal .toolbar-tray > .toolbar-lining,
  .toolbar-horizontal .toolbar .toolbar-toggle-orientation .toolbar-icon {
    border-top: 1px solid transparent;
  }
  .toolbar .toolbar-bar {
    border-bottom: 1px solid transparent;
  }
}
