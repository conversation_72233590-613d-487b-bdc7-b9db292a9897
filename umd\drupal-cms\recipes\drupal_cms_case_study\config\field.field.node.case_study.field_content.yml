langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_content
    - filter.format.content_format
    - node.type.case_study
  module:
    - text
id: node.case_study.field_content
field_name: field_content
entity_type: node
bundle: case_study
label: Content
description: 'The content of this page.'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  allowed_formats:
    - content_format
field_type: text_long
