langcode: en
status: true
dependencies:
  config:
    - filter.format.content_format
    - linkit.linkit_profile.default
  module:
    - ckeditor5
format: content_format
editor: ckeditor5
settings:
  toolbar:
    items:
      - heading
      - bold
      - italic
      - blockQuote
      - link
      - bulletedList
      - numberedList
      - indent
      - outdent
      - alignment
      - insertTable
      - drupalMedia
      - undo
      - redo
      - removeFormat
      - sourceEditing
  plugins:
    ckeditor5_alignment:
      enabled_alignments:
        - center
        - justify
        - left
        - right
    ckeditor5_heading:
      enabled_headings:
        - heading2
        - heading3
        - heading4
        - heading5
        - heading6
    ckeditor5_list:
      properties:
        reversed: true
        startIndex: true
      multiBlock: true
    ckeditor5_sourceEditing:
      allowed_tags: {  }
    linkit_extension:
      linkit_enabled: true
      linkit_profile: default
    media_media:
      allow_view_mode_override: true
image_upload:
  status: false
