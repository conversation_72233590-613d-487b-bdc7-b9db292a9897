<?php

namespace Drupal\unilevelmlm\Controller;

use <PERSON><PERSON><PERSON>\Core\Controller\ControllerBase;
use <PERSON><PERSON>al\menu_link_content\Entity\MenuLinkContent;
use <PERSON><PERSON>al\user\Entity\User;
use Drupal\Core\Url; 
use Drupal\Core\Mail\MailManagerInterface;
// require "vendor/autoload.php";
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;
use Drupal\file\Entity\File;

/**
 * Provides route responses for the Unilevelmlm module.
 */
class FrontendController extends ControllerBase {

  /**
   * Get transaction history for a user.
   *
   * @param int $user_id
   *   The user ID.
   *
   * @return array
   *   An array of transactions.
   */
  private function getTransactionHistory($user_id) {
    $database = \Drupal::database();
    $query = $database->select('wallet_transactions', 'wt');
    $query->fields('wt', ['amount', 'transaction_type', 'timestamp']);
    $query->condition('user_id', $user_id);
    $query->orderBy('timestamp', 'DESC');
    $result = $query->execute();

    $transactions = [];
    while ($record = $result->fetchAssoc()) {
      $transactions[] = [
        'date' => date('Y-m-d H:i:s', $record['timestamp']),
        'type' => $record['transaction_type'],
        'amount' => $record['amount'],
      ];
    }

    return $transactions;
  }
  
  /**
   * Returns a simple page.
   *
   * @return array
   *   A simple renderable array.
   */
  public function ump_registration()
  {    
    
  
      $registration_form = \Drupal::formBuilder()->getForm('Drupal\unilevelmlm\Form\UmpRegistrationForm');        
        
        return [
          '#attached' => array(
            'library' => array(
                'unilevelmlm/unilevelmlm',
            ),
          ),
          $registration_form
      ];
    
      
  }
 
  public function ump_registration_by_referral($id)
  {
    
  
    
      $registration_form = \Drupal::formBuilder()->getForm('Drupal\unilevelmlm\Form\UmpRegistrationForm',$id);        
        return [
          '#attached' => array(
            'library' => array(
                'unilevelmlm/unilevelmlm',
            ),
          ),
          $registration_form
      ];
          
  }

  public function ump_dashboard()
  {
    
      $current_user = \Drupal::currentUser();   
      $user_id =$current_user->id();           
      $user_name=ump_get_child_user_name_by_id($user_id);
     $data_menu_array=get_menu_array();
     $earning_week_percent = 0;
     $withdrawal_week_percent=0;
     $user_week_percent=0;
     $logout=\Drupal::request()->getSchemeAndHttpHost().'/user/logout';
     $profile_image=ump_get_profile_picture($user_id);
     $total_earning = get_total_earning($user_id);
     $total_earning_week = get_total_earing_week($user_id);
     if(!empty($total_earning_week) && !empty($total_earning)) {
        $earning_week_percent = ($total_earning_week / $total_earning) * 100;
      } 
      $total_earning_week=($total_earning_week>0)?$total_earning_week:'0'; 
      $total_withdrawal=get_withdrawa_amount_earning($user_id);   
      $total_withdrawal_week=get_withdrawa_week($user_id);
      if(!empty($total_withdrawal) && !empty($total_withdrawal_week)) {
        $withdrawal_week_percent = ($total_withdrawal_week / $total_withdrawal) * 100;
      } 
      $total_withdrawal=($total_withdrawal>0)?$total_withdrawal:'0';

      $total_downlines=get_total_downlines($user_id);   
      $total_downlines_week=get_total_downlines_week($user_id);
      if(!empty($total_downlines) && !empty($total_downlines_week)) {
        $user_week_percent = ($total_downlines_week / $total_downlines) * 100;
      } 
      $total_downlines=($total_downlines>0)?$total_downlines:'0';   
    
      
    
      // Get wallet balance and transaction history.
      $wallet_balance = $this->balance()->getContent();
      $transaction_history = $this->getTransactionHistory($user_id);

      return array(
        '#theme' => 'unilevelmlm_dashboard_template',
        '#menu_array'=>$data_menu_array,
        '#current_user_name'=>$user_name,
        '#total_earning_week'=>$total_earning_week,
        '#earning_week_percent'=>number_format($earning_week_percent,0),
        '#wallet_balance' => $wallet_balance,
        '#transaction_history' => $transaction_history,
        '#total_withdrawal'=>$total_withdrawal,
        '#withdrawal_week_percent'=>$withdrawal_week_percent,
        '#total_downlines'=> $total_downlines,
        '#user_week_percent'=>$user_week_percent,
        '#logout'=>$logout,
        '#profile_image'=>$profile_image,
        '#attached' => array(
          'library' => array(
            'unilevelmlm/unilevelmlm',
          ),
        ),
      );
    
  }
    public function payout_detail(){
    $current_user = \Drupal::currentUser();
    $user_id =$current_user->id();
    
      $user_name=ump_get_child_user_name_by_id($user_id);
      $logout=\Drupal::request()->getSchemeAndHttpHost().'/user/logout';
      $profile_image=ump_get_profile_picture($user_id);
      $data_menu_array=get_menu_array();
      $payout_detail=array()     ;
      $payout_detail=ump_get_payout_detail_list($user_id);
      return array(
        '#theme' => 'unilevelmlm_frontpayout_template',
        '#menu_array'=>$data_menu_array,
        '#current_user_name'=>$user_name,
        '#payout_detail'=>$payout_detail,
        '#logout'=>$logout,
        '#profile_image'=>$profile_image 
      );
    
  }
    public function join_commission(){
                
        $current_user = \Drupal::currentUser();
        $user_id =$current_user->id();
        $logout=$GLOBALS['base_url'].'/user/logout';
        $profile_image=ump_get_profile_picture($user_id);
        $user_name=ump_get_child_user_name_by_id($user_id);
        $data_menu_array=get_menu_array(); 
        $join_comm=array();
        $join_commissions=ump_join_commission_by_userId($user_id);
        $i=0;
        if(!empty($join_commissions))
        {
        
          foreach($join_commissions as $value) 
          {
          $join_comm[$i]->id=$value->id;       
          $join_comm[$i]->user_name=ump_get_child_user_name_by_id($value->parent_id);
          $join_comm[$i]->child_name=ump_get_child_user_name_by_id($value->child_id);
          $join_comm[$i]->payout_id=$value->payout_id;
          $join_comm[$i]->date_notified=date_format(date_create($value->date_notified), ' jS M Y'); 
          $join_comm[$i]->comm_type=$value->comm_type;
          $join_comm[$i]->amount=$value->amount;
          $i++;
        }    
      } 
        
      return array(
        '#theme' => 'unilevelmlm_joincomm_template',
        '#menu_array'=>$data_menu_array,
        '#current_user_name'=>$user_name,
        '#join_commission'=>$join_comm,
        '#logout'=>$logout,
        '#profile_image'=>$profile_image 
      );
    
  }
  function ref_commission(){
    $current_user = \Drupal::currentUser();
    $user_id =$current_user->id();
    
      $user_name=ump_get_child_user_name_by_id($user_id);
      $data_menu_array=get_menu_array();
      $ref_comm=array();
      $logout=\Drupal::request()->getSchemeAndHttpHost().'/user/logout';
      $profile_image=ump_get_profile_picture($user_id);
      $ref_commission=ump_ref_commission_by_UserId($user_id);
      $i=0;
      if(!empty($ref_commission))
      {
        foreach($ref_commission as $value) 
        {
          $ref_comm[$i]->id=$value->id;
          $ref_comm[$i]->parent_id=$value->parent_id;
          $ref_comm[$i]->child_id=$value->child_id;
          $ref_comm[$i]->user_name=ump_get_child_user_name_by_id($value->parent_id);
          $ref_comm[$i]->child_name=ump_get_child_user_name_by_id($value->child_id);
          $ref_comm[$i]->payout_id=$value->payout_id;
          $ref_comm[$i]->date_notified=date_format(date_create($value->date_notified), ' jS M Y'); 
          $ref_comm[$i]->comm_type=$value->comm_type;
          $ref_comm[$i]->amount=$value->amount;
          $i++;
        }
      }
      return array(
        '#theme' => 'unilevelmlm_refcomm_template',
        '#menu_array'=>$data_menu_array,
        '#current_user_name'=>$user_name,
        '#ref_commission'=>$ref_comm,
        '#logout'=>$logout,
        '#profile_image'=>$profile_image 
      );
    
  }
  
  function account_detail(){    
    $current_user = \Drupal::currentUser();         
    $user_id =$current_user->id();
    
      $user_name=ump_get_child_user_name_by_id($user_id);
      $user_name=!empty($user_name)?$user_name:'';
      $data_menu_array=get_menu_array();
      $user_info=array();
      $logout=\Drupal::request()->getSchemeAndHttpHost().'/user/logout';
      $profile_image=ump_get_profile_picture($user_id);
      $user=ump_getUserInfoByuserid($user_id);
      $i=0;
      if(!empty($user)){
        foreach($user as $value) 
        {
          $user_info[$i]->user_id=$value->user_id;      
          $user_info[$i]->user_email=$current_user->getEmail();
          $user_info[$i]->join_date=$value->creation_date;
          $user_info[$i]->user_name=ump_get_child_user_name_by_id($value->user_id);
          $user_info[$i]->parent_key = $value->parent_key;
          $user_info[$i]->sponsor_key = $value->sponsor_key;
          $user_info[$i]->payment_date=date_format(date_create($value->date_notified), ' jS M Y'); 
          $i++;     
        }    
      }
        return array(
        '#theme' => 'unilevelmlm_personalinfo_template',
        '#menu_array'=>$data_menu_array,
        '#current_user_name'=>$user_name,
        '#user_info'=>$user_info,
        '#logout'=>$logout,
        '#profile_image'=>$profile_image 
      );
    
  }

  function bank_detail(){

    
    $current_user = \Drupal::currentUser();         
    $user_id =$current_user->id();
    
      $user_name=ump_get_child_user_name_by_id($user_id);
      $data_menu_array=get_menu_array();
      $user_bank_details=array()   ;
      $logout=\Drupal::request()->getSchemeAndHttpHost().'/user/logout';
      $profile_image=ump_get_profile_picture($user_id);
      $user_bank_details=ump_get_user_bank_details($user_id);
      return array(
        '#theme' => 'unilevelmlm_bank_template',
        '#menu_array'=>$data_menu_array,
        '#current_user_name'=>$user_name,
        '#user_bank_details'=>$user_bank_details,
        '#logout'=>$logout,
        '#profile_image'=>$profile_image 
      ); 
    
  }

  function add_bank_detail(){    
    $current_user = \Drupal::currentUser();    
    $account_holder_name = $_POST['ump-bank-account-holder'];
    $account_number = $_POST['ump-bank-account-number'];
    $bank_name = $_POST['ump-bank-bank-name'];
    $branch = $_POST['ump-bank-branch-name'];
    $ifsc_code = $_POST['ump-bank-ifsc-code'];
    $contact_no = $_POST['ump-contact-number'];
    insert_user_bank_details($account_holder_name,$account_number,$bank_name,$branch,$ifsc_code,$contact_no,$current_user->id());  
    

  }

  function ump_withdrawal_amount(){
    $current_user = \Drupal::currentUser();         
    $user_id =$current_user->id();
    
      $user_name=ump_get_child_user_name_by_id($user_id);
      $data_menu_array=get_menu_array();
      $logout=\Drupal::request()->getSchemeAndHttpHost().'/user/logout';
      $profile_image=ump_get_profile_picture($user_id);
      $min_limit = \Drupal::config('unilevelmlm.general')->get('ump_withdrawal_min_limit');
      $max_limit = \Drupal::config('unilevelmlm.general')->get('ump_withdrawal_max_limit');
    
      $total_amount =get_user_total_amount($user_id);    
      $processed_amount =get_user_processed_amount($user_id);
      $pending_amount = get_user_pending_amount($user_id);
    
      $remaining_balance = $total_amount - $processed_amount; 
      return array(
        '#theme' => 'unilevelmlm_withdrawalamount_template',
        'menu_array'=>$data_menu_array,
        'current_user_name'=>$user_name,
        'total_amount'=>$total_amount,
        'processed_amount'=>$processed_amount>0?$processed_amount:'0.00',
        'pending_amount'=>$pending_amount>0?$pending_amount:'0.00',
        'remaining_balance'=>number_format($remaining_balance,2),
        'min_limit'=>!empty($min_limit)?$min_limit:0,
        'max_limit'=>!empty($max_limit)?$max_limit:0,
        'logout'=>$logout,
        'profile_image'=>$profile_image 
      );
    
  }

  function ump_withdrawal_amount_by_user(){
    $current_user = \Drupal::currentUser();         
    $user_id =$current_user->id();        
    ump_withdrwal_amount_request_function($_POST['amount'], $user_id);    
  }
 

  function ump_downlines(){
    $current_user = \Drupal::currentUser();         
    $user_id =$current_user->id();
    
      $user_name=ump_get_child_user_name_by_id($user_id);
      $data_menu_array=get_menu_array();
      $logout=\Drupal::request()->getSchemeAndHttpHost().'/user/logout';
      $profile_image=ump_get_profile_picture($user_id);
      return [
       
        '#attached' => array(
            'library' => array(
                'unilevelmlm/unilevelmlm',
            ),
          ),         
    
        '#theme' => 'unilevelmlm_frontgenealogy_template',
        '#menu_array'=>$data_menu_array,
        '#current_user_name'=>$user_name,
        '#logout'=>$logout,
        '#profile_image'=>$profile_image 

      ];
    
  }

  function ump_join(){
      $join_form = \Drupal::formBuilder()->getForm('Drupal\unilevelmlm\Form\UmpJoinForm');
    return [$join_form];
    
  }

  /**
   * Generates a QR code for a product page.
   *
   * @param int $product_id
   *   The product ID.
   *
   * @return string
   *   The QR code image.
   */
  public function generate_product_qr_code($product_id) {
    // Generate the URL for the product page.
    $product_url = Url::fromRoute('entity.node.canonical', ['node' => $product_id], ['absolute' => TRUE])->toString();

    // Get the product image URL.
    $node = \Drupal\node\Entity\Node::load($product_id);
    $product_image_url = '';
    if ($node && $node->hasField('field_product_image') && !$node->get('field_product_image')->isEmpty()) {
      $file_uri = $node->get('field_product_image')->first()->getValue()['uri'];
      $product_image_url = file_url_transform_relative(file_create_url($file_uri));
    }

    // Generate the QR code image using qrcode.js.
    $qr_code_image = '<img src="' . $product_image_url . '" width="50" height="50"><img src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=' . $product_url . '" alt="QR Code">';

    return $qr_code_image;
  }
   
/**
   * Uploads a product image.
   *
   * @param array $form
   *   The form.
   * @param \Drupal\Core\Form\FormStateInterface $form_state) {
    $validators = array(
      'file_validate_extensions' => array('gif png jpg jpeg'),
    );
    //Get the array of files that were uploaded.
    $files = $form_state->getValues();
    if (!empty($files['product_image'])) {
      $file = File::load($files['product_image'][0]);
      $file->setPermanent();
      $file->save();
      return $file->getFileUri();
    }
    return '';
  }
  
    /**
   * Deposit funds to the user's wallet.
   *
   * @return array
   *   A JSON response.
   */
  public function deposit() {
    // Check if the user is logged in.
    if (!\Drupal::currentUser()->isAuthenticated()) {
      $response = [
        'status' => 'error',
        'message' => 'User not authenticated.',
      ];
      return new \Symfony\Component\HttpFoundation\JsonResponse($response, 403);
    }

    // Check if the user has the necessary permissions.
    if (!user_has_permission('deposit')) {
      $response = [
        'status' => 'error',
        'message' => 'User not authorized to deposit funds.',
      ];
      return new \Symfony\Component\HttpFoundation\JsonResponse($response, 403);
    }

    $current_user = \Drupal::currentUser();
    $user_id = $current_user->id();

    // Get the request body.
    $request = \Drupal::request();
    $content = $request->getContent();
    $data = json_decode($content, TRUE);

    // Validate the amount.
    if (!isset($data['amount']) || !is_numeric($data['amount']) || $data['amount'] <= 0) {
      $response = [
        'status' => 'error',
        'message' => 'Invalid amount.',
      ];
      return new \Symfony\Component\HttpFoundation\JsonResponse($response);
    }
    $amount = $data['amount'];

    // Get the database connection.
    $database = \Drupal::database();

    // Check if the user_wallets table exists.
    if (!$database->schema()->tableExists('user_wallets')) {
      // Create the user_wallets table.
      $database->schema()->createTable('user_wallets', [
        'fields' => [
          'user_id' => [
            'type' => 'int',
            'unsigned' => TRUE,
            'not null' => TRUE,
            'description' => 'The user ID.',
          ],
          'balance' => [
            'type' => 'numeric',
            'precision' => 10,
            'scale' => 2,
            'not null' => TRUE,
            'default' => 0,
            'description' => 'The user\'s wallet balance.',
          ],
        ],
        'primary key' => ['user_id'],
        'indexes' => [
          'user_id' => ['user_id'],
        ],
        'description' => 'Stores user wallet balances.',
      ]);
    }

    // Check if the wallet_transactions table exists.
    if (!$database->schema()->tableExists('wallet_transactions')) {
      // Create the wallet_transactions table.
      $database->schema()->createTable('wallet_transactions', [
        'fields' => [
          'id' => [
            'type' => 'serial',
            'not null' => TRUE,
            'description' => 'The transaction ID.',
          ],
          'user_id' => [
            'type' => 'int',
            'unsigned' => TRUE,
            'not null' => TRUE,
            'description' => 'The user ID.',
          ],
          'amount' => [
            'type' => 'numeric',
            'precision' => 10,
            'scale' => 2,
            'not null' => TRUE,
            'description' => 'The transaction amount.',
          ],
          'transaction_type' => [
            'type' => 'varchar',
            'length' => 255,
            'not null' => TRUE,
            'description' => 'The transaction type (e.g., deposit, withdrawal, transfer).',
          ],
          'timestamp' => [
            'type' => 'int',
            'not null' => TRUE,
            'description' => 'The transaction timestamp.',
          ],
        ],
        'primary key' => ['id'],
        'indexes' => [
          'user_id' => ['user_id'],
        ],
        'description' => 'Stores wallet transactions.',
      ]);
    }

    // Update the user's wallet balance.
    $database->merge('user_wallets')
      ->key(['user_id' => $user_id])
      ->fields([
        'user_id' => $user_id,
        'balance' => $amount + $current_balance,
      ])
      ->execute();

    // Log the transaction.
    $database->insert('wallet_transactions')
      ->fields([
        'user_id' => $user_id,
        'amount' => $amount,
        'transaction_type' => 'deposit',
        'timestamp' => time(),
      ])
      ->execute();

    $response = [
      'status' => 'success',
      'message' => 'Funds deposited successfully.',
    ];
    return new \Symfony\Component\HttpFoundation\JsonResponse($response);
  }

  public function withdraw() {
    // TODO: Implement user authentication and authorization.
    $current_user = \Drupal::currentUser();
    $user_id = $current_user->id();

    // Get the request body.
    $request = \Drupal::request();
    $content = $request->getContent();
    $data = json_decode($content, TRUE);

    // Validate the amount.
    if (!isset($data['amount']) || !is_numeric($data['amount']) || $data['amount'] <= 0) {
      $response = [
        'status' => 'error',
        'message' => 'Invalid amount.',
      ];
      return new \Symfony\Component\HttpFoundation\JsonResponse($response);
    }
    $amount = $data['amount'];

    // Get the database connection.
    $database = \Drupal::database();

    // Get the user's current wallet balance.
    $result = $database->select('user_wallets', 'uw')
      ->fields('uw', ['balance'])
      ->condition('user_id', $user_id)
      ->execute()
      ->fetchAssoc();

    if (!$result) {
      $response = [
        'status' => 'error',
        'message' => 'Wallet not found.',
      ];
      return new \Symfony\Component\HttpFoundation\JsonResponse($response);
    }

    $current_balance = $result['balance'];

    // Check if the user has sufficient balance.
    if ($amount > $current_balance) {
      $response = [
        'status' => 'error',
        'message' => 'Insufficient balance.',
      ];
      return new \Symfony\Component\HttpFoundation\JsonResponse($response);
    }

    // Update the user's wallet balance.
    $new_balance = $current_balance - $amount;
    $database->update('user_wallets')
      ->fields(['balance' => $new_balance])
      ->condition('user_id', $user_id)
      ->execute();

    // Log the transaction.
    $database->insert('wallet_transactions')
      ->fields([
        'user_id' => $user_id,
        'amount' => $amount,
        'transaction_type' => 'withdrawal',
        'timestamp' => time(),
      ])
      ->execute();

    $response = [
      'status' => 'success',
      'message' => 'Funds withdrawn successfully.',
    ];
    return new \Symfony\Component\HttpFoundation\JsonResponse($response);
  }

  public function transfer() {
    // TODO: Implement user authentication and authorization.
    $current_user = \Drupal::currentUser();
    $user_id = $current_user->id();

    // Get the request body.
    $request = \Drupal::request();
    $content = $request->getContent();
    $data = json_decode($content, TRUE);

    // Validate the recipient.
    if (!isset($data['recipient']) || !is_numeric($data['recipient'])) {
      $response = [
        'status' => 'error',
        'message' => 'Invalid recipient.',
      ];
      return new \Symfony\Component\HttpFoundation\JsonResponse($response);
    }
    $recipient_id = $data['recipient'];

    // Validate the amount.
    if (!isset($data['amount']) || !is_numeric($data['amount']) || $data['amount'] <= 0) {
      $response = [
        'status' => 'error',
        'message' => 'Invalid amount.',
      ];
      return new \Symfony\Component\HttpFoundation\JsonResponse($response);
    }
    $amount = $data['amount'];

    // Get the database connection.
    $database = \Drupal::database();

    // Get the user's current wallet balance.
    $result = $database->select('user_wallets', 'uw')
      ->fields('uw', ['balance'])
      ->condition('user_id', $user_id)
      ->execute()
      ->fetchAssoc();

    if (!$result) {
      $response = [
        'status' => 'error',
        'message' => 'Wallet not found.',
      ];
      return new \Symfony\Component\HttpFoundation\JsonResponse($response);
    }

    $current_balance = $result['balance'];

    // Check if the user has sufficient balance.
    if ($amount > $current_balance) {
      $response = [
        'status' => 'error',
        'message' => 'Insufficient balance.',
      ];
      return new \Symfony\Component\HttpFoundation\JsonResponse($response);
    }

    // Get the recipient's current wallet balance.
    $result = $database->select('user_wallets', 'uw')
      ->fields('uw', ['balance'])
      ->condition('user_id', $recipient_id)
      ->execute()
      ->fetchAssoc();

    if (!$result) {
      $response = [
        'status' => 'error',
        'message' => 'Recipient wallet not found.',
      ];
      return new \Symfony\Component\HttpFoundation\JsonResponse($response);
    }

    $recipient_balance = $result['balance'];

    // Update the user's wallet balance.
    $new_balance = $current_balance - $amount;
    $database->update('user_wallets')
      ->fields(['balance' => $new_balance])
      ->condition('user_id', $user_id)
      ->execute();

    // Update the recipient's wallet balance.
    $new_recipient_balance = $recipient_balance + $amount;
    $database->update('user_wallets')
      ->fields(['balance' => $new_recipient_balance])
      ->condition('user_id', $recipient_id)
      ->execute();

    // Log the transaction.
    $database->insert('wallet_transactions')
      ->fields([
        'user_id' => $user_id,
        'amount' => $amount,
        'transaction_type' => 'transfer',
        'timestamp' => time(),
      ])
      ->execute();

    $response = [
      'status' => 'success',
      'message' => 'Funds transferred successfully.',
    ];
    return new \Symfony\Component\HttpFoundation\JsonResponse($response);
  }

  public function balance() {
    // TODO: Implement user authentication and authorization.
    $current_user = \Drupal::currentUser();
    $user_id = $current_user->id();

    // Get the database connection.
    $database = \Drupal::database();

    // Get the user's current wallet balance.
    $result = $database->select('user_wallets', 'uw')
      ->fields('uw', ['balance'])
      ->condition('user_id', $user_id)
      ->execute()
      ->fetchAssoc();

    if (!$result) {
      $response = [
        'status' => 'error',
        'message' => 'Wallet not found.',
      ];
      return new \Symfony\Component\HttpFoundation\JsonResponse($response);
    }

    $current_balance = $result['balance'];

    $response = [
      'status' => 'success',
      'balance' => $current_balance,
    ];
    return new \Symfony\Component\HttpFoundation\JsonResponse($response);
  }

}