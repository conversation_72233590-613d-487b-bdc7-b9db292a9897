{#
/**
 * @file
 * Default theme implementation of a media library item.
 *
 * This is used when displaying selected media items, either in the field
 * widget or in the "Additional selected media" area when adding new
 * media items in the media library modal dialog.
 *
 * Available variables:
 * - attributes: HTML attributes for the containing element.
 * - content: The content of the media library item, plus any additional
 *   fields or elements surrounding it.
 *
 * @see template_preprocess_media_library_item()
 *
 * @ingroup themeable
 */
#}
{%
  set classes = [
    'media-library-item',
    'media-library-item--grid',
  ]
%}
<div{{ attributes.addClass(classes) }}>
  {{ content }}
</div>
