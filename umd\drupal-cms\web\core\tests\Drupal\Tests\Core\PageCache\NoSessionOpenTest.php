<?php

declare(strict_types=1);

namespace Drupal\Tests\Core\PageCache;

use <PERSON><PERSON>al\Core\PageCache\RequestPolicy\NoSessionOpen;
use Drupal\Core\PageCache\RequestPolicyInterface;
use Drupal\Tests\UnitTestCase;
use Symfony\Component\HttpFoundation\Request;

/**
 * @coversDefaultClass \Drupal\Core\PageCache\RequestPolicy\NoSessionOpen
 * @group PageCache
 */
class NoSessionOpenTest extends UnitTestCase {

  /**
   * The session configuration.
   *
   * @var \Drupal\Core\Session\SessionConfigurationInterface|\PHPUnit\Framework\MockObject\MockObject
   */
  protected $sessionConfiguration;

  /**
   * The request policy under test.
   *
   * @var \Drupal\Core\PageCache\RequestPolicy\NoSessionOpen
   */
  protected $policy;

  /**
   * {@inheritdoc}
   */
  protected function setUp(): void {
    parent::setUp();

    $this->sessionConfiguration = $this->createMock('Drupal\Core\Session\SessionConfigurationInterface');
    $this->policy = new NoSessionOpen($this->sessionConfiguration);
  }

  /**
   * Asserts that caching is allowed unless there is a session cookie present.
   *
   * @covers ::check
   */
  public function testNoAllowUnlessSessionCookiePresent(): void {
    $request_without_session = new Request();
    $request_with_session = Request::create('/', 'GET', [], ['some-session-name' => 'some-session-id']);

    $this->sessionConfiguration->expects($this->exactly(2))
      ->method('hasSession')
      ->willReturnMap([
        [$request_without_session, FALSE],
        [$request_with_session, TRUE],
      ]);

    $result = $this->policy->check($request_without_session);
    $this->assertSame(RequestPolicyInterface::ALLOW, $result);

    $result = $this->policy->check($request_with_session);
    $this->assertNull($result);
  }

}
