langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_tags
    - node.type.event
    - taxonomy.vocabulary.tags
id: node.event.field_tags
field_name: field_tags
entity_type: node
bundle: event
label: Tags
description: 'Include tags for relevant topics.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      tags: tags
    sort:
      field: _none
      direction: asc
    auto_create: true
    auto_create_bundle: ''
field_type: entity_reference
