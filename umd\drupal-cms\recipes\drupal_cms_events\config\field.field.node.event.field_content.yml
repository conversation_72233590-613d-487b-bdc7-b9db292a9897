langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_content
    - filter.format.content_format
    - node.type.event
  module:
    - text
id: node.event.field_content
field_name: field_content
entity_type: node
bundle: event
label: Content
description: 'The content of this page.'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  allowed_formats:
    - content_format
field_type: text_long
