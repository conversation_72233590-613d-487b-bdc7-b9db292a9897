{#
/**
 * @file
 * Theme override of a datetime form wrapper.
 *
 * @see template_preprocess_form_element()
 */
#}
{%
  set title_classes = [
    'form-item__label',
    required ? 'js-form-required',
    required ? 'form-required',
    errors ? 'has-error',
  ]
%}
<div class="form-item form-datetime-wrapper">
{% if title %}
  <h4{{ title_attributes.addClass(title_classes) }}>{{ title }}</h4>
{% endif %}
{{ content }}
{% if errors %}
  <div class="form-item__error-message">
    {{ errors }}
  </div>
{% endif %}
{% if description %}
  <div{{ description_attributes.addClass('form-item__description') }}>
    {{ description }}
  </div>
{% endif %}
</div>
