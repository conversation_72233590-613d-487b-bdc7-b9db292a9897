{% include '@unilevelmlm/unilevelmlm-sidebar-template.html.twig' %}
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.6.3/css/font-awesome.css'>
{{ attach_library('unilevelmlm/unilevel-front-genealogy') }}
<div id="full-container" class="let-container">
    <div class="">
        <button class="btn-action  btn-fullscreen let-text-success icon-color"
            onclick="params.funcs.toggleFullScreen()"><i class="fa fa-arrows-alt" aria-hidden="true"></i></button>
        <button class=" btn-action  btn-search let-text-success icon-color" onclick="params.funcs.search()"><i
                class="fa fa-search" aria-hidden="true"></i></button>
        <button class="btn-action  btn-show-my-self let-text-success icon-color"
            onclick="params.funcs.showMySelf()"><span class='icon'> <i class="fa fa-user"
                    aria-hidden="true"></i></span></button>
        <button class="btn-action  let-text-success icon-color" onclick="params.funcs.expandAll()"><i
                class="fa fa-plus-circle" aria-hidden="true"></i></button>
        <button class="btn-action  let-text-success icon-color" onclick="params.funcs.collapseAll()"><i
                class="fa fa-minus-circle" aria-hidden="true"></i>
        </button>
        <button class=" btn-action  btn-back let-text-success icon-color" onclick="params.funcs.back()"><i
                class="fa fa-arrow-left" aria-hidden="true"></i></button>
    </div>

    <div class="user-search-box">
        <div class="input-box">
            <div class="close-button-wrapper icon-color"><i onclick="params.funcs.closeSearchBox()" class="fa fa-times"
                    aria-hidden="true"></i></div>
            <div class="input-wrapper">
                <input type="text" class="search-input" placeholder="{{'Search' |t}}" />
                <div class="input-bottom-placeholder">{{ 'By Username, Sponsor,  Userid, position' |t}}</div>
            </div>
            <div>
            </div>
        </div>
        <div class="result-box">
            <div class="result-header">{{'RESULTS' |t}} </div>
            <div class="result-list">
                <div class="let-buffer"></div>
            </div>
        </div>
    </div>
    <div id="svgChart" class="let-container let-col-md-12"></div>
</div>
{% include '@unilevelmlm/unilevelmlm-footer-template.html.twig' %}