{{attach_library('unilevelmlm/unilevelmlm')}}
<div class="content_style">
    <div class="form-row let-border">
        <div class="let-col-md-12 let-col-sm-12 let-col-xs-12 let-text-center"><br />
            <h4><strong>{{'Commission Distribution Section' |t}}</strong></h4>
        </div><br>
        <div class="let-loader-layer" id="run_distribute_loader">
            <div class="let-pic-loader"></div>
        </div>

        <table class="let-table-bordered  let-table-striped  ump_width_table let-border"
            id="ump_distibute_commission_table">
            <thead class="let-thead-inverse ump-bg let-text-center">
                <tr class="let-text-center">
                    <th class="let-text-center">{{'User Id' |t}}</th>
                    <th class="let-text-center">{{'User Name' |t}}</th>
                    <th class="let-text-center">{{'Join Commission' |t}}</th>
                    <th class="let-text-center">{{'Referral Commission' |t}}</th>                     
                    <th class="let-text-center">{{'Total Amount' |t}}</th>
                </tr>
            </thead>
            {% if (join_commission is not empty) or (referral_commision is not empty)  %}

            <tbody>
                {% for user in ump_user_name %}
                {% set total_row_amount = 0 %}
                <tr class="let-text-center">
                    <td class="let-align-middle">{{user.user_id}}</td>
                    <td class="let-align-middle"> {{user.user_name}}</td>
                    <td class="let-align-middle">
                        {% if join_commission is not empty %}
                        <table class="let-table let-table-bordered  let-table-striped let-table-sm let-m-0">
                            <thead class="let-bg-">
                                {% for ump_join in join_commission %}
                                {% if (user.user_id == ump_join.child_id) %}
                                <tr class="let-text-center let-text-black">
                                    <th class="let-text-white let-bg-secondary"> {{'Childs Name' |t}} </th>
                                    <th class="let-text-white let-bg-secondary"> {{'Amount' |t}} </th>
                                </tr>
                                {% endif %}
                                {% endfor %}
                            </thead>
                            <tbody>
                                {% for ump_join in join_commission %}
                                {% if (user.user_id == ump_join.child_id) %}
                                {% set total_row_amount = total_row_amount+ump_join.amount %}
                                <tr>
                                    <td>{{ump_join.child_name}}</td>
                                    <td>{{ump_join.amount}}</td>
                                </tr>
                                {% endif %}
                                {% endfor %}
                            </tbody>
                        </table>
                        {% endif %}
                    </td>

                    <td class="let-align-middle">
                        {% if referral_commision is not empty %}
                        {% for ump_referral in referral_commision %}
                        <table class="let-table let-table-bordered  let-table-striped let-table-sm let-m-0">
                            <thead class="let-bg-">
                                {% if (user.user_id == ump_referral.parent_id) %}
                                <tr class="let-text-center let-text-white">
                                    <th class="let-text-white let-bg-secondary"> {{'Childs Name' |t}} </th>
                                    <th class="let-text-white let-bg-secondary"> {{'Amount' |t}} </th>
                                </tr>
                                {% endif %}
                                
                            </thead>
                            <tbody>
                                
                                {% if (user.user_id == ump_referral.parent_id) %}
                                {% set total_row_amount = total_row_amount+ump_referral.amount %}
                                <tr>
                                    <td>{{ump_referral.child_name}}</td>
                                    <td>{{ump_referral.amount}}</td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                        {% endfor %}
                         
                        {% endif %}
                    </td>                       

                    <td class="let-align-middle">{{total_row_amount|number_format(2, '.', ',')}}</td>

                </tr>
                {% endfor %}
            </tbody>
            {% endif %}

            <tfoot>
                <tr class=" ump-bg let-text-dark let-text-center">
                    <td class="let-text-center" colspan="2">{{'Total' |t}}</td>
                    <th class="let-text-center">{{join_total_amount}}</th>
                    <th class="let-text-center">{{ref_total_amount}}</th>                    
                    <th class="let-text-center">{{(join_total_amount + ref_total_amount)|number_format(2, '.', ',')}}</th>

                </tr>
                <tr class="ump-bg let-text-white let-text-center ">
                    <td colspan="8" class="let-text-center ump-bg let-py-2">
                        <button class="let-btn  let-btn-primary" onclick='ump_distribute_commission();'>{{'Distribute
                            Commission' |t}} </button>
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>
</div>
<label class="let-switch">
  <input type="checkbox" id="theme-switcher">
  <span class="let-slider let-round"></span>
</label>

<script>
  (function ($) {
    $(document).ready(function () {
      var themeSwitcher = $('#theme-switcher');
      var darkTheme = 'css/unilevelmlm-dark.css';
      var lightTheme = 'css/unilevelmlm.css'; // Or the default theme

      // Function to enable dark theme
      function enableDarkTheme() {
        $('link[href="' + lightTheme + '"]').attr('href', darkTheme);
        localStorage.setItem('theme', 'dark');
      }

      // Function to enable light theme
      function enableLightTheme() {
        $('link[href="' + darkTheme + '"]').attr('href', lightTheme);
        localStorage.setItem('theme', 'light');
      }

      // Check local storage for saved theme
      var savedTheme = localStorage.getItem('theme');
      if (savedTheme === 'dark') {
        themeSwitcher.prop('checked', true);
        enableDarkTheme();
      }

      // Theme switcher functionality
      themeSwitcher.change(function () {
        if (this.checked) {
          enableDarkTheme();
        } else {
          enableLightTheme();
        }
      });
    });
  })(jQuery);
</script>