langcode: en
status: true
dependencies:
  config:
    - field.field.node.event.field_content
    - field.field.node.event.field_description
    - field.field.node.event.field_event__date
    - field.field.node.event.field_event__file
    - field.field.node.event.field_event__link
    - field.field.node.event.field_event__location_address
    - field.field.node.event.field_event__location_name
    - field.field.node.event.field_featured_image
    - field.field.node.event.field_geofield
    - field.field.node.event.field_tags
    - node.type.event
  module:
    - address
    - date_augmenter
    - layout_builder
    - leaflet
    - link
    - smart_date
    - text
    - user
third_party_settings:
  layout_builder:
    enabled: true
    allow_custom: false
    sections:
      -
        layout_id: layout_onecol
        layout_settings:
          label: ''
        components:
          f5b85d4d-8778-4f04-b8dc-10a8bc93a0da:
            uuid: f5b85d4d-8778-4f04-b8dc-10a8bc93a0da
            region: content
            configuration:
              id: 'extra_field_block:node:event:content_moderation_control'
              label_display: '0'
              context_mapping:
                entity: layout_builder.entity
            weight: 0
            additional: {  }
          9e37902f-f63b-4b61-8d77-5150c2410269:
            uuid: 9e37902f-f63b-4b61-8d77-5150c2410269
            region: content
            configuration:
              id: 'field_block:node:event:field_featured_image'
              label_display: '0'
              context_mapping:
                entity: layout_builder.entity
              formatter:
                type: entity_reference_entity_view
                label: hidden
                settings:
                  view_mode: hero
                  link: false
                third_party_settings: {  }
            weight: 1
            additional: {  }
          c871a72e-ca57-411e-958c-539fec3529e1:
            uuid: c871a72e-ca57-411e-958c-539fec3529e1
            region: content
            configuration:
              id: 'field_block:node:event:field_event__date'
              label_display: '0'
              context_mapping:
                entity: layout_builder.entity
              formatter:
                type: smartdate_default
                label: hidden
                settings:
                  timezone_override: ''
                  format_type: medium
                  format: default
                  force_chronological: false
                  add_classes: false
                  time_wrapper: true
                  localize: false
                  parts:
                    start: start
                    end: end
                    duration: '0'
                  duration:
                    separator: ' | '
                    unit: ''
                    decimals: 2
                    suffix: h
                third_party_settings:
                  date_augmenter:
                    instances:
                      status:
                        addtocal: false
                      weights:
                        order:
                          addtocal:
                            weight: 0
                      settings:
                        addtocal:
                          label: 'Add to calendar'
                          event_title: ''
                          location: ''
                          description: ''
                          retain_spacing: false
                          icons: true
                          max_desc: 60
                          ellipsis: true
                          past_events: false
                          target: ''
                          ignore_timezone_if_UTC: true
                      augmenter_settings:
                        fields__field_event__date__settings_edit_form__third_party_settings__date_augmenter__instances__augmenter_settings__active_tab: ''
            weight: 2
            additional: {  }
          b7c7acc3-674f-4137-94ff-200371a54d3c:
            uuid: b7c7acc3-674f-4137-94ff-200371a54d3c
            region: content
            configuration:
              id: 'field_block:node:event:field_event__location_name'
              label_display: '0'
              context_mapping:
                entity: layout_builder.entity
              formatter:
                type: string
                label: hidden
                settings:
                  link_to_entity: false
                third_party_settings: {  }
            weight: 3
            additional: {  }
          9158e8c8-7578-48c1-a8f2-659d8e80706e:
            uuid: 9158e8c8-7578-48c1-a8f2-659d8e80706e
            region: content
            configuration:
              id: 'field_block:node:event:field_event__location_address'
              label_display: '0'
              context_mapping:
                entity: layout_builder.entity
              formatter:
                type: address_default
                label: hidden
                settings: {  }
                third_party_settings: {  }
            weight: 4
            additional: {  }
          0561765a-b1af-4ce8-a01e-cd1f75d3e4aa:
            uuid: 0561765a-b1af-4ce8-a01e-cd1f75d3e4aa
            region: content
            configuration:
              id: 'field_block:node:event:field_geofield'
              label_display: '0'
              context_mapping:
                entity: layout_builder.entity
              formatter:
                type: leaflet_formatter_default
                label: hidden
                settings:
                  multiple_map: false
                  leaflet_map: 'OSM Mapnik'
                  height: 280
                  height_unit: px
                  hide_empty_map: true
                  disable_wheel: false
                  gesture_handling: false
                  fitbounds_options: '{"padding":[0,0]}'
                  reset_map:
                    control: false
                    options: '{"position":"topleft","title":"Reset View"}'
                  map_scale:
                    control: false
                    options: '{"position":"bottomright","maxWidth":100,"metric":true,"imperial":false,"updateWhenIdle":false}'
                  locate:
                    control: false
                    options: '{"position":"topright","setView":"untilPanOrZoom","returnToPrevBounds":true,"keepCurrentZoomLevel":true,"strings":{"title":"Locate my position"}}'
                    automatic: false
                  leaflet_tooltip:
                    value: ''
                    options: '{"permanent":false,"direction":"center"}'
                  popup: false
                  popup_content: ''
                  leaflet_popup:
                    control: '0'
                    content: ''
                    options: '{"maxWidth":"300","minWidth":"50","autoPan":true}'
                  map_position:
                    force: false
                    center:
                      lat: 0.0
                      lon: 0.0
                    zoomControlPosition: topleft
                    zoom: 12
                    minZoom: 1
                    maxZoom: 20
                    zoomFiner: 0
                  icon:
                    iconType: marker
                    iconUrl: ''
                    shadowUrl: ''
                    className: ''
                    iconSize:
                      x: ''
                      'y': ''
                    iconAnchor:
                      x: ''
                      'y': ''
                    shadowSize:
                      x: ''
                      'y': ''
                    shadowAnchor:
                      x: ''
                      'y': ''
                    popupAnchor:
                      x: ''
                      'y': ''
                    html: '<div></div>'
                    html_class: leaflet-map-divicon
                    circle_marker_options: '{"radius":100,"color":"red","fillColor":"#f03","fillOpacity":0.5}'
                  leaflet_markercluster:
                    control: false
                    options: '{"spiderfyOnMaxZoom":true,"showCoverageOnHover":true,"removeOutsideVisibleBounds": false}'
                    excluded: ''
                    include_path: false
                  fullscreen:
                    control: false
                    options: '{"position":"topleft","pseudoFullscreen":false}'
                  path: '{"color":"#3388ff","opacity":"1.0","stroke":true,"weight":3,"fill":"depends","fillColor":"*","fillOpacity":"0.2","radius":"6"}'
                  feature_properties:
                    values: ''
                  geocoder:
                    control: false
                    settings:
                      autocomplete:
                        placeholder: 'Search Address'
                        title: 'Search an Address on the Map'
                      position: topright
                      input_size: 20
                      providers:
                        nominatim:
                          weight: 0
                          checked: false
                      min_terms: 4
                      delay: 800
                      zoom: 16
                      popup: false
                      options: ''
                  map_lazy_load:
                    lazy_load: true
                third_party_settings: {  }
            weight: 5
            additional: {  }
          91e4b920-2499-4e9f-8e36-2aa11774306e:
            uuid: 91e4b920-2499-4e9f-8e36-2aa11774306e
            region: content
            configuration:
              id: 'field_block:node:event:field_content'
              label_display: '0'
              context_mapping:
                entity: layout_builder.entity
              formatter:
                type: text_default
                label: hidden
                settings: {  }
                third_party_settings: {  }
            weight: 6
            additional: {  }
          dc70d8af-07f7-45ce-82f6-31b1efecbb8f:
            uuid: dc70d8af-07f7-45ce-82f6-31b1efecbb8f
            region: content
            configuration:
              id: 'field_block:node:event:field_event__link'
              label_display: '0'
              context_mapping:
                entity: layout_builder.entity
              formatter:
                type: link
                label: hidden
                settings:
                  trim_length: 80
                  url_only: false
                  url_plain: false
                  rel: ''
                  target: ''
                third_party_settings: {  }
            weight: 7
            additional: {  }
          d33af099-0948-4a26-ade1-c9c981606e2f:
            uuid: d33af099-0948-4a26-ade1-c9c981606e2f
            region: content
            configuration:
              id: 'field_block:node:event:field_event__file'
              label_display: '0'
              context_mapping:
                entity: layout_builder.entity
              formatter:
                type: entity_reference_entity_view
                label: hidden
                settings:
                  view_mode: default
                  link: false
                third_party_settings: {  }
            weight: 8
            additional: {  }
          a6267978-afeb-4ea9-8d3f-f96aa64d58ab:
            uuid: a6267978-afeb-4ea9-8d3f-f96aa64d58ab
            region: content
            configuration:
              id: 'field_block:node:event:field_tags'
              label_display: '0'
              context_mapping:
                entity: layout_builder.entity
              formatter:
                type: entity_reference_label
                label: inline
                settings:
                  link: true
                third_party_settings: {  }
            weight: 9
            additional: {  }
        third_party_settings: {  }
id: node.event.default
targetEntityType: node
bundle: event
mode: default
content:
  content_moderation_control:
    settings: {  }
    third_party_settings: {  }
    weight: 0
    region: content
  field_content:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 6
    region: content
  field_event__date:
    type: smartdate_default
    label: hidden
    settings:
      timezone_override: ''
      format_type: medium
      format: default
      force_chronological: false
      add_classes: false
      time_wrapper: true
      localize: false
      parts:
        start: start
        end: end
        duration: '0'
      duration:
        separator: ' | '
        unit: ''
        decimals: 2
        suffix: h
    third_party_settings:
      date_augmenter:
        instances:
          status:
            addtocal: false
          weights:
            order:
              addtocal:
                weight: 0
          settings:
            addtocal:
              label: 'Add to calendar'
              event_title: ''
              location: ''
              description: ''
              retain_spacing: false
              icons: true
              max_desc: 60
              ellipsis: true
              past_events: false
              target: ''
              ignore_timezone_if_UTC: true
          augmenter_settings:
            fields__field_event__date__settings_edit_form__third_party_settings__date_augmenter__instances__augmenter_settings__active_tab: ''
    weight: 2
    region: content
  field_event__file:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: default
      link: false
    third_party_settings: {  }
    weight: 8
    region: content
  field_event__link:
    type: link
    label: hidden
    settings:
      trim_length: 80
      url_only: false
      url_plain: false
      rel: ''
      target: ''
    third_party_settings: {  }
    weight: 7
    region: content
  field_event__location_address:
    type: address_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 4
    region: content
  field_event__location_name:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 3
    region: content
  field_featured_image:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: 16_9_wide
      link: false
    third_party_settings: {  }
    weight: 1
    region: content
  field_geofield:
    type: leaflet_formatter_default
    label: hidden
    settings:
      multiple_map: false
      leaflet_map: 'OSM Mapnik'
      height: 400
      height_unit: px
      hide_empty_map: true
      disable_wheel: false
      gesture_handling: false
      fitbounds_options: '{"padding":[0,0]}'
      reset_map:
        control: false
        options: '{"position":"topleft","title":"Reset View"}'
      map_scale:
        control: false
        options: '{"position":"bottomright","maxWidth":100,"metric":true,"imperial":false,"updateWhenIdle":false}'
      locate:
        control: false
        options: '{"position":"topright","setView":"untilPanOrZoom","returnToPrevBounds":true,"keepCurrentZoomLevel":true,"strings":{"title":"Locate my position"}}'
        automatic: false
      leaflet_tooltip:
        value: ''
        options: '{"permanent":false,"direction":"center"}'
      popup: false
      popup_content: ''
      leaflet_popup:
        control: '0'
        content: ''
        options: '{"maxWidth":"300","minWidth":"50","autoPan":true}'
      map_position:
        force: false
        center:
          lat: 0.0
          lon: 0.0
        zoomControlPosition: topleft
        zoom: 14
        minZoom: 1
        maxZoom: 20
        zoomFiner: 0
      icon:
        iconType: marker
        iconUrl: ''
        shadowUrl: ''
        className: ''
        iconSize:
          x: ''
          'y': ''
        iconAnchor:
          x: ''
          'y': ''
        shadowSize:
          x: ''
          'y': ''
        shadowAnchor:
          x: ''
          'y': ''
        popupAnchor:
          x: ''
          'y': ''
        html: '<div></div>'
        html_class: leaflet-map-divicon
        circle_marker_options: '{"radius":100,"color":"red","fillColor":"#f03","fillOpacity":0.5}'
      leaflet_markercluster:
        control: false
        options: '{"spiderfyOnMaxZoom":true,"showCoverageOnHover":true,"removeOutsideVisibleBounds": false}'
        excluded: ''
        include_path: false
      fullscreen:
        control: false
        options: '{"position":"topleft","pseudoFullscreen":false}'
      path: '{"color":"#3388ff","opacity":"1.0","stroke":true,"weight":3,"fill":"depends","fillColor":"*","fillOpacity":"0.2","radius":"6"}'
      feature_properties:
        values: ''
      geocoder:
        control: false
        settings:
          autocomplete:
            placeholder: 'Search Address'
            title: 'Search an Address on the Map'
          position: topright
          input_size: 20
          providers:
            nominatim:
              weight: 0
              checked: false
          min_terms: 4
          delay: 800
          zoom: 16
          popup: false
          options: ''
      map_lazy_load:
        lazy_load: true
    third_party_settings: {  }
    weight: 5
    region: content
  field_tags:
    type: entity_reference_label
    label: inline
    settings:
      link: true
    third_party_settings: {  }
    weight: 9
    region: content
hidden:
  field_description: true
  langcode: true
  links: true
