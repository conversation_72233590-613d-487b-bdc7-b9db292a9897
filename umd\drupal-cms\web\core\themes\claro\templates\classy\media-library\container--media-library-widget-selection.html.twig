{#
/**
 * @file
 * Theme implementation of a wrapper for selected media items.
 *
 * This is used to wrap around the set of media items that are currently
 * selected in the media library widget (not the modal dialog), which may
 * be used for entity reference fields that target media.
 *
 * Available variables:
 * - attributes: HTML attributes for the containing element.
 * - children: The rendered child elements of the container.
 * - has_parent: A flag to indicate that the container has one or more parent
     containers.
 *
 * @see template_preprocess_container()
 *
 * @ingroup themeable
 */
#}
{%
  set classes = [
    has_parent ? 'js-form-wrapper',
    has_parent ? 'form-wrapper',
    'media-library-selection',
  ]
%}
<div{{ attributes.addClass(classes) }}>{{ children }}</div>
