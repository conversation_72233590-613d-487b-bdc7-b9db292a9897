langcode: en
status: true
dependencies:
  enforced:
    config:
      - node.type.blog
  module:
    - node
id: blog_content
label: 'Blog content'
type: 'canonical_entities:node'
pattern: '/blog/[node:created:html_month]/[node:title]'
selection_criteria:
  484783df-5ed0-4f01-bff4-c6497c0d6751:
    id: 'entity_bundle:node'
    negate: false
    uuid: 484783df-5ed0-4f01-bff4-c6497c0d6751
    context_mapping:
      node: node
    bundles:
      blog: blog
selection_logic: and
weight: -5
relationships: {  }
