<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.6.3/css/font-awesome.css'>
<!-- partial:index.partial.html -->
{{ attach_library('unilevelmlm/unilevel-genealogy') }}
{{attach_library('unilevelmlm/unilevelmlm')}}
<div class="let-card shadow let-m-4">
    <div class="let-card-header ump-bg let-py-3">
        <h6 class="let-m-0 let-font-weight-bold ">
            {{'Geonology' |t}}
        </h6>
    </div>
    <!-- {{genealogy}} -->
    <div class="let-card-body">
        <div id="full-container" class="let-container">
            <button class="btn-fullscreen let-text-success" style="color: #4287f5;"
                onclick="params.funcs.toggleFullScreen()"><i class="fa fa-arrows-alt" aria-hidden="true"></i></button>
            <button class=" btn-search let-text-success" style="color: #4287f5;" onclick="params.funcs.search()"><i
                    class="fa fa-search" aria-hidden="true"></i></button>
            <button class="btn-show-my-self let-text-success" style="color: #4287f5;"
                onclick="params.funcs.showMySelf()"><span class='icon'> <i class="fa fa-user"
                        aria-hidden="true"></i></span></button>
            <button class="let-text-success" style="color: #4287f5;" onclick="params.funcs.expandAll()"><i
                    class="fa fa-plus-circle" aria-hidden="true"></i></button>
            <button class="let-text-success" style="color: #4287f5;" onclick="params.funcs.collapseAll()"><i
                    class="fa fa-minus-circle" aria-hidden="true"></i>
            </button>
            <button class=" btn-back let-text-success" style="color: #4287f5;" onclick="params.funcs.back()"><i
                    class="fa fa-arrow-left" aria-hidden="true"></i></button>
            <div class="user-search-box">
                <div class="input-box">
                    <div class="close-button-wrapper" style="color: #4287f5;"><i onclick="params.funcs.closeSearchBox()"
                            class="fa fa-times" aria-hidden="true"></i></div>
                    <div class="input-wrapper">
                        <input type="text" class="search-input" placeholder="Search" />
                        <div class="input-bottom-placeholder">{{'By Username, Sponsor, Userid' |t}} </div>
                    </div>
                    <div>
                    </div>
                </div>
                <div class="result-box">
                    <div class="result-header">{{'RESULTS' |t}}</div>
                    <div class="result-list">
                        <div class="let-buffer"></div>
                    </div>
                </div>
            </div>
            <div id="svgChart" class="let-container let-col-md-12"></div>
        </div>
    </div>
</div>