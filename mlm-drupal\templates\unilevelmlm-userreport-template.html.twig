{{attach_library('unilevelmlm/unilevelmlm')}}
<div class="let-container let-border-left let-border-right">
    <div class="let-content ">
        <h5 class="let-text-info">{{'Personal Details' |t}} {{user_name}} </h5>
        <hr />
        <div class="let-row">
            <!-- user info profile -->
            <div class="let-col-md-5">
                <div class="let-card shadow let-mb-4">
                    <div class="let-card-header ump-bg let-py-3">
                        <h6 class="let-m-0 let-font-weight-bold ">{{'User Details' |t}} </h6>
                    </div>
                    <div class="let-card-body">
                        <div class="let-container">
                            <div class="let-row">
                                <div class="let-col-md-12 let-text-center ">
                                    <img class="img-profile let-rounded-circle" width="200"
                                        src="{{profile_info.image}}">
                                    <h3 class="let-my-3">{{profile_info.user_name}}</h3>
                                </div>
                            </div>
                            <div class="let-row let-text-center let-border">
                                <div class="let-col-md-4 let-col-sm-4 let-col-xs-4">
                                    <h2>{{profile_info.current_balance}}</h2>
                                    <p>{{'Current Balance' |t}}</p>
                                </div>
                                <div class="let-col-md-4 let-col-sm-4 let-col-xs-4">
                                    <h2>{{profile_info.withdrawal_amount}}</h2>
                                    <p>{{'Total withdrawal' |t}}</p>
                                </div>
                                <div class="let-col-md-4 let-col-sm-4 let-col-xs-4">
                                    <h2>{{profile_info.downlines}}</h2>
                                    <p>{{'Total Dowenlins' |t}}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- user profile info -->
            <div class="let-col-md-7">
                <div class="let-row">
                    <div class="let-col-md-6">
                        <div class="let-card shadow let-mb-4">
                            <div class="let-card-header ump-bg let-py-3">
                                <h6 class="let-m-0 let-font-weight-bold ">{{'User Info' |t}} </h6>
                            </div>
                            <div class="let-card-body">
                                <div class="let-table-responsive">
                                    <table class="let-table let-table-bordered  let-table-striped" width="100%"
                                        cellspacing="0">
                                        <tr>
                                            <th>{{'User Name' |t}}</th>
                                            <td>{{profile_info.user_name}}</td>
                                        </tr>
                                        <tr>
                                            <th>{{'User email' |t}}</th>
                                            <td>{{email}}</td>
                                        </tr>
                                        <tr>
                                            <th>{{'User Phone' |t}}</th>
                                            <td>{{ump_phone}}</td>
                                        </tr>
                                        <tr>
                                            <th>{{'Join Date' |t}}</th>
                                            <td>{{user_info.creation_date}}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="let-col-md-6">
                        <div class="let-card shadow let-mb-4">
                            <div class="let-card-header ump-bg let-py-3">
                                <h6 class="let-m-0 let-font-weight-bold ">{{'Ump Info' |t}} </h6>
                            </div>
                            <div class="let-card-body">
                                <div class="let-table-responsive">
                                    <table class="let-table let-table-bordered  let-table-striped" width="100%"
                                        cellspacing="0">
                                        <tr>
                                            <th>{{'User Id' |t}}</th>
                                            <td>{{user_info.user_id}}</td>
                                        </tr>
                                        <tr>
                                            <th>{{'Sponsor Key' |t}}</th>
                                            <td>{{user_info.sponsor_key}}</td>
                                        </tr>
                                        <tr>
                                            <th>{{'Parent key' |t}}</th>
                                            <td>{{user_info.parent_key}}</td>
                                        </tr>
                                        <tr>
                                            <th>{{'Payment Date' |t}}</th>
                                            <td>{{user_info.payment_date}}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="let-col-md-12 let-m-0">
            <div class="let-card shadow let-mb-4">
                <div class="let-card-header ump-bg let-py-3">
                    <h6 class="let-m-0 let-font-weight-bold ">{{'Join Commissions' |t}}</h6>
                </div>
                <div class="let-card-body">
                    <div class="let-table-responsive">
                        <table class="let-table let-table-bordered let-table-striped" id="join_dataTable" width="100%"
                            cellspacing="0">
                            <thead>
                                <tr>
                                    <th>{{'#ID' |t}}</th>
                                    <th>{{'User Name' |t}}</th>
                                    <th>{{'Child Name' |t}}</th>
                                    <th>{{'Amount' |t}}</th>
                                    <th>{{'Date' |t}}</th>
                                </tr>
                            </thead>
                            <tfoot>
                                <tr>
                                    <th>{{'#ID' |t}}</th>
                                    <th>{{'User Name' |t}}</th>
                                    <th>{{'Child Name' |t}}</th>
                                    <th>{{'Amount' |t}}</th>
                                    <th>{{'Date' |t}}</th>

                                </tr>
                            </tfoot>
                            <tbody>

                                {% for value in join_commission %}
                                <tr>
                                    <td>{{value.id}}</td>
                                    <td>{{value.parent_name}}</td>
                                    <td>{{value.child_name}}</td>
                                    <td>{{value.amount}}</td>
                                    <td>{{value.date_notified}}</td>

                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="let-col-md-12 let-m-0">
            <div class="let-card shadow let-mb-4">
                <div class="let-card-header ump-bg let-py-3">
                    <h6 class="let-m-0 let-font-weight-bold ">{{'Referral Commissions' |t}}</h6>
                </div>
                <div class="let-card-body">
                    <div class="let-table-responsive">
                        <table class="let-table let-table-bordered let-table-striped" id="join_dataTable" width="100%"
                            cellspacing="0">
                            <thead>
                                <tr>
                                    <th>{{'#ID' |t}}</th>
                                    <th>{{'User Name' |t}}</th>
                                    <th>{{'Child Name' |t}}</th>
                                    <th>{{'Amount' |t}}</th>
                                    <th>{{'Date' |t}}</th>
                                </tr>
                            </thead>
                            <tfoot>
                                <tr>
                                    <th>{{'#ID' |t}}</th>
                                    <th>{{'User Name' |t}}</th>
                                    <th>{{'Child Name' |t}}</th>
                                    <th>{{'Amount' |t}}</th>
                                    <th>{{'Date' |t}}</th>

                                </tr>
                            </tfoot>
                            <tbody>

                                {% for value in referral_commission %}
                                <tr>
                                    <td>{{value.id}}</td>
                                    <td>{{value.parent_name}}</td>
                                    <td>{{value.child_name}}</td>
                                    <td>{{value.amount}}</td>
                                    <td>{{value.date_notified}}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>