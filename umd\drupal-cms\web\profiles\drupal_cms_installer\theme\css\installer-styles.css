/**
 * @file
 * This file specifically overrides <PERSON><PERSON><PERSON>'s maintenance page styling to make
 * tweaks specific to the Drupal CMS installer. None of this needs to be
 * reusable.
 */

@view-transition {
  navigation: auto;
}

*, *::before, *::after {
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
}

a {
  color: var(--gin-color-primary);
}

html {
  display: table;
  width: 100%;
  height: 100%;
  margin: 0;
}

body.install-page {
  background-color: white;
  padding-block: 0;
  border-inline-start: 25px solid #CCEDF9;
  display: table-cell;

  @media (min-width: 48em) {
    border-inline-start-width: 35px;
  }
}

.page-wrapper {
  height: 100%;
}

.cms-installer {
  font-family: "Inter", system-ui;
  padding-block: 30px;
  padding-inline: 33px;
  min-block-size: 100%;

  @media (min-width: 78em) {
    padding-block: 60px;
    padding-inline: 66px;
  }

  @media (min-width: 78em) and (min-height: 870px) {
    display: grid;
    grid-template-areas: "stack";

    & > * {
      grid-area: stack;
    }
  }
}

.cms-installer__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  align-self: start;

  @media (max-width: 78em) and (min-height: 870px) {
    margin-block-end: 2.5rem;
  }
}

.cms-installer__wrapper {
  display: flex;
  place-items: center;
}

.cms-installer__main {
  inline-size: 100%;
  max-inline-size: 780px;
  min-block-size: 500px;
  position: relative;
  padding-top: 2rem;
}

.cms-installer__heading {
  margin-block: 0;

  @media (max-width: 48em) {
    width: 40vw;
  }
}

.cms-installer__step {
  color: #6D7583;
  font-size: 0.8125rem;
  font-weight: 500;
  position: absolute;
  inset-block-start: 0;
  margin: 0;
}

.cms-installer .cms-installer__main-heading {
  font-size: 2.75rem;
  font-weight: 700;
  margin-block-start: 0;
}

.cms-installer__subhead {
  color: var(--gin-color-text-light);
  font-size: var(--gin-font-size);
  margin-block-start: 0;
}

.cms-installer .form-actions {
  display: flex;
  align-items: center;
  gap:  var(--gin-spacing-xs);
}

.cms-installer .button {
  all: revert;
  display: inline-flex;
  align-items: center;
  flex-wrap: wrap;
  border: solid 1px transparent;
  background: transparent;
  font-family: inherit;
  line-height: 1;
  font-size: var(--gin-font-size-l);
  color: var(--gin-color-primary);
  padding: var(--gin-spacing-m);
  border-radius: var(--gin-border-m);
  font-weight: 600;
  box-shadow: none;
  cursor: pointer;
}
.cms-installer .button:focus {
  outline: 3px solid var(--color-focus);
  outline-offset: 2px;
}

.cms-installer .button--primary {
  transition: var(--gin-transition);
  background-color: var(--gin-color-primary);
  color: var(--gin-color-button-text);
}

.cms-installer .button--primary:active {
  background-color: var(--gin-color-primary-active);
}

.cms-installer .button--primary:hover {
  background-color: var(--gin-color-primary-hover);
}

.cms-installer .button--next {
  padding-inline-end: 2.5rem;
  background-image: url('../images/arrow-right.svg');
  background-repeat: no-repeat;
  background-position: center right 1rem;
}

.cms-installer__form-group {
  --input-border-radius-size: var(--gin-border-m);
  --input-border-size: 1.5px;

  margin-block: 2.5rem;
}

.cms-installer__form-group label {
  font-size: var(--gin-font-size);
  margin-block-end: var(--space-xs);
}

.cms-installer__info {
  flex: 1 1 100%;
  color: var(--gin-color-text-light);
  font-size: var(--gin-font-size);
  display: flex;
  gap: .5rem;

  &::before {
    display: inline-block;
    aspect-ratio: 1;
    content: "";
    background-color: currentColor;
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-position: center;
    -webkit-mask-size: contain;
    mask-size: 20px;
    inline-size: 20px;
    flex-shrink: 0;
    mask-image: url("../images/info.svg");
  }
}

.progress .progress {
  margin-block: 2.5rem;
}

.cms-installer .progress__track {
  border: 0;
  overflow: hidden;
}

.cms-installer .progress__bar {
  border-end-end-radius: 0;
  border-start-end-radius: 0;
  border: 0;
  background-color: var(--gin-color-primary);
}

.cms-installer__pattern {
  margin-block-start: 4.5rem;
  display: block !important;
  position: fixed;
  overflow: hidden;
  width: 100%;
  inset-inline-start: 0;

  @media (min-width: 78em) {
    margin-block-start: 0;
    inset-block: 0 auto;
    inset-inline: auto 0;
    height: 100%;
    width: 30vw;
  }

  @media (min-width: 100em) {
    width: 40vw;
  }
}

.cms-installer__pattern svg {
  @media (min-width: 78em) {
    position: absolute;
    inset-block-start: 0;
    inset-inline-end: 0;
    height: 100%;
  }
}
