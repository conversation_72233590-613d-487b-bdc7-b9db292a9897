langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_event__date
    - node.type.event
  module:
    - smart_date
id: node.event.field_event__date
field_name: field_event__date
entity_type: node
bundle: event
label: Date
description: ''
required: true
translatable: false
default_value:
  -
    default_duration: 60
    default_duration_increments: "30\r\n60|1 hour\r\n90\r\n120|2 hours\r\ncustom"
    default_date_type: next_hour
    default_date: ''
    min: ''
    max: ''
default_value_callback: ''
settings: {  }
field_type: smartdate
