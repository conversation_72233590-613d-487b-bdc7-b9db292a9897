_meta:
  version: '1.0'
  entity_type: node
  uuid: d32a5516-1f37-46d4-8040-e9dad062b194
  bundle: page
  default_langcode: en
default:
  revision_uid:
    -
      target_id: 1
  status:
    -
      value: true
  uid:
    -
      target_id: 1
  title:
    -
      value: Blog
  created:
    -
      value: 1734410215
  promote:
    -
      value: true
  sticky:
    -
      value: false
  revision_translation_affected:
    -
      value: true
  moderation_state:
    -
      value: published
  path:
    -
      alias: /blog
      langcode: en
  field_description:
    -
      value: 'Listing page for blog posts.'
  layout_builder__layout:
    -
      section:
        layout_id: layout_onecol
        layout_settings:
          label: ''
        components:
          541922dc-a8c8-4b74-9cdd-88a1f5d053dd:
            uuid: 541922dc-a8c8-4b74-9cdd-88a1f5d053dd
            region: content
            configuration:
              id: 'field_block:node:page:field_content'
              label_display: '0'
              context_mapping:
                entity: layout_builder.entity
              formatter:
                type: text_default
                label: hidden
                settings: {  }
                third_party_settings: {  }
            weight: 2
            additional: {  }
          261b00fc-c08b-46ea-bfa5-40464dc7f794:
            uuid: 261b00fc-c08b-46ea-bfa5-40464dc7f794
            region: content
            configuration:
              id: 'extra_field_block:node:page:content_moderation_control'
              label_display: '0'
              context_mapping:
                entity: layout_builder.entity
            weight: 1
            additional: {  }
          8b1a5f99-dc7a-43c8-b995-3afa216c41d1:
            uuid: 8b1a5f99-dc7a-43c8-b995-3afa216c41d1
            region: content
            configuration:
              id: 'views_block:blog-all'
              label: ''
              label_display: 0
              provider: views
              views_label: ''
              items_per_page: none
              context_mapping: {  }
            weight: 5
            additional: {  }
          b9d3f0c0-0a17-4d57-93db-84dd0bec52b8:
            uuid: b9d3f0c0-0a17-4d57-93db-84dd0bec52b8
            region: content
            configuration:
              id: 'field_block:node:page:field_featured_image'
              label: 'Featured image'
              label_display: 0
              provider: layout_builder
              formatter:
                label: hidden
                type: entity_reference_entity_view
                settings:
                  view_mode: hero
                third_party_settings: {  }
              context_mapping:
                entity: layout_builder.entity
                view_mode: view_mode
            weight: 0
            additional: {  }
          86e45555-f74e-4cac-b4d6-98b793a92ed4:
            uuid: 86e45555-f74e-4cac-b4d6-98b793a92ed4
            region: content
            configuration:
              id: 'field_block:node:page:field_tags'
              label: Tags
              label_display: 0
              provider: layout_builder
              formatter:
                label: inline
                type: entity_reference_label
                settings:
                  link: 1
                third_party_settings: {  }
              context_mapping:
                entity: layout_builder.entity
                view_mode: view_mode
            weight: 4
            additional: {  }
        third_party_settings: {  }
