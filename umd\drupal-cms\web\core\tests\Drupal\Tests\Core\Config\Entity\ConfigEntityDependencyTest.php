<?php

declare(strict_types=1);

namespace Drupal\Tests\Core\Config\Entity;

use Drupal\Tests\UnitTestCase;
use Drupal\Core\Config\Entity\ConfigEntityDependency;

/**
 * Tests the ConfigEntityDependency class.
 *
 * @group Config
 */
class ConfigEntityDependencyTest extends UnitTestCase {

  public function testEmptyDependencies(): void {
    $dep = new ConfigEntityDependency('config_test.dynamic.entity_id', []);

    $this->assertEquals('config_test.dynamic.entity_id', $dep->getConfigDependencyName());
    $this->assertEquals([], $dep->getDependencies('theme'));
    $this->assertEquals([], $dep->getDependencies('config'));
    $this->assertEquals(['config_test'], $dep->getDependencies('module'));
    $this->assertTrue($dep->hasDependency('module', 'config_test'));
    $this->assertFalse($dep->hasDependency('module', 'views'));
  }

  public function testWithDependencies(): void {
    $values = [
      'uuid' => '60db47f4-54fb-4c86-a439-5769fbda4bd1',
      'dependencies' => [
        'module' => [
          'node',
          'views',
        ],
        'config' => [
          'config_test.dynamic.entity_id:745b0ce0-aece-42dd-a800-ade5b8455e84',
        ],
      ],
    ];
    $dep = new ConfigEntityDependency('config_test.dynamic.entity_id', $values);

    $this->assertEquals([], $dep->getDependencies('theme'));
    $this->assertEquals(['config_test.dynamic.entity_id:745b0ce0-aece-42dd-a800-ade5b8455e84'], $dep->getDependencies('config'));
    $this->assertEquals(['node', 'views', 'config_test'], $dep->getDependencies('module'));
    $this->assertTrue($dep->hasDependency('module', 'config_test'));
    $this->assertTrue($dep->hasDependency('module', 'views'));
    $this->assertTrue($dep->hasDependency('module', 'node'));
    $this->assertFalse($dep->hasDependency('module', 'block'));
    $this->assertTrue($dep->hasDependency('config', 'config_test.dynamic.entity_id:745b0ce0-aece-42dd-a800-ade5b8455e84'));
    $this->assertFalse($dep->hasDependency('config', 'config_test.dynamic.another_id:7dfa5cb7-2248-4d52-8c00-cd8e02d1e78e'));
  }

}
