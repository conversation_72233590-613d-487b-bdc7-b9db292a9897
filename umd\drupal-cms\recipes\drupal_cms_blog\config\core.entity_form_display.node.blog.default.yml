langcode: en
status: true
dependencies:
  config:
    - field.field.node.blog.field_content
    - field.field.node.blog.field_description
    - field.field.node.blog.field_featured_image
    - field.field.node.blog.field_tags
    - node.type.blog
    - workflows.workflow.basic_editorial
  module:
    - path
    - text
    - content_moderation
    - scheduler
id: node.blog.default
targetEntityType: node
bundle: blog
mode: default
content:
  field_content:
    type: text_textarea
    weight: 4
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  created:
    type: datetime_timestamp
    weight: 7
    region: content
    settings: {  }
    third_party_settings: {  }
  field_description:
    type: string_textarea
    weight: 3
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_featured_image:
    type: media_library_widget
    weight: 2
    region: content
    settings:
      media_types: {  }
    third_party_settings: {  }
  field_tags:
    type: tagify_entity_reference_autocomplete_widget
    weight: 5
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 2
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  moderation_state:
    type: moderation_state_default
    weight: -7
    region: content
    settings: {  }
    third_party_settings: {  }
  path:
    type: path
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 8
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  publish_on:
    type: datetime_timestamp_no_default
    weight: -5
    region: content
    settings: {  }
    third_party_settings: {  }
  publish_status:
    type: scheduler_moderation
    weight: -4
    region: content
    settings: {  }
    third_party_settings: {  }
  scheduler_settings:
    weight: -6
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 11
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 9
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  translation:
    weight: 10
    region: content
    settings: {  }
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 6
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  unpublish_on:
    type: datetime_timestamp_no_default
    weight: -3
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  publish_state: true
  unpublish_state: true
