/*
 * DO NOT EDIT THIS FILE.
 * See the following change record for more information,
 * https://www.drupal.org/node/3084859
 * @preserve
 */

/**
 * @file
 * Replacement 'Reusable layout' component styles of system/admin library.
 */

.layout-container {
  margin-right: 1em;
  margin-left: 1em;
}

.layout-container::after {
  display: table;
  clear: both;
  content: "";
}

@media screen and (min-width: 38em) {
  .toolbar-tray-open:not(.toolbar-vertical) .layout-container,
  body:not(.toolbar-tray-open) .layout-container {
    margin-right: 3em;
    margin-left: 3em;
  }
  .toolbar-tray-open:not(.toolbar-vertical) .layout-row,
  body:not(.toolbar-tray-open) .layout-row {
    margin-right: -1.5em;
    margin-left: -1.5em;
  }
  .toolbar-tray-open:not(.toolbar-vertical) .layout-column,
  body:not(.toolbar-tray-open) .layout-column {
    float: left; /* LTR */
    box-sizing: border-box;
    padding-right: 1.5em;
    padding-left: 1.5em;
  }
  [dir="rtl"] .toolbar-tray-open:not(.toolbar-vertical) .layout-column,
  [dir="rtl"] body:not(.toolbar-tray-open) .layout-column {
    float: right;
  }
  .toolbar-tray-open:not(.toolbar-vertical) .layout-column--half,
  body:not(.toolbar-tray-open) .layout-column--half {
    width: 50%;
  }
  .toolbar-tray-open:not(.toolbar-vertical) .layout-column--quarter,
  body:not(.toolbar-tray-open) .layout-column--quarter {
    width: 25%;
  }
  .toolbar-tray-open:not(.toolbar-vertical) .layout-column--three-quarter,
  body:not(.toolbar-tray-open) .layout-column--three-quarter {
    width: 75%;
  }
}

@media screen and (min-width: 60em) {
  .layout-container {
    margin-right: 3em;
    margin-left: 3em;
  }
  .layout-row {
    margin-right: -1.5em;
    margin-left: -1.5em;
  }
  .layout-column {
    float: left; /* LTR */
    box-sizing: border-box;
    padding-right: 1.5em;
    padding-left: 1.5em;
  }
  [dir="rtl"] .layout-column {
    float: right;
  }
  .layout-column--half {
    width: 50%;
  }
  .layout-column--quarter {
    width: 25%;
  }
  .layout-column--three-quarter {
    width: 75%;
  }
}
