/*
 * DO NOT EDIT THIS FILE.
 * See the following change record for more information,
 * https://www.drupal.org/node/3084859
 * @preserve
 */
/**
 * @file toolbar.menu.css
 *
 * If Claro is the admin theme, this stylesheet will be used by the active theme
 * even if the active theme is not Claro.
 */
.toolbar .toolbar-menu,
[dir="rtl"] .toolbar .toolbar-menu {
  margin: 0;
  padding: 0;
  list-style: none;
}
.toolbar .toolbar-box {
  position: relative;
  display: block;
  width: auto;
  line-height: 1em; /* this prevents the value "normal" from being returned as the line-height */
}
/**
 * Hidden vertical toolbar sub-menus by default.
 */
.toolbar .toolbar-tray-vertical .toolbar-menu ul {
  display: none;
}
/**
 * Hidden horizontal toolbar handle icon.
 */
.toolbar .toolbar-tray-horizontal .toolbar-menu .toolbar-handle {
  display: none;
}
/**
 * Hidden toolbar sub-menus by default.
 */
.toolbar-tray-open .toolbar-menu .menu-item--expanded ul {
  display: none;
}
.toolbar .toolbar-tray-vertical li.open > ul {
  display: block; /* Show the sub-menus */
}
.toolbar .toolbar-tray-vertical .toolbar-handle + a {
  margin-right: 3em; /* LTR */
}
[dir="rtl"] .toolbar .toolbar-tray-vertical .toolbar-handle + a {
  margin-right: 0;
  margin-left: 3em;
}
.toolbar .toolbar-tray .menu-item--active-trail > .toolbar-box a,
.toolbar .toolbar-tray a.is-active {
  color: #000;
  background-color: #f5f5f5;
  font-weight: bold;
}
/* ----- Toolbar menu tray for viewports less than 320px ------ */
@media screen and (max-width: 19.9375rem) {
  .toolbar .toolbar-tray-vertical.is-active {
    width: 100%;
  }
}
/**
 * Items.
 */
.toolbar .level-2 > ul {
  border-top-color: #e5e5e5;
  border-bottom-color: #ccc;
  background-color: #fafafa;
}
.toolbar .level-3 > ul {
  border-top-color: #ddd;
  border-bottom-color: #c5c5c5;
  background-color: #f5f5f5;
}
.toolbar .level-4 > ul {
  border-top-color: #d5d5d5;
  border-bottom-color: #bbb;
  background-color: #eee;
}
.toolbar .level-5 > ul {
  border-top-color: #ccc;
  border-bottom-color: #b5b5b5;
  background-color: #e5e5e5;
}
.toolbar .level-6 > ul {
  border-top-color: #c5c5c5;
  border-bottom-color: #aaa;
  background-color: #eee;
}
.toolbar .level-7 > ul {
  border-top-color: #ccc;
  border-bottom-color: #b5b5b5;
  background-color: #fafafa;
}
.toolbar .level-8 > ul {
  border-top-color: #ddd;
  border-bottom-color: #ccc;
  background-color: #ddd;
}
/**
 * Handle.
 */
.toolbar .toolbar-handle:hover {
  cursor: pointer;
}
.toolbar .toolbar-icon.toolbar-handle {
  position: absolute;
  z-index: 1;
  top: 0;
  right: 0; /* LTR */
  bottom: 0;
  display: block;
  height: 100%;
  padding: 0;
}
[dir="rtl"] .toolbar .toolbar-icon.toolbar-handle {
  right: auto;
  left: 0;
  padding: 0;
}
