/**
 * @file
 * View UI admin theme.
 *
 * Replaces the styles provided by the views_ui module.
 */

.views-admin .links {
  margin: 0;
  list-style: none outside none;
}
.views-admin a:hover {
  text-decoration: none;
}
.views-admin .icon {
  width: 16px;
  height: 16px;
}
.views-admin .icon,
.views-admin .icon-text {
  background-image: url(../../../../modules/views_ui/images/sprites.png);
  background-repeat: no-repeat;
  background-attachment: scroll;
  background-position: left top; /* LTR */
}
[dir="rtl"] .views-admin .icon,
[dir="rtl"] .views-admin .icon-text {
  background-position: right top;
}
.views-admin a.icon {
  border: 1px solid #ddd;
  border-radius: 4px;
  background:
    linear-gradient(-90deg, #fff 0, #e8e8e8 100%) no-repeat,
    repeat-y;
  box-shadow: 0 0 0 rgba(0, 0, 0, 0.3333) inset;
}
.views-admin a.icon:hover {
  border-color: #d0d0d0;
  box-shadow: 0 0 1px rgba(0, 0, 0, 0.3333) inset;
}
.views-admin a.icon:active {
  border-color: #c0c0c0;
}
.views-admin span.icon {
  position: relative;
  float: left; /* LTR */
}
[dir="rtl"] .views-admin span.icon {
  float: right;
}
.views-admin .icon.compact {
  display: block;
  overflow: hidden;
  text-indent: -9999px;
  direction: ltr;
}

/* Targets any element with an icon -> text combo */
.views-admin .icon-text {
  padding-left: 19px; /* LTR */
}
[dir="rtl"] .views-admin .icon-text {
  padding-right: 19px;
  padding-left: 0;
}
.views-admin .icon.linked {
  background-position: center -153px;
}
.views-admin .icon.unlinked {
  background-position: center -195px;
}
.views-admin .icon.delete {
  background-position: center -52px;
}
.views-admin a.icon.delete {
  background-position:
    center -52px,
    left top; /* LTR */
}
[dir="rtl"] .views-admin a.icon.delete {
  background-position:
    center -52px,
    right top;
}
.views-admin .icon.rearrange {
  background-position: center -111px;
}
.views-admin a.icon.rearrange {
  background-position:
    center -111px,
    left top; /* LTR */
}
[dir="rtl"] .views-admin a.icon.rearrange {
  background-position:
    center -111px,
    right top;
}
details.box-padding {
  border: none;
}
.views-admin details details {
  margin-bottom: 0;
}
.form-item {
  margin-top: 9px;
  padding-top: 0;
  padding-bottom: 0;
}
.form-type-checkbox {
  margin-top: 6px;
}
.form-checkbox,
.form-radio {
  vertical-align: baseline;
}
/* Indent form elements so they're directly underneath the label of the checkbox that reveals them */
.views-admin .form-type-checkbox + .form-wrapper {
  margin-left: 16px; /* LTR */
}
[dir="rtl"] .views-admin .form-type-checkbox + .form-wrapper {
  margin-right: 16px;
  margin-left: 0;
}

/* Hide 'remove' checkboxes. */
.views-remove-checkbox {
  display: none;
}

/* sizes the labels of checkboxes and radio button to the height of the text */
.views-admin .form-type-checkbox label,
.views-admin .form-type-radio label {
  line-height: 2;
}
.views-admin-dependent .form-item {
  margin-top: 6px;
  margin-bottom: 6px;
}
.views-ui-view-name h3 {
  margin: 0.25em 0;
  font-weight: bold;
}
.view-changed {
  margin-bottom: 21px;
}
.views-admin .unit-title {
  margin-top: 18px;
  margin-bottom: 0;
  font-size: 15px;
  line-height: 1.6154;
}
.views-ui-view-displays ul {
  margin-left: 0; /* LTR */
  padding-left: 0; /* LTR */
  list-style: none;
}
[dir="rtl"] .views-ui-view-displays ul {
  margin-right: 0;
  margin-left: inherit;
  padding-right: 0;
  padding-left: inherit;
}

/* These header classes are ambiguous and should be scoped to th elements */
.views-ui-name {
  width: 20%;
}
.views-ui-description {
  width: 30%;
}
.views-ui-machine-name {
  width: 15%;
}
.views-ui-displays {
  width: 25%;
}
.views-ui-operations {
  width: 10%;
}

/**
 * I wish this didn't have to be so specific
 */
.form-item-description-enable + .form-item-description {
  margin-top: 0;
}
.form-item-description-enable label {
  font-weight: bold;
}
.form-item-page-create,
.form-item-block-create {
  margin-top: 13px;
}
.form-item-page-create label,
.form-item-block-create label,
.form-item-rest-export-create label {
  font-weight: bold;
}

/* This makes the form elements after the "Display Format" label flow underneath the label */
.form-item-page-style-style-plugin > label,
.form-item-block-style-style-plugin > label {
  display: block;
}
.views-attachment .options-set label {
  font-weight: normal;
}

/* Styling for the form that allows views filters to be rearranged. */
.group-populated {
  display: none;
}
td.group-title {
  font-weight: bold;
}
.views-ui-dialog td.group-title {
  height: 1px;
  margin: 0;
  padding: 0;
}
.views-ui-dialog td.group-title span {
  display: block;
  overflow: hidden;
  height: 1px;
}
.group-message .form-submit,
.views-remove-group-link,
.views-add-group {
  float: right; /* LTR */
  clear: both;
}
[dir="rtl"] .group-message .form-submit,
[dir="rtl"] .views-remove-group-link,
[dir="rtl"] .views-add-group {
  float: left;
}
.views-operator-label {
  position: absolute;
  z-index: 1;
  bottom: -13px;
  padding: 0.5px 6px;
  text-transform: uppercase;
  border: 1px solid var(--color-gray-200);
  background: #fff;
  font-weight: bold;
  font-style: italic;
}
.grouped-description,
.exposed-description {
  float: left; /* LTR */
  padding-top: 3px;
  padding-right: 10px; /* LTR */
}
[dir="rtl"] .grouped-description,
[dir="rtl"] .exposed-description {
  float: right;
  padding-right: 0;
  padding-left: 10px;
}
.views-displays {
  padding-bottom: 36px;
  border-top: 1px solid var(--color-gray-200-o-80);
  border-right: 1px solid var(--color-gray-200-o-80);
  border-left: 1px solid var(--color-gray-200-o-80);
}
.views-display-top {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: var(--space-s) var(--space-s) calc(var(--space-s) - 5px);
  border-bottom: 1px solid var(--color-gray-200-o-80);
  background-color: var(--color-gray-050);
}
.form-edit .form-actions,
.form-edit .field-actions {
  margin-top: 0;
  padding: var(--space-s) var(--space-m);
  border-right: 1px solid var(--color-gray-200-o-80);
  border-bottom: 1px solid var(--color-gray-200-o-80);
  border-left: 1px solid var(--color-gray-200-o-80);
  background-color: var(--color-gray-050);
}
.edit-display-settings {
  margin: var(--space-l) var(--space-l) 0 var(--space-l);
}
.edit-display-settings-top.views-ui-display-tab-bucket.views-ui-display-tab-bucket {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin: 0 0 var(--space-l);
  padding: 0;
  border-bottom: none;
  line-height: 20px;
}
.edit-display-settings-top.views-ui-display-tab-bucket .views-display-setting {
  padding: 0.125rem 0 0;
}
.views-display-column {
  border: 1px solid var(--color-gray-200-o-80);
  border-radius: var(--base-border-radius);
  box-shadow: var(--details-box-shadow);
}
.views-display-column + .views-display-column {
  margin-top: 0;
}
.view-preview-form .form-item--view-args,
.view-preview-form .form-actions,
.view-preview-form .field-actions {
  margin-top: 5px;
}
.view-preview-form .views-bulk-actions__item {
  margin-block-start: 0;
}
.view-preview-form .arguments-preview {
  font-size: 1em;
}
.view-preview-form .form-item--view-args {
  margin-top: var(--space-m);
}
.view-preview-form .arguments-preview,
.view-preview-form .form-item--view-args {
  margin-right: var(--space-m);
  margin-left: var(--space-m);
}
.preview-submit-wrapper {
  display: inline-block;
}
.form-item--live-preview,
.view-preview-form .form-actions,
.view-preview-form .field-actions {
  vertical-align: top;
}

@media screen and (min-width: 45em) {
  /* 720px */
  .view-preview-form .form-type-textfield .description {
    white-space: nowrap;
  }
}

/* These are the individual "buckets," or boxes, inside the display settings area */
.views-ui-display-tab-bucket.views-ui-display-tab-bucket {
  position: relative;
  margin: 0 0 var(--space-xs);
  padding-top: var(--space-xs);
  padding-bottom: var(--space-xs);
  border-bottom: 1px solid var(--color-gray-200-o-80);
  line-height: 20px;
}
.views-ui-display-tab-bucket:last-of-type {
  margin-bottom: 0;
  border-bottom: none;
}
.views-ui-display-tab-bucket + .views-ui-display-tab-bucket {
  border-top: medium none;
}
.views-ui-display-tab-bucket__header {
  display: inline-flex;
  justify-content: space-between;
  box-sizing: border-box;
  width: 100%;
  padding: var(--space-s) var(--space-m) calc(var(--space-s) + 2px);
}
.views-ui-display-tab-bucket__title {
  margin: 0;
  font-size: var(--font-size-base);
}
.views-ui-display-tab-bucket.access {
  padding-top: 0;
}
.views-ui-display-tab-bucket.page-settings {
  border-bottom: medium none;
}

/** Applies an overridden(italics) font style to overridden buckets.
 * The better way to implement this would be to add the overridden class
 * to the bucket header when the bucket is overridden and style it as a
 * generic icon classed element. For the moment, we'll style the bucket
 * header specifically with the overridden font style.
 */
.views-ui-display-tab-setting.overridden,
.views-ui-display-tab-bucket.overridden .views-ui-display-tab-bucket__title {
  font-style: italic;
}

/* This is each row within one of the "boxes." */
.views-ui-display-tab-bucket .views-display-setting {
  display: flex;
  flex-wrap: wrap;
  padding: var(--space-xs) var(--space-m);
  color: #666;
  font-size: var(--font-size-s);
}
.views-ui-display-tab-actions.views-ui-display-tab-bucket .views-display-setting {
  background-color: transparent;
}
.views-ui-display-tab-bucket .views-group-text {
  margin-top: 6px;
  margin-bottom: 6px;
}
.views-ui-display-tab-bucket__actions {
  margin-left: var(--space-xs); /* LTR */
}
[dir="rtl"] .views-ui-display-tab-bucket__actions {
  margin-right: var(--space-xs);
  margin-left: 0;
}
.views-display-setting .label {
  margin-right: var(--space-xs); /* LTR */
  white-space: nowrap;
}
[dir="rtl"] .views-display-setting .label {
  margin-right: 0;
  margin-left: var(--space-xs);
}
.label--separator.label--separator {
  margin-right: var(--space-xs);
  margin-left: var(--space-xs);
}
.views-edit-view {
  margin-bottom: 15px;
}
.views-edit-view.disabled .views-displays {
  background-color: var(--color-red-020);
}
.views-edit-view.disabled .views-display-column {
  background: var(--color-white);
}
/* The contents of the popup dialog on the views edit form. */
.views-filterable-options .form-type-checkbox {
  padding: 5px 8px;
  border-top: none;
}
.filterable-option .form-item {
  margin-top: 0;
  margin-bottom: 0;
}
.views-filterable-options .form-type-checkbox .description {
  margin-top: 0;
  margin-bottom: 0;
}
.views-filterable-options-controls .form-item {
  width: 30%;
  margin: 0 0 0 2%; /* LTR */
}
[dir="rtl"] .views-filterable-options-controls .form-item {
  margin: 0 2% 0 0;
}
.views-filterable-options-controls input,
.views-filterable-options-controls select {
  width: 100%;
}
.views-ui-dialog.views-ui-dialog > .ui-dialog-content {
  padding: 0;
}

.views-ui-dialog .views-filterable-options {
  margin-bottom: 10px;
}
[id^="views-ui-add-handler-form"] .scroll {
  padding-top: 0;
  padding-bottom: 0;
}

.views-ui-dialog .views-add-form-selected {
  padding: 0 1rem;
}
.views-ui-dialog .views-add-form-selected > div {
  display: block;
  margin: 0;
  padding: 0.3rem 0.8rem;
  border: 1px solid #dedfe4;
  border-radius: 2px 2px 0 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}
.views-ui-dialog .form-item-selected {
  margin: 0;
  padding: 6px 16px;
}
.views-ui-dialog .views-add-form-selected .views-selected-options {
  display: inline;
}
.views-ui-dialog.views-ui-dialog-scroll .ui-dialog-titlebar {
  border: none;
}
.views-ui-dialog .views-offset-top:not(:empty) {
  position: relative;
  padding: var(--space-s) var(--space-m) var(--space-xs);
  background-color: var(--color-gray-050);
}
.views-ui-dialog .views-offset-top:not(:empty)::after {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  content: "";
  background: linear-gradient(to bottom, rgba(80, 81, 86, 0.11) 0%, rgba(18, 19, 20, 0.02) 49%, rgba(18, 19, 20, 0) 100%);
}

.views-ui-dialog .views-offset-top > * {
  margin: 0;
}

.views-ui-dialog .views-offset-top .form-item {
  max-width: 40%;
  margin: 0 var(--space-s) var(--space-s) 0;
}

.views-ui-dialog .tabledrag-toggle-weight-wrapper {
  margin: 0 0 var(--space-m) 0;
}

.views-ui-dialog details .item-list {
  padding-left: 2em; /* LTR */
}
[dir="rtl"] .views-ui-dialog details .item-list {
  padding-right: 2em;
  padding-left: 0;
}
.views-display-columns .details-wrapper {
  margin: 0;
}
.views-ui-rearrange-filter-form table {
  border-collapse: collapse;
}
.views-ui-rearrange-filter-form tr td[rowspan] {
  border: 1px solid var(--color-gray-200);
}
.views-ui-rearrange-filter-form tr[id^="views-row"] {
  border-right: 1px solid #cdcdcd; /* LTR */
}
[dir="rtl"] .views-ui-rearrange-filter-form tr[id^="views-row"] {
  border-right: 0;
  border-left: 1px solid #cdcdcd;
}
.views-ui-rearrange-filter-form .draggable td {
  border-bottom: 0.0625rem solid var(--color-gray-200);
}
.views-ui-rearrange-filter-form .group-empty {
  border-bottom: 1px solid #cdcdcd;
}
.form-item-options-expose-required,
.form-item-options-expose-label,
.form-item-options-expose-field-identifier,
.form-item-options-expose-description {
  margin-top: 6px;
  margin-bottom: 6px;
  margin-left: 18px; /* LTR */
}
[dir="rtl"] .form-item-options-expose-required,
[dir="rtl"] .form-item-options-expose-label,
[dir="rtl"] .form-item-options-expose-field-identifier,
[dir="rtl"] .form-item-options-expose-description {
  margin-right: 18px;
  margin-left: 0;
}
.views-preview-wrapper {
  border: 1px solid #ccc;
}
.view-preview-form {
  position: relative;
}
.view-preview-form__title {
  margin-top: 0;
  padding: 8px 12px;
  border-bottom: 1px solid #ccc;
  background-color: var(--color-gray-050);
}
.view-preview-form .form-item--live-preview {
  position: absolute;
  top: 0.6875rem;
  right: var(--space-s);
  margin-top: 2px;
  margin-left: 2px; /* LTR */
}
[dir="rtl"] .view-preview-form .form-item--live-preview {
  right: auto;
  left: var(--space-s);
  margin-right: 2px;
  margin-left: 0;
}
.views-live-preview {
  padding: var(--space-m);
}
.views-live-preview .views-query-info {
  overflow: auto;
}
.views-live-preview .section-title {
  display: inline-block;
  margin-top: 0;
  margin-bottom: 0;
  color: var(--color-gray-800);
  font-size: 13px;
  font-weight: normal;
  line-height: 1.6154;
}
.views-live-preview .view > * {
  margin-top: var(--space-m);
}
.views-live-preview .preview-section {
  margin: 0 -5px;
  padding: 3px 5px;
  border: 1px dashed #dedede;
}
.views-live-preview li.views-row + li.views-row {
  margin-top: 18px;
}

/* The div.views-row is intentional and excludes li.views-row, for example */
.views-live-preview div.views-row + div.views-row {
  margin-top: 36px;
}
.views-query-info table {
  margin: 10px 0;
  border-spacing: 0;
  border-collapse: separate;
  border-color: var(--color-bg);
}
.views-query-info table tr {
  background-color: var(--color-gray-050);
}
.views-query-info table th,
.views-query-info table td {
  padding: var(--space-xs) var(--space-l);
  font-size: var(--font-size-s);
}
.messages {
  margin-bottom: 18px;
  line-height: 1.4555;
}

.js .dropbutton-wrapper .dropbutton .dropbutton-action > .ajax-progress-throbber {
  position: absolute;
  z-index: 2;
  top: -1px;
  right: -5px; /* LTR */
}
[dir="rtl"].js .dropbutton-wrapper .dropbutton .dropbutton-action > .ajax-progress-throbber {
  right: auto;
  left: -5px;
}

/**
 * Position dropbuttons with flexbox instead after the dropbuttons have been
 * converted to splitbuttons.
 *
 * @see https://www.drupal.org/project/drupal/issues/3134107
 * @see https://www.drupal.org/project/drupal/issues/1899236
 */
.edit-display-settings-top.views-ui-display-tab-bucket .dropbutton-wrapper {
  top: 0.8125rem;
  right: var(--space-s); /* LTR */
}
[dir="rtl"] .edit-display-settings-top.views-ui-display-tab-bucket .dropbutton-wrapper {
  right: auto;
  left: var(--space-s);
}

.views-list-section {
  margin-bottom: 2em;
}
.form-textarea-wrapper,
.form-item-options-content {
  width: 100%;
}

.views-messages .messages-list,
.views-messages .messages-list__item {
  margin-top: 0;
  margin-bottom: 0;
}

/**
 * Styles on devices with touchevents.
 */
html:not(.no-touchevents) .views-ui-display-tab-bucket__header--actions {
  padding: var(--space-l) var(--space-xs);
}
html:not(.no-touchevents) .edit-display-settings-top.views-ui-display-tab-bucket {
  padding: var(--space-m) var(--space-xs);
}
