{{attach_library('unilevelmlm/unilevelmlm')}}
<div class="content_style">
    <div class="form-row let-border">
        <div class="let-col-md-12 let-col-sm-12 let-col-xs-12 let-text-center"><br />
            <h4><strong>{{'Run payout' |t}}</strong></h4>
        </div>
        <div class="let-loader-layer" id="run_payout_loader">
            <div class="let-pic-loader"></div>
        </div>
        <table class="let-table-bordered let-table-striped  ump_width_table let-border" id="ump_run_payout_table">
            <thead class="let-thead-inverse ump-bg let-text-black">
                <tr class="let-text-center">
                    <th>{{'#' |t}}</th>
                    <th>{{'User Name ' |t}}</th>
                    <th>{{'Join Commission ' |t}}</th>
                    <th>{{'Refferal Commission ' |t}}</th>                     
                    <th>{{'Total Commission' |t}}</th>                    
                    <th>{{'Net Amount' |t}}</th>
                </tr>

            </thead>
            <tbody>
                {% set i=1 %}
                {% if data_array is not empty %}
                {% for data_array_row in data_array %}
                <tr class="let-text-center">
                    <td>{{i}}</td>
                    <td>{{data_array_row.username}}</td>
                    <td>{{data_array_row.join_commission}}</td>
                    <td>{{data_array_row.direct_refferal_commission}}</td>                     
                    <td>{{data_array_row.total_commission}}</td>                    
                    <td>{{data_array_row.net_amount}}</td>
                </tr>
                {% set i= i + 1 %}
                {% endfor %}
                {% endif %}
            </tbody>
            <tfoot>
                <tr class="let-text-black let-text-center ">
                    <td colspan="10" class="let-text-center ump-bg let-py-2">
                        <button class="let-btn let-btn-primary " onclick='ump_run_payout();'>{{'Run Payout' |t}}</button>
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>
</div> 