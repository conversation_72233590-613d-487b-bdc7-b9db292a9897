langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_case_study__client_logo
    - media.type.image
    - node.type.case_study
id: node.case_study.field_case_study__client_logo
field_name: field_case_study__client_logo
entity_type: node
bundle: case_study
label: 'Client logo'
description: 'Include the logo of the client or organization.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      image: image
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
