/*
 * DO NOT EDIT THIS FILE.
 * See the following change record for more information,
 * https://www.drupal.org/node/3084859
 * @preserve
 */

/**
 * @file
 * Styles for multiple file widget table.
 */

.table-file-multiple-widget tbody {
  vertical-align: top;
}

.table-file-multiple-widget .tabledrag-cell-content {
  position: relative;
  display: block;
  height: auto;
}

.table-file-multiple-widget .tabledrag-cell-content > * {
  display: block;
}

.table-file-multiple-widget .tabledrag-cell-content__item {
  padding: 0;
}

.table-file-multiple-widget .tabledrag-handle,
.table-file-multiple-widget .tabledrag-changed {
  float: left;
}

.table-file-multiple-widget .tabledrag-changed {
  line-height: calc(var(--tabledrag-handle-icon-size) + calc(var(--space-xs) * 2));
}

.table-file-multiple-widget td {
  height: calc(var(--space-m) * 3);
  padding-block: var(--space-m);
}

.table-file-multiple-widget td > :first-child,
.table-file-multiple-widget td > :last-child {
  margin-block: 0;
}

.table-file-multiple-widget .button.button:only-child {
  margin: 0;
}

.table-file-multiple-widget th {
  height: calc(var(--space-m) * 2);
  color: var(--color-gray-800);
  background: var(--color-gray-050);
  font-size: var(--font-size-s);
}

.table-file-multiple-widget .tabledrag-cell {
  padding-block: var(--space-xs);
}

.table-file-multiple-widget .checkbox .form-type--boolean {
  line-height: calc(var(--space-m) * 3);
}

[dir="rtl"] .table-file-multiple-widget .tabledrag-handle,
[dir="rtl"] .table-file-multiple-widget .tabledrag-changed {
  float: right;
}

.no-touchevents .table-file-multiple-widget .checkbox .form-type--boolean {
  line-height: var(--line-height);
}

/**
 * The cell that contains file operations (usually, this is the remove button).
 */

.file-operations-cell {
  width: 1px;
  white-space: nowrap; /* Don't let ajax-progress be broken into a new line. */
}

/**
 * Remove the border for the last table row if upload is not possible.
 * (A full file widget with limited cardinality.)
 */

.table-file-multiple-widget--no-upload > tbody:last-child > tr:last-child {
  border-block-end: 0;
}

/**
 * Take as much space as possible.
 */

@media screen and (max-width: 37.5em) {
  .claro-details__wrapper .file-widget-multiple__table-wrapper {
    margin-inline: calc(var(--space-m) * -1);
  }
  .claro-details__wrapper .file-widget-multiple__table-wrapper > :not(table) {
    margin-inline: var(--space-m);
  }
}
