<?php

namespace Drupal\unilevelmlm\Form;

use Drupal\Core\Entity\ContentEntityForm;
use Drupal\Core\Form\FormStateInterface;

/**
 * Form controller for the Rebate/Discount Rule entity edit form.
 */
class RebateDiscountRuleForm extends ContentEntityForm {

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    /* @var $entity \Drupal\unilevelmlm\Entity\RebateDiscountRule */
    $form = parent::buildForm($form, $form_state);

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function save(array $form, FormStateInterface $form_state) {
    $entity = $this->entity;

    $status = parent::save($form, $form_state);

    switch ($status) {
      case SAVED_NEW:
        drupal_set_message($this->t('Created the %label Rebate/Discount Rule entity.', [
          '%label' => $entity->label(),
        ]));
        break;

      default:
        drupal_set_message($this->t('Saved the %label Rebate/Discount Rule entity.', [
          '%label' => $entity->label(),
        ]));
    }
    $form_state->setRedirect('entity.rebate_discount_rule.canonical', ['rebate_discount_rule' => $entity->id()]);
  }

}