/**
 * Views styling
 */

/* @group Forms */

/**
 * <PERSON><PERSON><PERSON> positions the summary absolutely, but does not have a way to ignore
 * details without a summary so we make one up.
 *
 * @todo Neither a fieldset without legend nor a details without summary is
 *   valid HTML markup in any way. Refactor Views UI to not produce such invalid
 *   markup.
 */
details.fieldset-no-legend {
  padding-top: 0;
}

/**
 * Being extra safe here and scoping this to the add view wizard form (where
 * a layout problem occurs for the Display format details if we don't fix its
 * padding), but it's probably safe to just let it apply everywhere.
 */
.views-ui-dialog input.form-submit,
.views-admin a.button,
.views-ui-dialog a.button {
  margin-top: 0;
  margin-right: 0;
  margin-bottom: 0;
}
[dir="rtl"] .views-ui-dialog input.form-submit,
[dir="rtl"] .views-admin a.button,
[dir="rtl"] .views-ui-dialog a.button {
  margin-right: 1em;
  margin-left: 0;
}
[dir="rtl"] .views-ui-dialog input.form-submit:first-child,
[dir="rtl"] .views-admin a.button:first-child,
[dir="rtl"] .views-ui-dialog a.button:first-child {
  margin-right: 0;
}

.views-ui-dialog .form--flex {
  display: flex;
  overflow: hidden;
  flex-wrap: wrap;
}

.views-ui-dialog .form-item {
  margin-top: var(--space-m);
  margin-bottom: var(--space-m);
}
.views-ui-dialog .form-type--boolean {
  margin-right: 0;
  margin-left: 0;
}
.views-ui-dialog .form-type--boolean .form-boolean {
  top: 0;
  float: none;
  margin: 0 0.25rem 0 0; /* LTR */
  transform: none;
}
[dir="rtl"] .views-ui-dialog .form-type--boolean .form-boolean {
  margin-right: 0;
  margin-left: 0.25rem;
}

.views-ui-dialog .form-boolean-group .form-type--boolean {
  margin-top: 0.4em;
  margin-bottom: 0.4em;
}

.views-ui-dialog .form-item:first-of-type.description {
  margin: 0 0 var(--space-l) 0;
  padding-bottom: var(--space-s);
  border-bottom: 0.0625rem solid var(--color-gray-200);
  font-weight: bold;
}

/* So "remove" link appears next to the checkbox. */
.views-ui-dialog .draggable .form-type--checkbox {
  display: inline-block;
  margin: 0 0.25rem;
}

.views-ui-dialog .form-element {
  min-height: calc(((var(--input-padding-vertical--small) + var(--input-border-size)) * 2) + var(--input-line-height--small)); /* iOS. */
  padding: var(--input-padding-vertical--small) var(--input-padding-horizontal--small);
  font-size: var(--input-font-size--small);
  line-height: var(--input-line-height--small);
}
.views-ui-dialog .form-element--type-select {
  padding-inline-end: calc(2rem - var(--input-border-size));
  background-position-y: 56%;
}
.views-ui-dialog td .form-element {
  width: auto;
}

/* @group Dependent options */

/* This is necessary to supercede the Claro .form-item
 * reset declaration that sets the margin to zero.
 */
.form-item-options-expose-required,
.form-item-options-expose-label,
.form-item-options-expose-field-identifier,
.form-item-options-expose-description {
  margin-left: 1.5em; /* LTR */
}
[dir="rtl"] .form-item-options-expose-required,
[dir="rtl"] .form-item-options-expose-label,
[dir="rtl"] .form-item-options-expose-field-identifier,
[dir="rtl"] .form-item-options-expose-description {
  margin-right: 1.5em;
  margin-left: 0;
}

.views-admin-dependent .form-item .form-item,
.views-admin-dependent .form-type-checkboxes,
.views-admin-dependent .form-type-radios,
.views-admin-dependent .form-item .form-item,
.form-item-options-expose-required,
.form-item-options-expose-label,
.form-item-options-expose-field-identifier,
.form-item-options-expose-description {
  margin-top: 6px;
  margin-bottom: 6px;
}

.views-admin-dependent .form-type-radio,
.views-admin-dependent .form-radios .form-item {
  margin-top: 2px;
  margin-bottom: 2px;
}

/* @end */

/* @group Lists */

.views-admin .item-list ul {
  margin: 0;
  padding: 0;
}

.views-admin .links li {
  padding-right: 0; /* LTR */
}
[dir="rtl"] .views-admin .links li {
  padding-left: 0;
}

.views-admin .button .links li {
  padding-right: 12px; /* LTR */
}
[dir="rtl"] .views-admin .button .links li {
  padding-left: 12px;
}

.views-display-top__extra-actions-wrapper {
  margin: calc(var(--space-xs) / 2) var(--space-xs) var(--space-xs);
}

/* @end */

/* @group Tables */

.views-ui-rearrange-filter-form td,
.views-ui-rearrange-filter-form th {
  vertical-align: top;
}

/* @end */

/* @group Attachment details */

#edit-display-settings-title {
  color: var(--color-blue-400);
}

/* @end */

/* @group Attachment details tabs
 *
 * The tabs that switch between sections
 *
 * @todo this group contains lots of duplicates from core styles because Claro
 *   has its custom markup for views tabs. Some of these could be removed after
 *   https://www.drupal.org/node/3051605 has been solved.
 */

.views-tabs {
  display: flex;
  overflow: visible;
  flex-wrap: wrap;
  margin: 0 var(--space-l) 0 0; /* LTR */
  padding: 0;
  list-style: none;
  text-align: left; /* LTR */
  border-bottom: 0 none;
}
[dir="rtl"] .views-tabs {
  margin-right: 0;
  margin-left: var(--space-l);
  text-align: right;
}
.views-tabs .views-display-deleted-link {
  text-decoration: line-through;
}
.views-tabs li,
.views-tabs li.is-active {
  width: auto;
  padding: 0;
  border: 0;
  background: transparent;
}
.views-tabs li.add ul.action-list li {
  margin: 0;
}
.views-tabs li {
  margin: 0 5px 5px 6px; /* LTR */
}
[dir="rtl"] .views-tabs li {
  margin-right: 6px;
  margin-left: 5px;
}
.views-tabs li + li {
  border-top: 0;
}
.views-tabs li:hover {
  padding-left: 0; /* LTR */
  border: 0;
}
[dir="rtl"] .views-tabs li:hover {
  padding-right: 0;
}
.views-tabs a {
  display: inline-block;
  padding: 10px;
  border: var(--input-border-size) solid #cbcbcb;
  border-radius: 7px;
  font-size: small;
  line-height: 1.3333;
}

/* Display a red border if the display doesn't validate. */
.views-tabs li.is-active a.is-active.error,
.views-tabs .error {
  padding: 8px;
  border: 2px solid #ed541d;
}
.views-tabs a:focus {
  outline: none;
}
.views-tabs li a {
  text-decoration: none;
  background-color: #fff;
}
.views-tabs li a:hover,
.views-tabs li.is-active a,
.views-tabs li.is-active a.is-active {
  color: #fff;
  background-color: #555;
}
.views-tabs .add {
  position: relative;
}
.views-tabs .add a {
  padding: 9px 13px 9px 9px;
  color: var(--color-gray-800);
  border: none;
  border-radius: var(--base-border-radius);
  background-color: transparent;
  font-size: var(--font-size-base);
  font-weight: 700;
}
.views-tabs .add a::before {
  display: inline-block;
  width: 1em;
  height: calc(1em - (var(--input-border-size) * 2));
  content: "";
  /* Copy of icon from .action-link--icon-plus */
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' stroke-width='2' stroke='%23545560'%3E%3Cpath d='m3 8h10'/%3E%3Cpath d='m8 3v10'/%3E%3C/svg%3E");
}
.views-tabs .add a:hover {
  color: var(--color-absolutezero-hover);
  background-color: var(--color-bgblue-hover);
}
.views-tabs .add a:hover::before {
  /* Copy of icon from .action-link--icon-plus:hover */
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' stroke-width='2' stroke='%230036b1'%3E%3Cpath d='m3 8h10'/%3E%3Cpath d='m8 3v10'/%3E%3C/svg%3E");
}
.views-tabs .add a:focus {
  background-color: var(--color-bgblue-active);
  box-shadow: 0 0 0 3px #26a769;
}
.views-tabs .add.open a {
  color: var(--color-white);
  background-color: var(--color-absolutezero);
}
.views-tabs .add.open a::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16' stroke-width='2' stroke='%23FFFFFF'%3E%3Cpath d='m3 8h10'/%3E%3Cpath d='m8 3v10'/%3E%3C/svg%3E");
}
/* Hide core icon, added via JS that isn't accessible via theme function. */
.views-tabs .add .icon.add {
  display: none;
}
.views-tabs .action-list {
  position: absolute;
  z-index: 50;
  top: 38px;
  left: -2px; /* LTR */
  margin: 0;
  box-shadow: var(--details-box-shadow);
}
[dir="rtl"] .views-tabs .action-list {
  right: 0;
  left: auto;
}
.views-tabs .action-list li {
  display: block;
}
.views-tabs .action-list li {
  border-width: 0 1px;
  border-style: solid;
  border-color: #cbcbcb;
  background-color: #fff;
}
.views-tabs .action-list li:first-child {
  border-width: 1px 1px 0;
  border-radius: 0 var(--base-border-radius) 0 0; /* LTR */
}
[dir="rtl"] .views-tabs .action-list li:first-child {
  border-radius: 0 0 0 var(--base-border-radius);
}
.views-tabs .action-list li:last-child,
.views-displays .action-list li:last-child {
  padding-bottom: 0.4rem;
  border-width: 0 1px 1px;
  border-bottom-right-radius: var(--button-border-radius-size);
  border-bottom-left-radius: var(--button-border-radius-size);
}
.views-tabs__action-list-button {
  width: 100%;
  margin: 0;
  padding: var(--space-s) var(--space-l);
  text-align: left;
  border: medium none;
  border-radius: 0;
  background: none repeat scroll 0 0 transparent;
  font-weight: normal;
}

/* JS moves Views action buttons under a secondary tabs container, which causes
a large layout shift. We mitigate this by using animations to temporarily hide
the buttons, but they will appear after a set amount of time just in case the JS
is loaded but does not properly run. */
@media (scripting: enabled) {
  .views-tabs__action-list-button:not(.views-tabs--secondary *) {
    animation-name: appear;
    animation-duration: 0.1s;
    /* Buttons will be hidden for the amount of time in the animation-delay if
    not moved. Note this is the approximate time to download the views
    aggregate CSS with slow 3G. */
    animation-delay: 5s;
    animation-iteration-count: 1;
    animation-fill-mode: backwards;
  }
}

@keyframes appear {
  from {
    display: none;
  }

  to {
    display: unset;
  }
}

/* RTL required for precedence over core's styles. */
[dir="rtl"] .views-tabs__action-list-button {
  margin: 0;
}
.views-tabs__action-list-button.button:hover,
.views-tabs__action-list-button.button:focus {
  color: var(--color-white);
  background-color: var(--color-absolutezero);
}
/* Remove outline provided by default styling */
.views-tabs__action-list-button:not(:focus) {
  box-shadow: none;
}

/* @end */

/* @group Attachment buckets
 *
 * These are the individual "buckets," or boxes, inside the display settings area
 */

.views-ui-display-tab-bucket .links {
  padding: 2px 6px 4px;
}

.views-ui-display-tab-bucket .links li + li {
  margin-left: 3px; /* LTR */
}
[dir="rtl"] .views-ui-display-tab-bucket .links li + li {
  margin-right: 3px;
  margin-left: 0;
}

/* @end */

/* @group Rearrange filter criteria */

.views-ui-rearrange-filter-form tr {
  border-bottom: 0;
}
.views-ui-rearrange-filter-form tr:first-of-type {
  border-top: 0.0625rem solid var(--color-gray-200);
}
.views-ui-rearrange-filter-form tr:not(.draggable):hover {
  background: inherit;
}
.views-ui-rearrange-filter-form .action-links {
  float: left;
  margin: 0 0 1em;
  padding: 0;
}
.views-ui-rearrange-filter-form .tabledrag-toggle-weight-wrapper {
  float: right;
}

.views-ui-rearrange-filter-form .tabledrag-cell {
  position: relative;
}

.views-ui-rearrange-filter-form [id^="views-row"] {
  border: medium none;
}

.views-ui-rearrange-filter-form tr td:last-child {
  border-right: medium none; /* LTR */
}
[dir="rtl"] .views-ui-rearrange-filter-form tr td:last-child {
  border-right: initial;
  border-left: medium none;
}

.views-ui-rearrange-filter-form .filter-group-operator-row {
  border-right: 1px solid transparent !important;
  border-left: 1px solid transparent !important;
}

.views-ui-rearrange-filter-form tr.drag td {
  background-color: #fe7 !important;
}

.views-ui-rearrange-filter-form tr.drag-previous td {
  background-color: #ffb !important;
}

.views-ui-rearrange-filter-form .draggable td {
  vertical-align: middle;
}

/* @end */

/* @group Live preview elements */

.views-query-info pre {
  margin-top: 0;
  margin-bottom: 0;
}

/* @group Query info table */

.views-query-info table {
  border-spacing: 1px;
  border-radius: 7px;
}

.views-query-info table tr td:last-child {
  /* Fixes a Claro style that bleeds down into this table unnecessarily */
  border-right: 0 none; /* LTR */
}
[dir="rtl"] .views-query-info table tr td:last-child {
  border-right: initial;
  border-left: 0 none;
}

/* @end */

/* @end */

/* @group Add view */

.form-item-page-create,
.form-item-block-create {
  margin-top: 13px;
}

/* @end */

/* @group Modal dialog box
 *
 * The contents of the popup dialog on the views edit form.
 */

.filterable-option .form-item.form-type-checkbox {
  padding-top: 4px;
  /* This selector is aggressive because Claro's reset for .form-items is aggressive. */
  padding-bottom: 4px;
  padding-left: 4px; /* LTR */
}
[dir="rtl"] .filterable-option .form-item.form-type-checkbox {
  padding-right: 4px;
  padding-left: 8px;
}

/* @end */

/* @group Grouping styles
 *
 * For grouping related form elements together, mainly used with exposed
 * filters.
 */
.views-config-group-region {
  display: table;
  margin: var(--space-l) 0;
  border: 0.0625rem solid var(--color-gray-200);
  border-collapse: collapse;
}
.views-config-group-region .views-group-box {
  position: relative;
  display: table-cell;
  padding: var(--space-l);
  border: 0.0625rem solid var(--color-gray-200);
}
.views-config-group-region .views-group-box--operator {
  padding-right: var(--space-xl);
  border-right-width: 0;
}
[dir="rtl"] .views-config-group-region .views-group-box--operator {
  padding-right: var(--space-l);
  padding-left: var(--space-xl);
  border-right-width: 0.0625rem;
  border-left-width: 0;
}
.views-config-group-region .views-group-box--value {
  padding: 0;
  border-left-width: 0; /* LTR */
}
[dir="rtl"] .views-config-group-region .views-group-box--value {
  border-right-width: 0;
  border-left-width: 0.0625rem;
}
.views-config-group-region .views-group-box--value > .form-item {
  margin-right: var(--space-l); /* LTR */
  margin-left: var(--space-xl); /* LTR */
}
[dir="rtl"] .views-config-group-region .views-group-box--value > .form-item {
  margin-right: var(--space-xl); /* LTR */
  margin-left: var(--space-l); /* LTR */
}
.views-config-group-region .views-group-box--value > .form-item::before {
  position: absolute;
  top: 0;
  left: 0;
  width: 0.0625rem;
  height: 100%;
  content: "";
  border-left: 0.0625rem solid var(--color-gray-200);
}
[dir="rtl"] .views-config-group-region .views-group-box--value > .form-item::before {
  right: 0;
}
.views-config-group-region .views-group-box--value > .form-item::after {
  position: absolute;
  z-index: 1;
  top: 3.8rem;
  left: calc(var(--space-m) * -1); /* LTR */
  padding: 0 0.3rem 0.3rem 0.4rem; /* LTR */
  content: ">";
  color: var(--color-gray-600);
  border: 0.0625rem solid var(--color-gray-200);
  background: #fff;
  font-size: var(--font-size-h1);
  font-weight: bold;
  line-height: var(--font-size-h1);
}

[dir="rtl"] .views-config-group-region .views-group-box--value > .form-item::after {
  right: calc(var(--space-m) * -1);
  left: auto;
  padding-right: 0.4rem;
  padding-left: 0.3rem;
}
