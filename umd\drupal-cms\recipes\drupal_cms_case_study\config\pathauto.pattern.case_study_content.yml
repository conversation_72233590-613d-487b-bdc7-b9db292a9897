langcode: en
status: true
dependencies:
  enforced:
    config:
      - node.type.case_study
  module:
    - node
id: case_study_content
label: 'Case study content'
type: 'canonical_entities:node'
pattern: '/case-studies/[node:title]'
selection_criteria:
  04baf08e-4b81-48d4-97b5-a915f5cba973:
    id: 'entity_bundle:node'
    negate: false
    uuid: 04baf08e-4b81-48d4-97b5-a915f5cba973
    context_mapping:
      node: node
    bundles:
      case_study: case_study
selection_logic: and
weight: -5
relationships: {  }
