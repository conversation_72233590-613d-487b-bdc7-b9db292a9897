/**
 * @file
 * Claro styles for the System Status general info.
 */

.system-status-general-info {
  padding-block-start: var(--space-m);
}

.system-status-general-info__header {
  --system-status-general-info-header-color: #ccc;

  padding: 2.75rem 0 var(--space-l);
  border-block-start: 1px solid var(--system-status-general-info-header-color);
}

.system-status-general-info__item {
  display: block;
  overflow-x: auto;
  box-sizing: border-box;
  margin-block-end: var(--space-l);
  padding: 18px;
}

.system-status-general-info__item-icon {
  display: inline-block;
  width: 42px;
  height: 42px;
  vertical-align: top;
}
.system-status-general-info__item-icon::before {
  display: block;
  width: 100%;
  height: 100%;
  content: "";
  border-radius: 42px;
  background-color: var(--color-gray-600);
  background-repeat: no-repeat;
  background-position: 50% center;
  background-size: var(--space-l);
}
.system-status-general-info__item-icon--drupal::before {
  background-image: url(../../images/core/ffffff/drupal-logo.svg);
}
.system-status-general-info__item-icon--clock::before {
  background-image: url(../../images/core/ffffff/clock.svg);
}
.system-status-general-info__item-icon--server::before {
  background-image: url(../../images/core/ffffff/server.svg);
}
.system-status-general-info__item-icon--php::before {
  background-image: url(../../images/core/ffffff/php-logo.svg);
  background-size: 32px;
}
.system-status-general-info__item-icon--database::before {
  background-image: url(../../images/core/ffffff/database.svg);
}

@media (forced-colors: active) {
  .system-status-general-info__item-icon::before {
    background-color: canvastext;
    background-image: none;
    mask-repeat: no-repeat;
    mask-position: 50% center;
    mask-size: var(--space-l);
  }
  .system-status-general-info__item-icon--drupal::before {
    mask-image: url(../../images/core/ffffff/drupal-logo.svg);
  }
  .system-status-general-info__item-icon--clock::before {
    mask-image: url(../../images/core/ffffff/clock.svg);
  }
  .system-status-general-info__item-icon--server::before {
    mask-image: url(../../images/core/ffffff/server.svg);
  }
  .system-status-general-info__item-icon--php::before {
    mask-image: url(../../images/core/ffffff/php-logo.svg);
    mask-size: 32px;
  }
  .system-status-general-info__item-icon--database::before {
    mask-image: url(../../images/core/ffffff/database.svg);
  }
}

.system-status-general-info__item-details {
  position: relative;
  display: inline-block;
  box-sizing: border-box;
  width: calc(100% - 60px);
  padding-inline-start: 10px;
  font-size: var(--font-size-s);
}

.system-status-general-info__item-title {
  margin: 0 0 8px;
  font-size: 18px;
}

.system-status-general-info__sub-item-title {
  margin: 14px 0 0;
  font-size: 14px;
}

.system-status-general-info__sub-item__title {
  font-weight: bold;
}
.system-status-general-info__sub-item__value {
  display: block;
}

.system-status-general-info__run-cron {
  margin: 1em 0 0;
}

@media screen and (min-width: 48em) {
  .system-status-general-info__items {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .system-status-general-info__items::after {
    flex: auto;
    content: "";
  }

  .system-status-general-info__item {
    display: block;
    width: 32%;
  }
  .system-status-general-info__item:last-of-type {
    margin-inline-start: 2%;
  }
}

@media screen and (max-width: 48em) {
  .system-status-general-info__header {
    display: none;
  }
  .system-status-general-info {
    margin-block-start: 25px;
    border-block-start: 0;
  }
}
