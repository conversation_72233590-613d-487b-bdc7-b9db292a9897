langcode: en
status: true
dependencies:
  config:
    - image.style.16_9_1008x567_focal_point_webp
    - image.style.16_9_1312x738_focal_point_webp
    - image.style.16_9_1800x1080_focal_point_webp
    - image.style.16_9_512x288_focal_point_webp
    - image.style.16_9_704x396_focal_point_webp
  theme:
    - olivero
id: 16_9_wide
label: '16:9 Wide'
image_style_mappings:
  -
    image_mapping_type: sizes
    image_mapping:
      sizes: 100vw
      sizes_image_styles:
        - 16_9_1008x567_focal_point_webp
        - 16_9_1312x738_focal_point_webp
        - 16_9_1800x1080_focal_point_webp
        - 16_9_512x288_focal_point_webp
        - 16_9_704x396_focal_point_webp
    breakpoint_id: olivero.sm
    multiplier: 1x
breakpoint_group: olivero
fallback_image_style: 16_9_512x288_focal_point_webp
