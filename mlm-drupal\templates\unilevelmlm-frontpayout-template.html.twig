{% include '@unilevelmlm/unilevelmlm-sidebar-template.html.twig' %}
<div class="let-card shadow let-mb-4">
    <div class="let-card-header ump-bg let-py-3">
        <h6 class="let-m-0 let-font-weight-bold ">{{'Payout List' |t}}</h6>
    </div>
    <div class="let-card-body">
        <div class="let-table-responsive">
            <table class="let-table let-table-bordered  let-table-striped" id="payout_dataTable"
                width="100%" cellspacing="0">
                <thead>
                    <tr>
                        <th>{{'# Payout ID' |t}}</th>
                        <th>{{'User Name' |t}}</th>
                        <th>{{'Join Commission' |t}}</th>
                        <th>{{'Referral Commission' |t}}</th>                        
                        <th>{{'Total Amount' |t}}</th>
                        <th>{{'Date' |t}}</th>
                    </tr>
                </thead>
                <tbody>
                    {% if payout_detail %}
                    {% for value in payout_detail %}
                    <tr>
                        <td>{{value.id}}</td>
                        <td>{{current_user_name}}</td>
                        <td>{{value.join_commission_amount}}</td>
                        <td>{{value.referral_commission_amount}}</td>                         
                        <td>{{value.total_commission}}</td>
                        <td>{{value.date}}</td>
                    </tr>
                    {% endfor %}
                    {% else %}
                    <tr>{{'No Data is Availble' |t}}</tr>
                    {% endif %}
                </tbody>
                <tfoot>
                    <tr>
                        <th>{{'# Payout ID' |t}}</th>
                        <th>{{'User Name' |t}}</th>
                        <th>{{'Join Commission' |t}}</th>
                        <th>{{'Referral Commission' |t}}</th>                        
                        <th>{{'Total Amount' |t}}</th>
                        <th>{{'Date' |t}}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>
{% include '@unilevelmlm/unilevelmlm-footer-template.html.twig' %}