langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_description
    - node.type.page
id: node.page.field_description
field_name: field_description
entity_type: node
bundle: page
label: Description
description: 'Describe the page content. This appears as the description in search engine results.'
required: true
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: string_long
