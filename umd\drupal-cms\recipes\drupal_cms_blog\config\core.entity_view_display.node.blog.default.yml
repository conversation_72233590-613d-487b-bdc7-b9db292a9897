langcode: en
status: true
dependencies:
  config:
    - field.field.node.blog.field_content
    - field.field.node.blog.field_description
    - field.field.node.blog.field_featured_image
    - field.field.node.blog.field_tags
    - node.type.blog
    - views.view.blog
  module:
    - layout_builder
    - layout_discovery
    - media
    - text
    - user
    - views
third_party_settings:
  layout_builder:
    enabled: true
    allow_custom: false
    sections:
      -
        layout_id: layout_onecol
        layout_settings:
          label: ''
        components:
          5cde3553-47a9-4254-be42-b8b7ca2025c9:
            uuid: 5cde3553-47a9-4254-be42-b8b7ca2025c9
            region: content
            configuration:
              id: 'field_block:node:blog:field_tags'
              label_display: '0'
              context_mapping:
                entity: layout_builder.entity
              formatter:
                type: entity_reference_label
                label: inline
                settings:
                  link: true
                third_party_settings: {  }
            weight: 5
            additional: {  }
          247faa8e-4645-40cb-9788-ff0e86ba8043:
            uuid: 247faa8e-4645-40cb-9788-ff0e86ba8043
            region: content
            configuration:
              id: 'field_block:node:blog:field_featured_image'
              label: 'Featured image'
              label_display: '0'
              provider: layout_builder
              context_mapping:
                entity: layout_builder.entity
                view_mode: view_mode
              formatter:
                type: entity_reference_entity_view
                label: hidden
                settings:
                  view_mode: hero
                third_party_settings: {  }
            weight: 2
            additional: {  }
          db8679dc-7b6d-4de3-98bd-ce1ea24a3fbe:
            uuid: db8679dc-7b6d-4de3-98bd-ce1ea24a3fbe
            region: content
            configuration:
              id: 'views_block:blog-related'
              label: ''
              label_display: visible
              provider: views
              context_mapping: {  }
              views_label: ''
              items_per_page: none
            weight: 6
            additional: {  }
          774e5cfe-36b2-40ba-a5ff-13c821e3b9af:
            uuid: 774e5cfe-36b2-40ba-a5ff-13c821e3b9af
            region: content
            configuration:
              id: 'extra_field_block:node:blog:content_moderation_control'
              label_display: '0'
              context_mapping:
                entity: layout_builder.entity
            weight: 3
            additional: {  }
          0e9be297-1812-4215-b940-a61b7e808025:
            uuid: 0e9be297-1812-4215-b940-a61b7e808025
            region: content
            configuration:
              id: 'field_block:node:blog:field_content'
              label_display: '0'
              context_mapping:
                entity: layout_builder.entity
              formatter:
                type: text_default
                label: hidden
                settings: {  }
                third_party_settings: {  }
            weight: 4
            additional: {  }
        third_party_settings: {  }
id: node.blog.default
targetEntityType: node
bundle: blog
mode: default
content:
  content_moderation_control:
    settings: {  }
    third_party_settings: {  }
    weight: -20
    region: content
  field_content:
    type: text_default
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: content
  field_featured_image:
    type: media_thumbnail
    label: hidden
    settings:
      image_link: ''
      image_style: ''
      image_loading:
        attribute: eager
    third_party_settings: {  }
    weight: 0
    region: content
  field_tags:
    type: entity_reference_label
    label: inline
    settings:
      link: true
    third_party_settings: {  }
    weight: 1
    region: content
hidden:
  field_description: true
  langcode: true
  layout_builder__layout: true
  links: true
