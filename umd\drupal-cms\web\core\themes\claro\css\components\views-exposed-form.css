/*
 * DO NOT EDIT THIS FILE.
 * See the following change record for more information,
 * https://www.drupal.org/node/3084859
 * @preserve
 */

/**
 * @file
 * Visual styles for views exposed form.
 */

/**
 * Use flexbox and some margin resets to make the fields + actions go inline.
 *
 * For (at least) Media Library, this file is typically inserted by AJAX add_css
 * command when the dialog is opened. The AJAX add_css command always adds the
 * missing-but-required CSS assets to the beginning of the HTML <head>. Because
 * of this, we cannot rely on the expected loading order of the CSS assets.
 * This is why we have to double these selectors: we have to get the expected
 * output even for the Media Library modal.
 *
 * @todo Remove double selectors after https://www.drupal.org/node/1461322
 * has been resolved.
 */

.views-exposed-form.views-exposed-form {
  display: flex;
  flex-wrap: wrap;
  margin-block: var(--space-l);
  padding: var(--space-xs) var(--space-l) var(--space-l) var(--space-l);
  border: var(--details-border-size) solid var(--details-border-color);
  border-radius: var(--details-border-size-radius);
  background-color: var(--color-white);
  box-shadow: var(--details-box-shadow);
}

.views-exposed-form--preview.views-exposed-form--preview {
  margin-top: 0;
}

.views-exposed-form__item.views-exposed-form__item {
  max-width: 100%;
  margin-block: var(--space-s) 0;
  margin-inline: 0 var(--space-xs);
}

.views-exposed-form__item--preceding-actions.views-exposed-form__item--preceding-actions {
  margin-inline-end: var(--space-m);
}

.views-exposed-form__item--actions.views-exposed-form__item--actions .button {
  margin-block: 0;
}

.views-exposed-form__item--actions.views-exposed-form__item--actions .button:last-child {
  margin-inline-end: 0;
}

.views-exposed-form .form-item--no-label,
.views-exposed-form__item.views-exposed-form__item.views-exposed-form__item--actions {
  /* Line height of a label + existing margin for the element + margin for label */
  margin-top: calc(var(--line-height-form-label) + var(--space-s) + var(--space-xs));
}
