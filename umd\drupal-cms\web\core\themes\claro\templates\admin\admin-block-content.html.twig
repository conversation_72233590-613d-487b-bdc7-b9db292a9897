{#
/**
 * @file
 * <PERSON><PERSON><PERSON>'s theme implementation for the content of an administrative block.
 *
 * Available variables:
 * - content: List of administrative menu items. Each menu item contains:
 *   - link: Link to the admin section.
 *   - title: Short name of the section.
 *   - description: Description of the administrative menu item.
 *   - url: URI to the admin section.
 *   - options: URL options. See \Drupal\Core\Url::fromUri() for details.
 * - attributes: HTML attributes to be added to the element.
 * - compact: Boolean indicating whether compact mode is turned on or not.
 *
 * @see template_preprocess_admin_block_content()
 * @see claro_preprocess_admin_block_content()
 */
#}
{%
  set item_classes = [
    'admin-item',
  ]
%}
{% if content %}
  <dl{{ attributes.addClass('admin-list') }}>
    {% for item in content %}
      <div{{ create_attribute({class: item_classes}) }}>
        <dt class="admin-item__title">{{ item.link }}</dt>
        {% if item.description %}
          <dd class="admin-item__description">{{ item.description }}</dd>
        {% endif %}
      </div>
    {% endfor %}
  </dl>
{% endif %}
