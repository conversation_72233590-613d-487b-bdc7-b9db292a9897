/*
 * DO NOT EDIT THIS FILE.
 * See the following change record for more information,
 * https://www.drupal.org/node/3084859
 * @preserve
 */

/**
 * @file
 * Maintenance theming.
 *
 * These styles affect the installation UI, maintenance page, and the database update UI.
 */

.maintenance-page {
  min-height: 100%;
  background-color: var(--color-gray-100);
}

h1,
h2 {
  font-size: var(--font-size-h3);
}

h3 {
  font-size: var(--font-size-h4);
}

.site-name {
  margin-top: var(--space-s);
  word-wrap: break-word;
  color: var(--color-gray);
  font-size: var(--font-size-xl);
}

.site-name--subtle {
  margin-top: var(--space-s);
  color: var(--color-gray-800);
  font-size: var(--font-size-h4);
}

.title {
  margin-top: 0;
  font-size: var(--font-size-h3);
}

.title--broad {
  font-size: var(--font-size-h1);
}

.site-name,
.title {
  margin-bottom: var(--space-l);
}

.content {
  margin-bottom: var(--space-m);
  color: var(--color-gray);
}

.content--subtle {
  color: var(--color-gray-800);
}

.site-name,
.title,
.content {
  max-width: 34rem;
}

/**
 * Task list & step indicator.
 * Shows on both the install screen and the database update page.
 */

@media all and (max-width: 48em) {
  /* 768px */
  header[role="banner"] {
    position: relative;
  }
  .step-indicator {
    position: absolute;
    top: 0.2em;
    right: 0.5em; /* LTR */
    display: block;
    font-size: 1.385em;
  }
  [dir="rtl"] .step-indicator {
    right: auto;
    left: 0.5em;
  }
}

@media all and (min-width: 48em) {
  /* 768px */
  .step-indicator {
    display: none;
  }
  .task-list {
    margin-left: -3rem; /* LTR */
    padding: 0;
    list-style-type: none;
    counter-reset: steps;
    font-weight: bold;
  }
  [dir="rtl"] .task-list {
    margin-right: -3rem;
    margin-left: 0;
  }
  .task-list li {
    padding: 1rem 1rem 1rem 3rem; /* LTR */
    counter-increment: steps;
    color: var(--color-gray);
    border-radius: 0 2rem 2rem 0 /* LTR */;
    font-weight: 500;
  }
  [dir="rtl"] .task-list li {
    padding: 1rem 3rem 1rem 1rem;
    border-radius: 2rem 0 0 2rem;
  }
  .task-list li::before {
    margin-right: 0.375rem;
    content: counter(steps);
  }
  [dir="rtl"] .task-list li::before {
    margin-right: 0;
    margin-left: 0.375rem;
  }

  .task-list .is-active {
    color: var(--color-absolutezero);
    border: 1px solid transparent; /* Required for forced-colors mode. */
    background: var(--color-bgblue-active);
    font-weight: 500;
  }
  .task-list .done {
    color: var(--color-gray-700);
  }
}

/**
 * Layout
 */

.layout-container {
  width: auto;
  margin-right: 1.25em;
  margin-left: 1.25em;
  padding: var(--space-xl);
  border: 1px solid transparent; /* Required for forced-colors mode. */
  border-radius: 0.5rem;
  background: #fff;
  box-shadow: var(--shadow-z3);
}

.layout-container::after {
  /* No reason for a clearfix in the markup. */
  display: table;
  clear: both;
  content: "";
}

@media all and (max-width: 48em) {
  /* 768px */
  .layout-container {
    margin: 1.25em;
    padding: var(--space-m) var(--space-l);
  }

  .layout-sidebar-first {
    display: none;
  }
}

@media all and (min-width: 48em) {
  /* 768px */
  html {
    display: table;
  }
  .install-page,
  .maintenance-page {
    display: table-cell;
    padding: 1em 0;
    vertical-align: middle;
  }
  html,
  .install-page,
  .maintenance-page {
    width: 100%;
    height: 100%;
    margin: 0;
  }
  /**
   * Needed to override rules from system-admin--layout.pcss.
   */
  .layout-container.layout-container.layout-container {
    width: 75%;
    max-width: 58.75rem;
    margin: 0 auto;
  }
  ul {
    margin: 0.25em 0;
    padding: 0.9375rem;
  }
  [dir="rtl"] ul {
    margin-right: 0; /* Overrides default [dir="rtl"] ul margin. */
  }

  /* Positioning sidebar & content. */
  .layout-sidebar-first {
    float: left; /* LTR */
    width: 35%;
  }
  [dir="rtl"] .layout-sidebar-first {
    float: right;
  }

  .main-content {
    float: left; /* LTR */
    clear: none;
    box-sizing: border-box;
    width: 65%;
    padding-left: 3.85em; /* LTR */
  }
  [dir="rtl"] .main-content {
    float: right;
    padding-right: 3.85em;
    padding-left: 0;
  }
  .main-content--attached {
    padding-left: 0;
  }
}

/**
 * Status report customization for install and update page.
 */

.system-status-report__status-title {
  float: none;
  width: 100%;
}

.system-status-report__entry__value {
  float: none;
  width: 100%;
  padding-top: 0;
  padding-left: 3em; /* LTR */
}

[dir="rtl"] .system-status-report__entry__value {
  padding-right: 3em;
  padding-left: 1em;
}

/**
 * Updates list customization for update page.
 */

.update-results {
  overflow: auto; /* Required to prevent text clipping. */
  margin-top: 0;
  padding: var(--space-s);
  border: 1px solid var(--color-gray-100);
  border-radius: var(--base-border-radius);
  background: var(--color-gray-050);
}

.update-results > h3:first-child {
  margin-top: 0;
}

.update-results .failure strong {
  color: var(--color-maximumred);
}

.item-list > h3 {
  font-size: var(--font-size-h5);
}

.item-list > ul {
  margin-left: 0;
}
