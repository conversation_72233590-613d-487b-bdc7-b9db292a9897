{% include '@unilevelmlm/unilevelmlm-sidebar-template.html.twig' %}
<div class="let-loader-layer" id="user_request_amount_loader">
    <div class="let-pic-loader"></div>
</div>
<div class="let-container">
    <div class="let-col-md-12 let-row">
        <div class="let-col-md-6 ">
            <div class="let-card shadow let-mb-4">
                <div class="let-card-header ump-bg let-py-3">
                    {{'Earning Summary' |t}}
                </div>
                <div class="let-card-body">
                    <div class="let-table-responsive">
                        <table class="let-table let-table-striped let-table-bordered">
                            <tbody>
                                <tr>
                                    <th> {{'Total Amount' |t}} </th>
                                    <td>{{total_amount}}</td>
                                </tr>
                                <tr>
                                    <th>{{'Available Amount' |t}}</th>
                                    <td>{{remaining_balance}}</td>
                                </tr>
                                <tr>
                                    <th>{{'Processed Amount' |t}} </th>
                                    <td>{{processed_amount}}</td>
                                </tr>
                                <tr>
                                    <th>{{'Pending Amount' |t}} </th>
                                    <td>{{pending_amount}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="let-col-md-6 earning_summary">
            <div class="let-card shadow let-mb-4">
                <div class="let-card-header ump-bg let-py-3">
                    {{'Withdraw Amount' |t}}
                </div>
                <div class="let-card-body">
                    <div class="let-table-responsive">
                        <table class="let-table let-table-striped  let-table-bordered">
                            <tbody>
                                <tr>
                                    <td>{{'Account Balance' |t}}
                                    </td>
                                    <td>{{total_amount}}
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        {{'Minimum Withdrawal' |t}}
                                    </td>
                                    <td>
                                        {{min_limit}}
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        {{'Maximum Withdrawal' |t}}
                                    </td>
                                    <td>
                                        {{max_limit}}
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        {{'Remaining Balance' |t}}
                                    </td>
                                    <td> {{remaining_balance}}</td>
                                </tr>
                                <tr>
                                    <td> {{'Requested Amount' |t}}
                                    </td>
                                    <td> {{pending_amount}}
                                    </td>
                                </tr>
                                {% if total_amount is not empty %}
                                <tr>
                                    <td>
                                        {{'Enter Amount' |t}}
                                    </td>
                                    <td class="let-text-center">

                                        <input type="number" class="let-form-control" name="withdrawal_amount"
                                            id="withdrawal_amount" class="let-text-center amount_styl">
                                    </td>
                                </tr>
                                <tr>
                                    <td class="let-text-center" colspan="2">
                                        <button id="ump_withdrwal_request" type="submit"
                                            onclick="ump_withdrwal_request();return false;"
                                            class="let-btn let-btn-sm let-btn-info">{{'Withdraw' |t}}</button>
                                    </td>
                                </tr>
                                {% endif %}
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% include '@unilevelmlm/unilevelmlm-footer-template.html.twig' %}