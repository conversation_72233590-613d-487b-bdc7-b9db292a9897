langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
    - field.field.node.event.field_content
    - field.field.node.event.field_description
    - field.field.node.event.field_event__date
    - field.field.node.event.field_event__file
    - field.field.node.event.field_event__link
    - field.field.node.event.field_event__location_address
    - field.field.node.event.field_event__location_name
    - field.field.node.event.field_featured_image
    - field.field.node.event.field_geofield
    - field.field.node.event.field_tags
    - node.type.event
  module:
    - smart_date
    - user
id: node.event.teaser
targetEntityType: node
bundle: event
mode: teaser
content:
  field_description:
    type: basic_string
    label: hidden
    settings: {  }
    third_party_settings: {  }
    weight: 2
    region: content
  field_event__date:
    type: smartdate_default
    label: hidden
    settings:
      timezone_override: ''
      format_type: medium
      format: default
      force_chronological: false
      add_classes: false
      time_wrapper: true
      localize: false
      parts:
        - start
        - end
      duration:
        separator: ' | '
        unit: ''
        decimals: 2
        suffix: h
    third_party_settings: {  }
    weight: 1
    region: content
  field_featured_image:
    type: entity_reference_entity_view
    label: hidden
    settings:
      view_mode: small
      link: false
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  content_moderation_control: true
  field_content: true
  field_event__file: true
  field_event__link: true
  field_event__location_address: true
  field_event__location_name: true
  field_geofield: true
  field_tags: true
  langcode: true
  links: true
