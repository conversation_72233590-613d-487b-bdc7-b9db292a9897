/**
 * @file media-library.pcss.css

 * Styling for Media Library.
 */

/**
 * Negative margins compensate for modal dialog padding and compensate for
 * positioning that would otherwise hide the active tab indicator on the left.
 */
.media-library-wrapper {
  display: flex;
  margin: -1rem -1.5rem -1rem -1rem; /* LTR */
}
[dir="rtl"] .media-library-wrapper {
  margin-right: -1em;
  margin-left: -1.5em;
}

.media-library-wrapper .messages-list,
.media-library-wrapper .messages {
  margin-top: 0;
  margin-bottom: var(--space-l);
}

.media-library-menu {
  position: relative;
  display: block;
  width: var(--vertical-tabs-menu-width);
  margin: 0;
  padding-top: var(--vertical-tabs-menu-item-shadow-extraspace);
  list-style: none;
  color: var(--color-text);
}
[dir="rtl"] .media-library-menu {
  margin: 0;
}

.media-library-menu__item {
  overflow: hidden;
  margin: var(--vertical-tabs-menu-item--top-margin) var(--vertical-tabs-menu-item--right-margin) var(--vertical-tabs-menu-item--bottom-margin) var(--vertical-tabs-menu-item--left-margin); /* LTR */
  padding: var(--vertical-tabs-menu-item-shadow-extraspace) 0;
}

[dir="rtl"] .media-library-menu__item {
  margin-right: var(--vertical-tabs-menu-item--left-margin);
  margin-left: var(--vertical-tabs-menu-item--right-margin);
}

.media-library-menu__item::before {
  z-index: var(--vertical-tabs-menu--z-index); /* The line should be kept above the vertical tabs menu link to keep it visible even if the link is hovered and gets the 'hover' background color. */
  display: block;
  width: 100%;
  margin-top: calc(var(--vertical-tabs-menu-separator-size) * -1);
  content: "";
  border-top: var(--vertical-tabs-menu-separator-size) solid var(--vertical-tabs-menu-separator-color);
}

.media-library-menu__link {
  position: relative;
  display: block;
  margin-top: calc(var(--vertical-tabs-border-size) * -1);
  padding: var(--space-s) var(--space-s) var(--space-s) calc(var(--space-l) - var(--vertical-tabs-menu-link--active-border-size)); /* LTR */
  text-decoration: none;
  color: var(--color-text);
  border: var(--vertical-tabs-border-size) solid transparent;
  border-width: var(--vertical-tabs-border-size) 0 var(--vertical-tabs-border-size) var(--vertical-tabs-menu-link--active-border-size); /* LTR */
  border-radius: var(--vertical-tabs-border-radius) 0 0 var(--vertical-tabs-border-radius); /* LTR */
}

[dir="rtl"] .media-library-menu__link {
  padding-right: calc(var(--space-l) - var(--vertical-tabs-menu-link--active-border-size));
  padding-left: var(--space-s);
  border-width: var(--vertical-tabs-border-size) var(--vertical-tabs-menu-link--active-border-size) var(--vertical-tabs-border-size) 0;
  border-radius: 0 var(--vertical-tabs-border-radius) var(--vertical-tabs-border-radius) 0;
}

/* Menu link states. */
.media-library-menu__link:focus {
  z-index: calc(var(--vertical-tabs-menu--z-index) + 3); /* Focus state should be on the highest level to make the focus effect be fully visible. This also means that it should have bigger z-index than the selected link. */
  text-decoration: none;
  box-shadow: none;
}

.media-library-menu__link:hover {
  text-decoration: none;
  color: var(--color-absolutezero);
  /* These borders are necessary to replace the dividing lines while in the hover state. */
  border-top: var(--vertical-tabs-menu-separator-size) solid var(--vertical-tabs-menu-separator-color);
  border-bottom: var(--vertical-tabs-menu-separator-size) solid var(--vertical-tabs-menu-separator-color);
  background: var(--color-bgblue-hover);
}

/* This pseudo element provides the background for the hover state. */
.media-library-menu__link::before {
  position: absolute;
  z-index: calc(var(--vertical-tabs-menu--z-index) - 1); /* This should be on a lower level than the menu-item separator lines. */
  top: calc(var(--vertical-tabs-border-size) * -1);
  right: 0; /* LTR */
  bottom: calc(var(--vertical-tabs-border-size) * -1);
  left: calc(var(--vertical-tabs-menu-link--active-border-size) * -1); /* LTR */
  content: "";
  pointer-events: none;
  background-clip: padding-box;
}
[dir="rtl"] .media-library-menu__link::before {
  right: calc(var(--vertical-tabs-menu-link--active-border-size) * -1);
  left: 0;
}

.media-library-menu__link:focus::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: calc(var(--vertical-tabs-border-size) * -1) calc(var(--vertical-tabs-menu-link--active-border-size) * -1);
  content: "";
  pointer-events: none;
  border: var(--vertical-tabs-menu-link-focus-border-size) solid var(--color-focus);
  border-radius: var(--vertical-tabs-border-radius);
}

.media-library-menu__link.active {
  z-index: calc(var(--vertical-tabs-menu--z-index) + 2); /* The selected menu link should be on a higher level than the white masking line that hides the gray separator. */
  color: var(--color-absolutezero);
  border-color: var(--vertical-tabs-border-color) transparent;
  background-color: var(--color-white);
  box-shadow: var(--vertical-tabs-shadow);
}

.media-library-menu__link.active:hover {
  color: var(--color-absolutezero-hover);
  background-color: var(--color-bgblue-hover);
}

.media-library-menu__link.active::before {
  z-index: 1; /* The blue active-tab indication should be on a higher level than the green focus border. */
  border-left: var(--vertical-tabs-menu-link--active-border-size) solid var(--vertical-tabs-menu-link--active-border-color); /* LTR */
  border-radius: var(--base-border-radius) 0 0 var(--base-border-radius); /* LTR */
}
[dir="rtl"] .media-library-menu__link.active::before {
  border-right: var(--vertical-tabs-menu-link--active-border-size) solid var(--vertical-tabs-menu-link--active-border-color);
  border-left: 0;
  border-radius: 0 var(--base-border-radius) var(--base-border-radius) 0;
}

.media-library-menu__link.active:hover::before {
  background: none;
}

.media-library-content {
  width: 100%;
  padding: 1em;
  outline: none;
}

.media-library-menu + .media-library-content {
  z-index: var(--vertical-tabs-menu--z-index);
  border-left: var(--vertical-tabs-border); /* LTR */
  box-shadow: var(--vertical-tabs-shadow);
}

[dir="rtl"] .media-library-menu + .media-library-content {
  border-right: var(--vertical-tabs-border);
  border-left: 0;
}

/* Generic media add form styles. */
.media-library-add-form--without-input .form-item {
  margin-right: 1rem;
}

/**
 * Remove outline from added media list.
 *
 * The added media list receives focus after adding new media, but since it is
 * not an interactive element, it does not need an outline.
 */
.media-library-add-form__added-media {
  margin: 0;
  padding: 0;
  outline: none;
}

/**
 * This Media Library form is an exception to the extrasmall button pattern.
 * Additional padding is needed to accommodate the remove button icon. The
 * margin is adjusted for alignment within the media library dialog.
 */
.media-library-add-form__added-media .media-library-add-form__remove-button.button--extrasmall {
  margin: var(--space-xs) 0; /* LTR */
  /* Left padding is double the background size of the button icon. */
  padding: calc(calc(var(--space-xs) / 2) - 1px) calc(var(--space-s) - 1px) calc(calc(var(--space-xs) / 2) - 1px) calc(var(--space-s) * 2);
}

/* This is needed to override the default extrasmall button left margin. */
[dir="rtl"] .media-library-add-form__added-media .media-library-add-form__remove-button.button--extrasmall {
  margin-left: 0;
}

.media-library-add-form__input-wrapper {
  padding: var(--space-xs) var(--space-l) var(--space-l) var(--space-l);
  border: var(--details-border-size) solid var(--details-border-color);
  border-radius: var(--base-border-radius);
  background-color: var(--color-white);
  box-shadow: var(--details-box-shadow);
}

/* Style the media add upload form. */
.media-library-add-form--upload.media-library-add-form--without-input .form-item-upload {
  margin-bottom: 0;
}
.media-library-add-form--upload.media-library-add-form--with-input .form-managed-file__main,
.media-library-add-form--upload.media-library-add-form--with-input .form-managed-file.no-upload {
  display: block;
}

/* Adjust the focus border on this element so it is not too close to buttons. */
.media-library-add-form__added-media:focus {
  box-shadow:
    0 0 0 calc(var(--focus-border-offset-size) + 2px) var(--color-white),
    0 0 0 calc(var(--focus-border-size) + var(--focus-border-offset-size) + 2px) var(--color-focus);
}

.media-library-add-form .file-upload-help {
  margin: 8px 0 0;
}

/* Style the media add oEmbed form. */
.media-library-add-form--oembed .media-library-add-form__input-wrapper {
  display: flex;
  align-items: center;
}

@media screen and (max-width: 37.5em) {
  .media-library-add-form--oembed .media-library-add-form__input-wrapper {
    display: block;
  }
}

.media-library-add-form--oembed.media-library-add-form--without-input .form-item-url {
  margin-bottom: 0;
}

.media-library-add-form-oembed-url {
  width: 100%;
}

/* Media add form selection styles. */
.media-library-add-form__selected-media {
  margin-top: 1em;
}

/* Change to padding to account for the negative margin for flex grid. */
.media-library-add-form__selected-media .details-wrapper {
  padding: 0 10px 1em 10px;
}

.media-library-add-form__selected-media .media-library-item .field--name-thumbnail img {
  height: 100px;
}

/* Generic media library view styles. */
.media-library-select-all {
  flex-basis: 100%;
  width: 100%;
  margin: 10px 8px;
}
.media-library-select-all input {
  margin-right: 10px;
}
[dir="rtl"] .media-library-select-all input {
  margin-left: 10px;
}

.media-library-views-form,
.media-library-selection,
.media-library-add-form__selected-media .details-wrapper,
.media-library-view .form--inline {
  display: flex;
  flex-wrap: wrap;
}

.media-library-views-form > .form-actions,
.media-library-views-form > .field-actions {
  flex-basis: 100%;
}

.media-library-views-form__header {
  flex-basis: 100%;
}

.media-library-views-form__header .form-item {
  margin-right: 8px; /* @TODO RTL? */
}

.media-library-views-form__rows {
  display: flex;
  flex-wrap: wrap;
  flex-basis: 100%;
  margin: 0 -8px;
}

/**
 * Override the table display of the visually hidden labels.
 *
 * The width, height and overflow properties in the styles for the
 * .visually-hidden class do not work correctly if the element has a table
 * display.
 */
.media-library-item label {
  display: inline-block;
}

/* Media library widget view styles. */
.media-library-wrapper .media-library-view {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.media-library-wrapper .view-header {
  align-self: flex-end;
  margin: 1em 0;
  text-align: right; /* LTR */
}
[dir="rtl"] .media-library-wrapper .view-header {
  text-align: left;
}

.media-library-wrapper .media-library-view .view-filters,
.media-library-wrapper .media-library-view .view-content {
  flex: 0 0 100%;
}

.media-library-wrapper .views-display-link {
  margin: 0;
  padding-left: 22px; /* LTR */
  color: #333;
  font-size: 15px;
  line-height: 16px;
}
[dir="rtl"] .media-library-wrapper .views-display-link {
  padding-right: 22px;
  padding-left: 0;
}

.media-library-wrapper .views-display-link.is-active {
  font-weight: bold;
}

.media-library-wrapper .views-display-link-widget {
  margin-right: 15px;
  background: url(../../../../misc/icons/333333/grid.svg) left 0 no-repeat; /* LTR */
}
[dir="rtl"] .media-library-wrapper .views-display-link-widget {
  background-position: right 0;
}

.media-library-wrapper .views-display-link-widget_table {
  background: url(../../../../misc/icons/333333/table.svg) left 0 no-repeat; /* LTR */
}
[dir="rtl"] .media-library-wrapper .views-display-link-widget_table {
  background-position: right 0;
}

/**
 * Style the media library grid items.
 */
.media-library-item,
.media-library-item__preview-wrapper {
  position: relative;
}

/**
 * Ajax throbbers inside a media library item.
 */
.media-library-item .ajax-progress.ajax-progress.ajax-progress {
  position: absolute;
  z-index: calc(var(--vertical-tabs-menu--z-index) + 1);
  top: 50%;
  left: 50%;
  box-sizing: border-box;
  width: 3rem; /* 56px */
  height: 3rem;
  margin: -1.5rem;
  border: var(--input-border-size) solid var(--jui-dropdown-border-color);
  border-radius: 3.5rem;
  background: var(--color-white);
  box-shadow: 0 0.25rem 0.625rem var(--jui-dropdown-shadow-color);
}
.media-library-item .ajax-progress__throbber {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 1.75rem;
  height: 1.75rem;
  margin: -0.875rem;
  border: 3px solid var(--color-absolutezero);
  border-right: 3px dotted transparent;
}
.media-library-item .ajax-progress__message {
  display: none;
}

.media-library-item--grid {
  justify-content: center;
  box-sizing: border-box;
  width: 50%;
  padding: 8px;
  vertical-align: top;
  outline: none;
  background: #fff;
}

.media-library-item--grid::before {
  position: absolute;
  top: 7px;
  left: 7px;
  width: calc(100% - 16px);
  height: calc(100% - 16px);
  content: "";
  transition:
    border-color 0.2s,
    color 0.2s,
    background 0.2s;
  pointer-events: none;
  border: 1px solid #dbdbdb;
  border-radius: 2px;
  box-shadow: var(--details-box-shadow);
}

/**
* The media library grid item focus border is moved to a child element to
* improve padding.
*/
.media-library-item--grid:focus {
  outline: none;
  box-shadow: none;
}

.media-library-item--grid:focus .media-library-item__preview-wrapper {
  outline: var(--focus-outline);
  box-shadow: var(--focus-box-shadow);
}

/* Media library widget weight field styles. */
.media-library-item--grid .form-item {
  margin: 0.75em;
}

/* The selected items in the add form should be shown a bit smaller. */
.media-library-add-form__selected-media .media-library-item--small {
  width: 33.3%;
}

.media-library-widget-modal .ui-dialog-buttonpane {
  display: flex;
  align-items: center;
}

.media-library-widget-modal .ui-dialog-buttonpane .form-actions,
.media-library-widget-modal .ui-dialog-buttonpane .field-actions {
  flex: 1;
}

/**
 * By default, the dialog is too narrow to be usable.
 * @see Drupal.ckeditor5.openDialog()
 */
.ui-dialog--narrow.media-library-widget-modal {
  max-width: 75%;
}

@media screen and (min-width: 45em) {
  .media-library-item--grid {
    width: 33.3%;
  }

  /* Change the width for the modal and widget since there is less space. */
  .media-library-widget-modal .media-library-item--grid,
  .media-library-selection .media-library-item--grid {
    width: 50%;
  }

  /* The selected items in the add form should be shown a bit smaller. */
  .media-library-add-form__selected-media .media-library-item--small {
    width: 25%;
  }
}

@media screen and (min-width: 60em) {
  .media-library-item--grid {
    width: 25%;
  }

  /* Change the width for the modal and widget since there is less space. */
  .media-library-widget-modal .media-library-item--grid,
  .media-library-selection .media-library-item--grid {
    width: 33.3%;
  }

  /* The selected items in the add form should be shown a bit smaller. */
  .media-library-add-form__selected-media .media-library-item--small {
    width: 16.6%;
  }
}

@media screen and (min-width: 77em) {
  .media-library-item--grid {
    width: 16.6%;
  }

  /* Change the width for the modal and widget since there is less space. */
  .media-library-widget-modal .media-library-item--grid,
  .media-library-selection .media-library-item--grid {
    width: 25%;
  }

  /* The selected items in the add form should be shown a bit smaller. */
  .media-library-add-form__selected-media .media-library-item--small {
    width: 16.6%;
  }
}

.media-library-item--grid .field--name-thumbnail {
  overflow: hidden;
  text-align: center;
  background-color: #ebebeb;
}

.media-library-item--grid .field--name-thumbnail img {
  height: 180px;
  object-fit: contain;
  object-position: center center;
}

.media-library-item--grid.is-hover::before,
.media-library-item--grid.checked::before,
.media-library-item--grid.is-focus::before {
  top: 5px;
  left: 5px;
  border-width: 3px;
  border-radius: 3px;
}
.media-library-item--grid.is-hover::before,
.media-library-item--grid.checked.is-hover::before {
  border-color: var(--color-absolutezero-hover);
}

.media-library-item--grid.is-focus::before {
  border-color: var(--color-focus);
}

.media-library-item--grid.checked::before {
  border-color: var(--button--focus-border-color);
}

.media-library-item--grid .form-boolean--type-checkbox:checked {
  border-color: var(--button--focus-border-color);
  background-color: var(--button--focus-border-color);
}

.media-library-item__click-to-select-checkbox {
  position: absolute;
  z-index: 1;
  top: 16px;
  left: 16px; /* LTR */
  display: block;
}
[dir="rtl"] .media-library-item__click-to-select-checkbox {
  right: 16px;
  left: auto;
}

.media-library-item__click-to-select-checkbox input {
  width: 20px;
  height: 20px;
}

.media-library-item__click-to-select-checkbox .form-item {
  margin: 0;
}

.media-library-item__click-to-select-trigger {
  overflow: hidden;
  height: 100%;
  cursor: pointer;
}

/* Media library item table styles. */
.media-library-item--table img {
  max-width: 100px;
  height: auto;
}

/* Media library entity view display styles. */
.media-library-item__preview {
  padding-bottom: 34px;
}

.field--widget-media-library-widget .media-library-item__preview {
  cursor: move;
}

.field--widget-media-library-widget .media-library-item__preview img {
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
}

.field--widget-media-library-widget .js-media-library-item:only-child .media-library-item__preview {
  cursor: inherit;
}

.media-library-item__status {
  position: absolute;
  top: 40px;
  left: 5px; /* LTR */
  padding: 5px 10px;
  pointer-events: none;
  color: #e4e4e4;
  background: #666;
  font-size: 12px;
  font-style: italic;
}
[dir="rtl"] .media-library-item__status {
  right: 5px;
  left: auto;
}

.media-library-item__attributes {
  position: absolute;
  bottom: 0;
  display: block;
  overflow: hidden;
  max-width: calc(100% - 10px);
  max-height: calc(100% - 50px);
  padding: 5px;
  background: white;
}

.media-library-item__name {
  display: block;
  overflow: hidden;
  margin: calc(var(--space-xs) / 2) var(--space-xs);
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 14px;
}

.media-library-item__attributes:hover .media-library-item__name,
.media-library-item--grid.is-focus .media-library-item__name,
.media-library-item--grid.checked .media-library-item__name {
  white-space: normal;
}

.media-library-item__type {
  color: #696969;
  font-size: 12px;
}

.media-library-item--disabled {
  pointer-events: none;
  opacity: 0.5;
}

/* Media library widget styles. */
.media-library-widget {
  position: relative;
}

.media-library-widget__toggle-weight.media-library-widget__toggle-weight {
  position: absolute;
  top: var(--space-s);
  right: var(--space-m); /* LTR */
  text-decoration: none;
}
[dir="rtl"] .media-library-widget__toggle-weight.media-library-widget__toggle-weight {
  right: auto;
  left: var(--space-m);
}

/* Add negative margin for flex grid. */
.media-library-selection {
  margin: 1em -8px;
}

/**
 * Media library widget edit and delete button styles.
 *
 * We have to override the .button styles since buttons make heavy use of
 * background and border property changes.
 */
.media-library-item__edit,
.media-library-item__edit:hover,
.media-library-item__edit:focus,
.media-library-item__remove,
.media-library-item__remove:hover,
.media-library-item__remove:focus,
.media-library-item__remove.button,
.media-library-item__remove.button:first-child,
.media-library-item__remove.button:disabled,
.media-library-item__remove.button:disabled:active,
.media-library-item__remove.button:hover,
.media-library-item__remove.button:focus {
  position: absolute;
  z-index: 1;
  top: 10px;
  overflow: hidden;
  width: 24px;
  height: 24px;
  margin: 5px;
  padding: 0;
  transition: 0.2s border-color;
  color: transparent;
  background-size: 12px;
  text-shadow: none;
  font-size: 0;
}

.media-library-item__edit {
  right: 40px; /* LTR */
}
[dir="rtl"] .media-library-item__edit {
  right: auto;
  left: 40px;
}

.media-library-item__remove {
  right: 10px; /* LTR */
}
[dir="rtl"] .media-library-item__remove {
  right: auto;
  left: 10px;
}

.media-library-item__edit {
  /* !important to override button class border. */
  border: 1px solid var(--color-gray-200) !important;
  background-image: url("../../images/icons/545560/pencil.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 12px;
}
.media-library-item__edit:active {
  background-image: url("../../../../misc/icons/ffffff/pencil.svg");
}
.media-library-item__remove,
.media-library-item__remove.button,
.media-library-item__remove.button:first-child,
.media-library-item__remove.button:disabled,
.media-library-item__remove.button:disabled:active,
.media-library-item__remove.button:hover,
.media-library-item__remove.button:focus {
  /* !important to override button class border. */
  border: 1px solid var(--color-gray-200) !important;
  background-image: url("../../../../misc/icons/545560/ex.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 12px;
}

.media-library-item__remove:active,
.media-library-item__remove.button:active,
.media-library-item__remove.button:disabled:active {
  /* !important to override button class border. */
  border-color: var(--color-absolutezero) !important;
  background-image: url("../../../../misc/icons/ffffff/ex.svg");
}

/* Style the added media item container. */
.media-library-add-form__media {
  position: relative;
  display: flex;
  border-bottom: 1px solid #c0c0c0;
}

/* Do not show the bottom border and padding for the last item. */
.media-library-add-form__media:last-child {
  padding-bottom: 0;
  border-bottom: 0;
}

.media-library-add-form__preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 220px;
  margin-right: 20px; /* LTR */
  background: var(--color-gray-050-o-40);
}
[dir="rtl"] .media-library-add-form__preview {
  margin-right: 0;
  margin-left: 20px;
}

.media-library-add-form__fields {
  flex-grow: 1;
}

.media-library-add-form__remove-button {
  position: absolute;
  right: 0; /* LTR */
  background-image: url("../../../../misc/icons/000000/ex.svg");
  background-repeat: no-repeat;
  background-position: var(--space-xs) center;
  background-size: var(--space-s);
}
[dir="rtl"] .media-library-add-form__remove-button {
  right: auto;
  left: 0;
}
.media-library-add-form__remove-button.button:disabled {
  background-image: url("../../../../misc/icons/8e929c/ex.svg");
}

.views-live-preview .media-library-view div.views-row + div.views-row {
  margin-top: 0;
}
