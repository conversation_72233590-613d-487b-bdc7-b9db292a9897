<div class="unilevelmlm-wallet">
  <h2>Wallet Management</h2>

  <div class="wallet-balance">
    <h3>Wallet Balance</h3>
    <p>{{ wallet_balance }}</p>
  </div>

  <div class="transaction-history">
    <h3>Transaction History</h3>
    <ul>
      {% for transaction in transaction_history %}
        <li>{{ transaction.date }} - {{ transaction.type }} - {{ transaction.amount }}</li>
      {% endfor %}
    </ul>
  </div>

  <div class="wallet-actions">
    <h3>Actions</h3>
    <button>Deposit</button>
    <button>Withdraw</button>
    <button>Transfer</button>
  </div>

  <script>
    (function ($) {
      $(document).ready(function () {
        $('.wallet-actions button:nth-child(1)').click(function () { // Deposit
:start_line:29
-------
          var amount = prompt("Enter amount to deposit:", "0.00");
          if (amount) {
            $.ajax({
              url: '/unilevelmlm/deposit',
              method: 'POST',
              contentType: 'application/json',
              data: JSON.stringify({ amount: amount }),
              success: function (response) {
:start_line:38
-------
                if (response.status === 'success') {
                  Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: response.message,
                    timer: 1500,
                    showConfirmButton: false
                  }).then(function() {
                    location.reload();
                  });
                } else {
                  Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: response.message
                  });
                }
:start_line:45
-------
              },
              error: function (jqXHR, textStatus, errorThrown) {
                Swal.fire({
                  icon: 'error',
                  title: 'Error!',
                  text: 'Error: ' + textStatus + ' - ' + errorThrown
                });
              }
            });
          }
        });

        $('.wallet-actions button:nth-child(2)').click(function () { // Withdraw
:start_line:52
-------
          var amount = prompt("Enter amount to withdraw:", "0.00");
          if (amount) {
            $.ajax({
              url: '/unilevelmlm/withdraw',
              method: 'POST',
              contentType: 'application/json',
              data: JSON.stringify({ amount: amount }),
              success: function (response) {
:start_line:61
-------
                if (response.status === 'success') {
                  Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: response.message,
                    timer: 1500,
                    showConfirmButton: false
                  }).then(function() {
                    location.reload();
                  });
                } else {
                  Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: response.message
                  });
                }
:start_line:68
-------
              },
              error: function (jqXHR, textStatus, errorThrown) {
                Swal.fire({
                  icon: 'error',
                  title: 'Error!',
                  text: 'Error: ' + textStatus + ' - ' + errorThrown
                });
              }
            });
          }
        });

        $('.wallet-actions button:nth-child(3)').click(function () { // Transfer
:start_line:75
-------
          var recipient = prompt("Enter recipient user ID:", "");
          if (recipient) {
            var amount = prompt("Enter amount to transfer:", "0.00");
            if (amount) {
              $.ajax({
                url: '/unilevelmlm/transfer',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ recipient: recipient, amount: amount }),
                success: function (response) {
                  if (response.status === 'success') {
:start_line:86
-------
                    Swal.fire({
                      icon: 'success',
                      title: 'Success!',
                      text: response.message,
                      timer: 1500,
                      showConfirmButton: false
                    }).then(function() {
                      location.reload();
                    });
                  } else {
                    Swal.fire({
                      icon: 'error',
                      title: 'Error!',
                      text: response.message
                    });
                  }
:start_line:93
-------
                },
                error: function (jqXHR, textStatus, errorThrown) {
                  Swal.fire({
                    icon: 'error',
                    title: 'Error!',
                    text: 'Error: ' + textStatus + ' - ' + errorThrown
                  });
                }
              });
            }
          }
        });
      });
    })(jQuery);
  </script>
</div>