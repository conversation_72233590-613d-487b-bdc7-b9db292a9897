langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_content
    - node.type.blog
  module:
    - text
id: node.blog.field_content
field_name: field_content
entity_type: node
bundle: blog
label: Content
description: 'The content of this page.'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  allowed_formats:
    - content_format
field_type: text_long
