langcode: en
status: true
dependencies:
  enforced:
    config:
      - node.type.page
  module:
    - node
id: page_content
label: 'Page content'
type: 'canonical_entities:node'
pattern: '/[node:title]'
selection_criteria:
  425d6456-71e3-4b65-9302-6c695efaab28:
    id: 'entity_bundle:node'
    negate: false
    uuid: 425d6456-71e3-4b65-9302-6c695efaab28
    context_mapping:
      node: node
    bundles:
      page: page
selection_logic: and
weight: -5
relationships: {  }
