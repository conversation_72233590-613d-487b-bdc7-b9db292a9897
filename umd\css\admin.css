
/*.ump-section{position: relative;background: #fafafa;display: flow-root;padding: 5px;}*/


.wp-core-ui select {
    max-width:unset!important;
    padding:unset!important;
}
.setting-left-section{width:12%;float:left;min-height:669px;background: #000;}
.setting-right-section{width:88%;float:left;}
.setting-left-section ul li{margin-bottom: 6px;background: #000;padding: 5px;color: #fff;text-decoration: none;border: 1px solid #4475a7;;margin: 0.5rem;}
.setting-left-section ul li.active{margin-bottom: 6px;background: #1f72aa;padding: 5px;color: #fff;text-decoration: none;margin: 0.5rem;}
.setting-left-section ul li a{color:#fff;text-decoration:none;}
.setting-left-section ul li a:hover{color:#fff;text-decoration:none;}
.ump_heading{
background: #d8d8d8;
    padding: 1rem;
  }
 .ump-section{width:97%;margin-left:1.5%; border: 1px solid #ccc;padding: 5px;margin-bottom: 20px;height: 90%;display: flow-root;}
.ump-section ul{margin-bottom: 0px;}
.ump_width_table
{
  width: 97%!important;
  margin: auto;
}
.ump_width_table tbody tr
{
  color: #000!important;
}
.dataTables_length select {
    width: 50px;
}
#epin_length_id
{
	display: none;
}#ePin_Price
{
	display: none;
}
.p2
{
      font-size: 1rem!important;
    font-weight: 800;
    margin: 29%;
}
#ump-acount-table td {
	padding: 0.5rem!important;
  text-align:center;
    border-bottom: 1px solid #e1e1e1;
}

.h4_bk
{
      padding: 1rem;
    background: #86f1e7;
    text-align:center;
}
.table-b
{
  padding: 0!important;
  max-width: 48%!important;
  margin: 1%;
  

}

.table-p
{
  padding: 0!important;
  max-width: 98%!important;
  margin: 1%;

}
#transaction_id{
  display: none;
}
.err_message
{
  color: red;
  font-size: 0.8rem;
}

.reset_data_message
{
border: 1px solid #f9aeae;
padding: 1rem;
padding-right: 1rem;
padding-left: 1rem;
background: #f7c7d96b;
color: #d5325f;
letter-spacing: 0.05rem;
font-size: 1rem;
border-radius: 1em;
}
.reset_data_message_green
{
display: none;
border: 1px solid #aef9bc;
padding: 1rem!important;
background: #c7f7cd6b;
letter-spacing: 0.05rem;
font-size: 1rem;
border-radius: 1em;
}
.success_green
{
color: #15881f;
margin-left: 5rem;

}
.err_red
{
color:#d92929db;
margin-left: 5rem;
}

.ump-bg{
background-color:#153f6b !important;
color: #ffffff;
}
.active
{
  color: #fff!important;
  background-color: #60ced4!important;
  text-decoration: none;
}
.active:hover
{
  color: #fff!important;
  
}
.buttn{
  width:130px;
}

.ump-report table thead tr th{
  background-color:#153f6b !important;
  color:white;
} 
.ump-report table thead tr th a{
  color: white !important;;
} 
.ump-report table tfoot tr th{
  background-color:#153f6b !important;
  color:white;
}
.ump-report table tfoot tr th a{
  color:white !important;
}
.view_img{
  background-color: yellow;
  width: 40px;
  height: 25px;
}
.margin-l{
  margin-left: 0;
}
.epin_report{
  display: inline-block;
}
.menu_margin{
  margin-top: 10px;
}
/*slider checkbox*/

.switch {
  display: inline-block;
  height: 20px;
  position: relative;
  width: 40px;
}

.switch input {
  display:none;
}

.slider {
  background-color: #ccc;
  bottom: 0;
  cursor: pointer;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  transition: .4s;
}

.slider:before {
  background-color: #fff;
  bottom: 2px;
  content: "";
  height: 16px;
  left: 4px;
  position: absolute;
  transition: .4s;
  width: 16px;
}

input:checked + .slider {
      background-color: #FF9800;
}

input:checked + .slider:before {
  transform: translateX(16px);
}

.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}
.msg-counting-box
{
    border: 1px solid #4CAF50;
    margin: 2px 0px;
    padding: 0.2em 0.5em;
    font-size: 12px;
    border-radius: 1em;
    color: #666;
}






