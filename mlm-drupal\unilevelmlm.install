<?php

/**
 * @file
 * Install, update and uninstall functions for the unilevelmlm module.
 */

use Drupal\Core\Database\Database;

/**
 * Implements hook_schema().
 */
function unilevelmlm_schema() {
  $schema['ump_wallet'] = [
    'description' => 'Stores user wallet information.',
    'fields' => [
      'user_id' => [
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'description' => 'The user ID.',
      ],
      'balance' => [
        'type' => 'numeric',
        'precision' => 20,
        'scale' => 2,
        'not null' => TRUE,
        'default' => 0.00,
        'description' => 'The user\'s wallet balance.',
      ],
      'currency' => [
        'type' => 'varchar',
        'length' => 3,
        'not null' => TRUE,
        'default' => 'USD',
        'description' => 'The currency of the wallet balance.',
      ],
    ],
    'primary key' => ['user_id'],
  ];

  return $schema;
}

/**
 * Implements hook_install().
 */
function unilevelmlm_install() {
  // Create the table.
  $schema = unilevelmlm_schema();
  foreach ($schema as $name => $table) {
    db_create_table($name, $table);
  }
}

/**
 * Implements hook_uninstall().
 */
function unilevelmlm_uninstall() {
  // Remove the table.
  $schema = unilevelmlm_schema();
  foreach ($schema as $name => $table) {
    db_drop_table($name);
  }
}