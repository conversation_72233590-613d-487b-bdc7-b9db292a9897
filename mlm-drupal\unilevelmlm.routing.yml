unilevelmlm.settings.general_settings:
  path: '/admin/config/unilevelmlm/settings/general_settings'
  defaults:
    _controller: '\Drupal\unilevelmlm\Controller\SettingController::general'
    _title: 'General Configuration Unilevel MLM'
  requirements:
    _permission: 'access administration content'

unilevelmlm.settings.distribute:
  path: '/admin/config/unilevelmlm/settings/distribute'
  defaults:
    _controller:  '\Drupal\unilevelmlm\Controller\SettingController::distribute'
    _title: 'Distribute Amount'
  requirements:
    _permission:  'access administration pages'

unilevelmlm.settings.run_payout:
  path: '/admin/config/unilevelmlm/settings/run_payout'
  defaults:
    _controller:  '\Drupal\unilevelmlm\Controller\SettingController::run_payout'
    _title: 'Run Payout'
  requirements:
    _permission:  'access administration pages'

unilevelmlm.settings.user_report:
  path: '/admin/config/unilevelmlm/settings/user_report'
  defaults:
    _controller:  '\Drupal\unilevelmlm\Controller\SettingController::user_report'
    _title: 'User Report'
  requirements:
    _permission:  'access administration pages'

unilevelmlm.settings.user_report/{user_id}:
  path: '/admin/config/unilevelmlm/settings/user_report/{user_id}'
  defaults:
    _controller:  '\Drupal\unilevelmlm\Controller\SettingController::user_report_data'
    _title: 'User Report Display'
  requirements:
    _permission:  'access administration pages'

unilevelmlm.settings.pay_report/{payout_id}:
  path: '/admin/config/unilevelmlm/settings/pay_report/{payout_id}'
  defaults:
    _controller:  '\Drupal\unilevelmlm\Controller\SettingController::pay_report_data'
    _title: 'Payout Report Display'
  requirements:
    _permission:  'access administration pages'

unilevelmlm.settings.pay_report:
  path: '/admin/config/unilevelmlm/settings/pay_report'
  defaults:
    _controller:  '\Drupal\unilevelmlm\Controller\SettingController::pay_report'
    _title: 'Payout Report'
  requirements:
    _permission:  'access administration pages'

unilevelmlm.settings.display_epins:
  path: '/admin/config/unilevelmlm/settings/display_epins'
  defaults:
    _controller:  '\Drupal\unilevelmlm\Controller\SettingController::display_epins'
    _title: 'ePins List'
  requirements:
    _permission:  'access administration pages'

unilevelmlm.settings.display_epins/viewallepins:
  path: '/admin/config/unilevelmlm/settings/display_epins/viewallepins/{id}'
  defaults:
    _controller:  '\Drupal\unilevelmlm\Controller\SettingController::viewallepins'
    _title: 'All ePins List'
  requirements:
    _permission:  'access administration pages'

unilevelmlm.settings.withdrawal:
  path: '/admin/config/unilevelmlm/settings/withdrawal'
  defaults:
    _controller:  '\Drupal\unilevelmlm\Controller\SettingController::withdrawal'
    _title: 'Withdrawal List'
  requirements:
    _permission:  'access administration pages'

unilevelmlm.settings.withdrawal/transfer_amount:
  path: '/admin/config/unilevelmlm/settings/withdrawal/transfer_amount/{user_id}/{id}'
  defaults:
    _controller:  '\Drupal\unilevelmlm\Controller\SettingController::transfer_amount'
    _title: 'Transfer Amount'
  requirements:
    _permission:  'access administration pages'

unilevelmlm.ump-registration:
  path: 'ump-registration'
  defaults:
    _controller: '\Drupal\unilevelmlm\Controller\FrontendController::ump_registration'
    _title: 'Unilevel Registration Page'
  requirements:
    _permission: 'access content'

unilevelmlm.ump_registration_by_referral:
  path: 'ump_registration_by_referral/{id}'
  defaults:
    _controller: '\Drupal\unilevelmlm\Controller\FrontendController::ump_registration_by_referral'
    _title: 'Unilevel Registration Page'
  requirements:
    _role: 'ump_user'

unilevelmlm.ump-dashboard:
  path: 'ump-dashboard'
  defaults:
    _controller: '\Drupal\unilevelmlm\Controller\FrontendController::ump_dashboard'
    _title: 'Unilevel Dashboard'
  requirements:
    _role: 'ump_user'

unilevelmlm.distribute_commission_function:
  path: "/admin/config/unilevelmlm/settings/distribute_commission_function"
  defaults:
    _controller: 'Drupal\unilevelmlm\Controller\SettingController::distribute_commission_function'
    _title: 'Distribute Commisiton'
  requirements:
    _permission: "access administration pages"

unilevelmlm.run_payout_function:
  path: "/admin/config/unilevelmlm/settings/run_payout_function"
  defaults:
    _controller: 'Drupal\unilevelmlm\Controller\SettingController::run_payout_function'
    _title: 'Run Payout'
  requirements:
    _permission: "access administration pages"

unilevelmlm.transfer_user_amount:
  path: "/admin/config/unilevelmlm/settings/withdrawal/transfer_amount/{user_id}/transfer_user_amount"
  defaults:
    _controller: 'Drupal\unilevelmlm\Controller\SettingController::transfer_user_amount'
    _title: 'Transfer Amount'
  requirements:
    _permission: "access administration pages"

unilevelmlm.settings.genealogy_function:
  path: "/admin/config/unilevelmlm/settings/genealogy_function"
  defaults:
    _controller: 'Drupal\unilevelmlm\Controller\GenealogyController::genealogy_function'
    _title: 'My genealogy'
  requirements:
    _permission: "access administration pages"

unilevelmlm.payout-details:
  path: "payout-details"
  defaults:
    _controller: 'Drupal\unilevelmlm\Controller\FrontendController::payout_detail'
    _title: 'Payout Details'
  requirements:
   _role: 'ump_user'

unilevelmlm.join-commission:
  path: "join-commission"
  defaults:
    _controller: 'Drupal\unilevelmlm\Controller\FrontendController::join_commission'
    _title: 'Join Commission'
  requirements:
    _permission: "access content"
    
unilevelmlm.ref-commission:
  path: "ref-commission"
  defaults:
    _controller: 'Drupal\unilevelmlm\Controller\FrontendController::ref_commission'
    _title: 'Referral Commission'
  requirements:
    _role: 'ump_user' 

unilevelmlm.account-detail:
  path: "account-detail"
  defaults:
    _controller: 'Drupal\unilevelmlm\Controller\FrontendController::account_detail'
    _title: 'Account Details'
  requirements:
    _role: 'ump_user'

unilevelmlm.bank-detail:
  path: "bank-detail"
  defaults:
    _controller: 'Drupal\unilevelmlm\Controller\FrontendController::bank_detail'
    _title: 'Bank Details'
  requirements:
    _role: 'ump_user'

unilevelmlm.add_bank_detail:
  path: "add_bank_detail"
  defaults:
    _controller: 'Drupal\unilevelmlm\Controller\FrontendController::add_bank_detail'
    _title: 'Add Bank details'
  requirements:
    _role: 'ump_user'

unilevelmlm.withdrawal-amount:
  path: "withdrawal-amount"
  defaults:
    _controller: 'Drupal\unilevelmlm\Controller\FrontendController::ump_withdrawal_amount'
    _title: 'Withdrawal Amount'
  requirements:
    _role: 'ump_user'

unilevelmlm.ump_withdrawal_amount_by_user:
  path: "ump_withdrawal_amount_by_user"
  defaults:
    _controller: 'Drupal\unilevelmlm\Controller\FrontendController::ump_withdrawal_amount_by_user'
    _title: 'withdrwal amount'
  requirements:
   _role: 'ump_user' 

unilevelmlm.ump-downlines:
  path: "ump-downlines"
  defaults:
    _controller: 'Drupal\unilevelmlm\Controller\FrontendController::ump_downlines'
    _title: 'User Genealogy'
  requirements:
   _role: 'ump_user'

unilevelmlm.ump-join:
  path: "ump-join"
  defaults:
    _controller: 'Drupal\unilevelmlm\Controller\FrontendController::ump_join'
    _title: 'User Join Us'
  requirements:
    _permission: 'access content'

unilevelmlm.settings.rebate_discount:
  path: '/admin/config/unilevelmlm/settings/rebate_discount'
  defaults:
    _controller: '\Drupal\unilevelmlm\Controller\SettingController::rebateDiscount'
    _title: 'Rebate/Discount Rules'
  requirements:
    _permission: 'access administration content'
rebate_discount_rule.settings:
  path: '/admin/structure/rebate_discount_rule/settings'
  defaults:
    _form: 'Drupal\unilevelmlm\Form\RebateDiscountRuleSettingsForm'
    _title: 'Rebate/Discount Rule settings'
  requirements:
    _permission: 'administer rebate_discount_rule entity'

entity.rebate_discount_rule.canonical:
  path: '/admin/structure/rebate_discount_rule/{rebate_discount_rule}'
  defaults:
    _entity_view: 'rebate_discount_rule'
    _title_callback: '\Drupal\Core\Entity\Controller\EntityController::title'
  requirements:
    _entity_access: 'rebate_discount_rule.view'

entity.rebate_discount_rule.edit_form:
  path: '/admin/structure/rebate_discount_rule/{rebate_discount_rule}/edit'
  defaults:
    _entity_form: 'rebate_discount_rule.edit'
    _title: 'Edit Rebate/Discount Rule'
  requirements:
    _entity_access: 'rebate_discount_rule.update'

entity.rebate_discount_rule.delete_form:
  path: '/admin/structure/rebate_discount_rule/{rebate_discount_rule}/delete'
  defaults:
    _entity_form: 'rebate_discount_rule.delete'
    _title: 'Delete Rebate/Discount Rule'
  requirements:
    _entity_access: 'rebate_discount_rule.delete'

entity.rebate_discount_rule.collection:
  path: '/admin/structure/rebate_discount_rule/list'
  defaults:
    _entity_list: 'rebate_discount_rule'
    _title: 'Rebate/Discount Rules'
  requirements:
    _permission: 'administer rebate_discount_rule entity'

rebate_discount_rule.add:
  path: '/admin/structure/rebate_discount_rule/add'
  defaults:
    _entity_form: 'rebate_discount_rule.add'
    _title: 'Add Rebate/Discount Rule'
  requirements:
    _permission: 'administer rebate_discount_rule entity'