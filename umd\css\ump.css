.register{
   
    margin-top: 3%;
    padding: 3%;
}
.register-left{
    text-align: center;
    color: #fff;
    margin-top: 4%;
    font-size: 18px;
}
.register-left input{
    border: none;
    border-radius: 1.5rem;
    padding: 2%;
    width: 60%;
    background: #f8f9fa;
    font-weight: bold;
    color: #383d41;
    margin-top: 30%;
    margin-bottom: 3%;
    cursor: pointer;
}
.register-right{
    background: #f8f9fa;
    border-top-left-radius: 10% 50%;
    border-bottom-left-radius: 10% 50%;
}
.register-left img{
    margin-top: 15%;
    margin-bottom: 5%;
    width: 100%;
    z-index: 1;
    -webkit-animation: mover 2s infinite  alternate;
    animation: mover 1s infinite  alternate;
}
@-webkit-keyframes mover {
    0% { transform: translateY(0); }
    100% { transform: translateY(-20px); }
}
@keyframes mover {
    0% { transform: translateY(0); }
    100% { transform: translateY(-20px); }
}
.register-left p{
    font-weight: lighter;
    padding: 12%;
    margin-top: -9%;
}
#copy_btn
{
    cursor: pointer;
}
.register .register-form{
        min-height: 411px;
    max-height:auto;
}
.btnRegister{
    float: right;
    margin-top: 10%;
    border: none;
    border-radius: 1.5rem;
    padding: 2%;
    background: #0062cc;
    color: #fff;
    font-weight: 600;
    width: 50%;
    cursor: pointer;
}
.register .nav-tabs{
    margin-top: 3%;
    border: none;
    background: #0062cc;
    border-radius: 1.5rem;
    width: 28%;
    float: right;
}
.register .nav-tabs .nav-link{
    padding: 2%;
    height: 34px;
    font-weight: 600;
    color: #fff;
    border-top-right-radius: 1.5rem;
    border-bottom-right-radius: 1.5rem;
}
.register .nav-tabs .nav-link:hover{
    border: none;
}
.register .nav-tabs .nav-link.active{
    width: 100px;
    color: #0062cc;
    border: 2px solid #0062cc;
    border-top-left-radius: 1.5rem;
    border-bottom-left-radius: 1.5rem;
}
.register-heading{
    text-align: center;
    margin-top: 8%;
    margin-bottom: -15%;
    color: #495057;
}
.err_div
{
    margin-bottom: -1.1rem;
    color: red;
    font-size: 0.8rem;
}

#bmp_sponsor_id,#bmp_position{height: 34px;}
#bmp_login{color:#fff;text-decoration: none;}
#bmp_register_form .form-control::placeholder,#bmp_register_form .form-control{background: #fff!important;}

/* downlines tree */

span.owner
{
    //color:#339966;
    color:#ffffff !important;
    font-style:italic;
}
span.paid
{
    color: #669966!important;
    font-style:normal;
}
span.leg
{
    color:red;
    font-style:italic;
}

#chart_div{overflow:visible;width:90%;overflow-x: scroll;}
#chart_div .img
{
  height: auto;
  width: auto;
  max-height: 60px;
  max-width: 60px;
  min-height: 50px;
  min-width: 50px;
  border: 1px solid;
  border-radius: 50%;
  color: #c2c9db;
}


.google-visualization-orgchart-node {
    border: none!important;
    box-shadow: none!important;
    border-radius: 0!important;
     text-align: center; 
    background: white!important; 
}

.google-visualization-orgchart-nodesel a,.google-visualization-orgchart-node a
{
    color:#8B0000 !important;
}
#chart_div table
{
    border-collapse:separate !important;
    
}
.google-visualization-orgchart-table
{
 padding: 0px 4rem 0 4rem;
  margin-top: 12rem;

}
.google-visualization-orgchart-lineleft {
    border-left: 1px solid #42aced !important;
}
.google-visualization-orgchart-lineright
{
    border-right: 1px solid #42aced !important;
}
.google-visualization-orgchart-linebottom {
    border-bottom: 1px solid #42aced !important;
}

#downlines-usersearch{display:block;}

#downlines-username{float:left;width:50%;}

#downlines-search{float:left;}


.user_error
{
  text-align: center;
  padding: 1.5rem;
  font-weight: 700;
  font-size: 1rem;
  border: 1px solid rgb(178,34,34);
  background-color: rgb(240,128,128,0.2);
  color:rgb(220,20,60);
}
.table-p
{
  padding: 0!important;
  max-width: 98%!important;
  margin-left: 1%;

   


}
.table-b
{
  padding: 0!important;
  max-width: 48%!important;
  margin: 1%;

}

.table-b h4 
{
  font-weight: 400;
  margin: 0!important;
  padding: 1rem;
}
.table-p h4
{
  font-weight: 400;
  margin: 0!important;
  padding: 1rem;
}
.p-2
{
  padding: 1rem!important;
}
.p-down-search
{
  margin-left: 1rem;
}
.table-p
 {
  border: 1px solid #696969 !important;
  }
  .ump_btn
  {
    background-color: #1d323e!important;
  color: #fff!important;
  }
  .table-b
{
  border: 1px solid #696969;
}
  .table-b h4 
{
  background-color: #1d323e;
  color: #fff;
}
.table-p h4
{
  background-color: #1d323e;
  color: #fff;
  }
  .ump-border-bottom{
border-top: 2px solid #1d323e!important;
}
.donators_account_table th,.donators_account_table td
{
padding: 0.1rem!important;
font-size: 0.8rem;
border: 1px solid #1d323e!important;
}
.err
{
  font-size: 0.8rem;
  color: red;
}
.ump_heading{
background: #d8d8d8;
    padding: 1rem;
  }

.paginator {
clear:both;
padding:6px;
position:relative;
text-decoration: none;
line-height:13px;
font-size: 1rem;
border: 1px solid #dee2e6;
}


.paginator:hover{
color:#fff;
background: #1d323e;
text-decoration: none;
}


a.tooltip {outline:none; position: relative;}
a.tooltip:hover {text-decoration:none; cursor:pointer;} 
a.tooltip span {
 z-index: ************;display:none;
width:auto; line-height:17px;
}

a.tooltip:hover span {
  display:block;
  position:absolute;
  bottom: 4.5em;
  z-index: ************!important;
  left: 50%;
  color:#373535;
  border:2px solid #D3D3D3;
  background:#fffFff;
  -webkit-transform: translateX(-50%);


}

/*CSS3 extras*/
a.tooltip span
{

border: 3px solid #d2cdc587;
   z-index: ************;
    border-radius:5px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    -moz-box-shadow: 1px 1px 8px #CCC;
    -webkit-box-shadow: 1px 1px 8px #CCC;
    box-shadow: 1px 1px 8px #CCC;
}


.tooltip {
  position: relative;
  display: inline-block;
  opacity: 1;
  width: 100%;
 text-align: center;
 z-index: 1 !important;
}


.tooltip span::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: #777 transparent transparent transparent;
}

.tooltip:hover{
  visibility: visible;
  opacity: 1;
  z-index:9999;
}
.ump_class_round{
    border:5px solid rgb(10, 143, 16);
}
@media only screen and (max-width: 767px) {
.ump_user_image{
    display:none;
}
}
.toggled .ump_user_image{
    display:none;
}
.table-responsive{
  overflow-x: none !important;
  min-height: 100% !important;
}
.tooltip_design
{   letter-spacing: 0.1em;
    /*font-size: 1.55rem;*/
    font-weight: 600;
    padding: 0.5rem!important;
    color: #0a0000;
    border: 3px solid #d2cdc587;
}
.tooltip .table-striped>tbody>tr:nth-of-type(odd) {
   background-color: #d2cdc587!important;
}

.purchase_epin{
  
  margin-top: 50px;
}
.ump_purchase_heading{
   margin-bottom: 30px;
 text-align:center;
color:black;
 padding-top: 15px;
 padding-bottom: 25px;
}
.ump_but{
  margin-top: 50px;
   background-color: #153f6b;
   color:white;
     padding: 8px  15px 8px 15px;


}
.ump_but:hover{
   
text-decoration: none !important;
color:black;   
}

label {
    font-weight: 600;
    color: #555;
}
.ump_button{
  
  padding:10px 10px 10px 10px !important;
  color:white;
  text-align: center;
  text-decoration: none;

}
.ump_button:hover{
  text-decoration: none !important;
color:black;   
}
.join_margin{
  margin-top:30px;
}
.shadow{
padding: 20px;
  box-shadow: 
       inset 0 -3em 3em rgba(0,0,0,0.1), 
             0 0  0 2px rgb(255,255,255),
             0.3em 0.3em 1em rgba(0,0,0,0.3);
   
  
}
.join_buttom{
   width:50%;
   margin-left: 25%;
  margin-bottom: 50px;
}
.icon-color{
  color: #4287f5 !important;
}
.dataTables_wrapper .paginate_button {
  margin: 1px;
}
.dtr-inline{
  width: 1351px;
}

.let_sub_count {
  font-size: 0.50em;
}

.password_icon {
    position: relative;
    margin-right: 12px;
    margin-top: -30px;
}
