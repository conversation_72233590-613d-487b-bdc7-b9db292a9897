langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_case_study__client_link
    - node.type.case_study
  module:
    - link
id: node.case_study.field_case_study__client_link
field_name: field_case_study__client_link
entity_type: node
bundle: case_study
label: 'Client link'
description: 'Include a link to the client or organization website.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  title: 1
  link_type: 17
field_type: link
