{"name": "drupal/cms", "version": "1.1.0", "description": "Drupal CMS is a ready-to-use platform built on Drupal core, offering smart defaults to get started quickly and enterprise-grade tools for marketers, designers, and content creators.", "type": "project", "license": "GPL-2.0-or-later", "homepage": "https://www.drupal.org/project/drupal", "support": {"docs": "https://drupal.org/docs/user_guide/en/index.html", "chat": "https://drupal.org/node/314178"}, "repositories": {"drupal": {"type": "composer", "url": "https://packages.drupal.org/8"}}, "require": {"composer/installers": "^2.3", "drupal/core-composer-scaffold": "^11.1.4", "drupal/core-project-message": "^11.1.4", "drupal/core-recommended": "^11.1.4", "drupal/drupal_cms_accessibility_tools": "~1.1.0", "drupal/drupal_cms_ai": "~1.1.0", "drupal/drupal_cms_analytics": "~1.1.0", "drupal/drupal_cms_blog": "~1.1.0", "drupal/drupal_cms_case_study": "~1.1.0", "drupal/drupal_cms_events": "~1.1.0", "drupal/drupal_cms_forms": "~1.1.0", "drupal/drupal_cms_news": "~1.1.0", "drupal/drupal_cms_page": "~1.1.0", "drupal/drupal_cms_person": "~1.1.0", "drupal/drupal_cms_project": "~1.1.0", "drupal/drupal_cms_seo_tools": "~1.1.0", "drupal/drupal_cms_starter": "~1.1.0", "drupal/project_browser": "@beta", "drupal/recipe_installer_kit": "^1-alpha3@alpha", "drupal/webform": "@beta", "drush/drush": "^13"}, "conflict": {"drupal/drupal": "*"}, "minimum-stability": "stable", "prefer-stable": true, "config": {"allow-plugins": {"composer/installers": true, "drupal/core-composer-scaffold": true, "drupal/core-project-message": true, "php-http/discovery": true}, "sort-packages": true, "optimize-autoloader": true}, "extra": {"drupal-scaffold": {"locations": {"web-root": "web/"}}, "installer-paths": {"web/core": ["type:drupal-core"], "web/libraries/{$name}": ["type:drupal-library"], "web/modules/contrib/{$name}": ["type:drupal-module"], "web/profiles/contrib/{$name}": ["type:drupal-profile"], "web/themes/contrib/{$name}": ["type:drupal-theme"], "drush/Commands/contrib/{$name}": ["type:drupal-drush"], "web/modules/custom/{$name}": ["type:drupal-custom-module"], "web/profiles/custom/{$name}": ["type:drupal-custom-profile"], "recipes/{$name}": ["type:drupal-recipe"], "web/themes/custom/{$name}": ["type:drupal-custom-theme"]}, "drupal-core-project-message": {"include-keys": ["homepage", "support"], "post-create-project-cmd-message": ["<bg=blue;fg=white>                                                   </>", "<bg=blue;fg=white>  Congratulations, you’ve installed Drupal CMS!    </>", "<bg=blue;fg=white>                                                   </>", "", "<bg=yellow;fg=black>Next steps</>:", "  * Read the user guide: https://www.drupal.org/docs/user_guide/en/index.html", "  * Get support: https://drupal.org/support", "  * Get involved with the Drupal community: https://drupal.org/getting-involved"]}}}