<?php

namespace Drupal\umd\Form;

use Drupal\Core\Form\FormBase;
use Drupal\Core\Form\FormStateInterface;

/**
 * Provides a form for defining rebate and discount rules.
 */
class RebateDiscountForm extends FormBase {

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'rebate_discount_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $form['discount_type'] = [
      '#type' => 'select',
      '#title' => $this->t('Discount Type'),
      '#options' => [
        'percentage' => $this->t('Percentage'),
        'fixed_amount' => $this->t('Fixed Amount'),
        'tiered' => $this->t('Tiered'),
      ],
      '#required' => TRUE,
    ];

    $form['applicable_products'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Applicable Products (comma-separated)'),
      '#description' => $this->t('Enter product IDs or categories, separated by commas.'),
    ];
$form['apply_to'] = [
      '#type' => 'checkboxes',
      '#title' => $this->t('Apply To'),
      '#options' => [
        'products' => $this->t('Products'),
        'epins' => $this->t('E-pins'),
      ],
      '#default_value' => ['products'],
      '#required' => TRUE,
    ];

    $form['start_date'] = [
      '#type' => 'date',
      '#title' => $this->t('Start Date'),
      '#required' => TRUE,
    ];

    $form['end_date'] = [
      '#type' => 'date',
      '#title' => $this->t('End Date'),
      '#required' => TRUE,
    ];

    $form['min_quantity'] = [
      '#type' => 'number',
      '#title' => $this->t('Minimum Purchase Quantity (for tiered discounts)'),
      '#min' => 1,
      '#states' => [
        'visible' => [
          ':input[name="discount_type"]' => ['value' => 'tiered'],
        ],
      ],
    ];

    $form['submit'] = [
      '#type' => 'submit',
      '#value' => $this->t('Save Rule'),
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state) {
    // Validate form values.
    if ($form_state->getValue('discount_type') == 'tiered' && empty($form_state->getValue('min_quantity'))) {
      $form_state->setErrorByName('min_quantity', $this->t('Minimum purchase quantity is required for tiered discounts.'));
    }

    if ($form_state->getValue('start_date') > $form_state->getValue('end_date')) {
      $form_state->setErrorByName('end_date', $this->t('End date must be after start date.'));
    }

    // Validate applicable products.
    $applicable_products = $form_state->getValue('applicable_products');
    if (!empty($applicable_products)) {
      $product_ids = explode(',', $applicable_products);
      foreach ($product_ids as $product_id) {
        $product_id = trim($product_id);
        // Check if the product ID exists in the database.
        $product = \Drupal::database()->select('products', 'p')
          ->fields('p', ['product_id'])
          ->condition('product_id', $product_id)
          ->execute()
          ->fetchField();

        // Check if the category exists in the database.
        $category = \Drupal::database()->select('categories', 'c')
          ->fields('c', ['category_id'])
          ->condition('category_id', $product_id)
          ->execute()
          ->fetchField();

        if (!$product && !$category) {
          $form_state->setErrorByName('applicable_products', $this->t('Invalid product ID or category: @product_id', ['@product_id' => $product_id]));
        }
      }
    }
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    // Store the defined rule in the database.
    $discount_type = $form_state->getValue('discount_type');
    $applicable_products = $form_state->getValue('applicable_products');
    $start_date = $form_state->getValue('start_date');
    $end_date = $form_state->getValue('end_date');
    $min_quantity = $form_state->getValue('min_quantity');
    $apply_to = $form_state->getValue('apply_to');

    // Save to database (implementation omitted for brevity).
    // You would typically use Drupal's database API here.
    $apply_to_string = implode(',', array_keys(array_filter($apply_to)));
    \Drupal::messenger()->addMessage($this->t('Rebate/Discount rule saved. Applied to: @apply_to', ['@apply_to' => $apply_to_string]));
  }

}