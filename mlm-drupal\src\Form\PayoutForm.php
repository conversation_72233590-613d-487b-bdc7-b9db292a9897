<?php
namespace Drupal\unilevelmlm\Form;

use Drupal\Core\Form\ConfigFormBase;
use Drupal\Core\Form\FormStateInterface;

class PayoutForm extends ConfigFormBase {

 
	public function getFormId()
	{
		return 'unilevelmlm_payout_config_form';
	}

	protected function getEditableConfigNames(){	 

		return [
			 'unilevelmlm.payout'
		];
	}

	public function buildForm(array $form , FormStateInterface $form_state ){
		$no_of_level = \Drupal::config('unilevelmlm.general')->get('ump_no_of_levels');
		$config = $this->config('unilevelmlm.payout');

		$form['payout'] = [
            '#type' => 'details',
            '#title' => t('Payout Settings'),
            '#open' => FALSE, 
          ];
		
		 

		$form['payout']['ump_join_commission'] = [
		    '#type' => 'number',
		    '#title' => t('Join Commission'),
		    '#required' => TRUE,
		    '#description' => t('Enter Join commission'),
		    '#default_value' => $config->get('ump_join_commission'),
		    '#attributes' => array('class' => array('let-form-control w-95'), 'min' => 0),
		    '#element_validate' => array('::validateNumber'),
			'#wrapper_attributes' => ['class' => ['let-col-6']],
			'#prefix'=>'<div class="let-row">'
			
		];
		$form['payout']['ump_join_commission_type']=[			 
	        '#title' => t('Join Commission Type'),
	        '#type' => 'select',
	        '#description' => 'Select Commission Type.',
	        '#options' => array(
				'fixed'=>t('fixed'),
				'percent'=>t('percent')
			),
			'#wrapper_attributes' => ['class' => ['let-col-4']],
	        '#default_value' => $config->get('ump_join_commission_type'),
			'#attributes'	=> array('class' => array('let-form-control w-50')),
			'#suffix'=>'</div>'
		];

		 
		$form['payout']['ump_referral_commission']=[
			'#type'=>'number',
			'#title'=>t('Referral Commission'),
			'#required'=>TRUE,
			'#description'=>t('Refferal Commission'),
			'#default_value'=>$config->get('ump_referral_commission'),			 
			'#wrapper_attributes' => ['class' => ['let-col-6']],
			'#attributes'	=> array('class' => array('let-form-control w-95')),
			'#prefix'=>'<div class="let-row">'

		];
		$form['payout']['ump_referral_commission_type']=[			 
	        '#title' => t('Referral Commission Type'),
	        '#type' => 'select',
	        '#description' => 'Select Commission Type.',
	        '#options' => array(
	        'fixed'=>t('fixed'),
	         'percent'=>t('percent')
	     	),
			 '#wrapper_attributes' => ['class' => ['let-col-4']],
	        '#default_value' => $config->get('ump_referral_commission_type'),
			'#attributes'	=> array('class' => array('let-form-control w-50')),
			'#suffix'=>'</div>'
            

		];	 

		  $form['payout']['ump_1st_bonus'] = [
		    '#type' => 'number',
		    '#title' => t('1st Bonus'),
		    '#required' => TRUE,
		    '#description' => t('Enter 1st bonus amount'),
		    '#default_value' => $config->get('ump_1st_bonus'),
		    '#attributes'  => array('class' => array('let-form-control w-95')),
		    '#wrapper_attributes' => ['class' => ['let-col-6']],
		    '#prefix' => '<div class="let-row">',
		  ];
		  $form['payout']['ump_1st_bonus_sales_volume'] = [
		    '#type' => 'number',
		    '#title' => t('1st Bonus Sales Volume'),
		    '#required' => TRUE,
		    '#description' => t('Enter sales volume required for 1st bonus'),
		    '#default_value' => $config->get('ump_1st_bonus_sales_volume'),
		    '#attributes'  => array('class' => array('let-form-control w-95')),
		    '#wrapper_attributes' => ['class' => ['let-col-6']],
		    '#suffix' => '</div>',
		  ];

		  $form['payout']['ump_2nd_bonus'] = [
		    '#type' => 'number',
		    '#title' => t('2nd Bonus'),
		    '#required' => TRUE,
		    '#description' => t('Enter 2nd bonus amount'),
		    '#default_value' => $config->get('ump_2nd_bonus'),
		    '#attributes'  => array('class' => array('let-form-control w-95')),
		    '#wrapper_attributes' => ['class' => ['let-col-6']],
		    '#prefix' => '<div class="let-row">',
		  ];
		  $form['payout']['ump_2nd_bonus_min_rank'] = [
		    '#type' => 'number',
		    '#title' => t('2nd Bonus Minimum Rank'),
		    '#required' => TRUE,
		    '#description' => t('Enter minimum rank required for 2nd bonus'),
		    '#default_value' => $config->get('ump_2nd_bonus_min_rank'),
		    '#attributes'  => array('class' => array('let-form-control w-95')),
		    '#wrapper_attributes' => ['class' => ['let-col-6']],
		    '#suffix' => '</div>',
		  ];

		$form['payout']['submit'] = [
		          '#type' => 'submit',
		          '#value' => t('Save Payout Settings'),
        ];

        $form['payout']['submit']['#attributes']['class'][]='button--primary let-m-auto let-d-flex';

        return $form;
	}

	public function submitForm(array &$form, FormStateInterface $form_state){
		$no_of_level = \Drupal::config('unilevelmlm.general')->get('ump_no_of_levels');
        \Drupal::configFactory()->getEditable('unilevelmlm.payout')->delete();
		  $config = $this->config('unilevelmlm.payout');
		  $config->set('ump_join_commission', $form_state->getValue('ump_join_commission'));
		  $config->set('ump_join_commission_type', $form_state->getValue('ump_join_commission_type'));
		  $config->set('ump_referral_commission', $form_state->getValue('ump_referral_commission'));
		  $config->set('ump_referral_commission_type', $form_state->getValue('ump_referral_commission_type'));
		  $config->set('ump_1st_bonus', $form_state->getValue('ump_1st_bonus'));
		  $config->set('ump_1st_bonus_sales_volume', $form_state->getValue('ump_1st_bonus_sales_volume'));
		  $config->set('ump_2nd_bonus', $form_state->getValue('ump_2nd_bonus'));
		  $config->set('ump_2nd_bonus_min_rank', $form_state->getValue('ump_2nd_bonus_min_rank'));
		  $config->save();
        return $this->messenger()->addStatus($this->t('Payout Setting has been Save.'));
	}
}
?>