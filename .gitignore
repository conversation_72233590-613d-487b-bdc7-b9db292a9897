# UMD Drupal MLM Project .gitignore

# Large installer files
*.exe
*.msi
*.dmg
*.pkg
*.deb
*.rpm

# XAMPP/Development Environment Files
xampp/
*xampp*
wamp/
mamp/

# Drupal specific
web/sites/*/files/
web/sites/*/private/
web/sites/*/settings.local.php
web/sites/*/services.local.yml
web/sites/*/settings.php
web/sites/default/files/
web/sites/default/private/
web/core/
web/modules/contrib/
web/themes/contrib/
web/profiles/contrib/
web/libraries/
vendor/

# Composer
composer.phar
composer.lock

# Database dumps
*.sql
*.sql.gz
*.sql.bz2

# Log files
*.log
logs/
log/

# Temporary files
tmp/
temp/
cache/
*.tmp
*.temp
*.cache

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files
.env
.env.local
.env.*.local

# Backup files
*.bak
*.backup
*~

# Archive files (large)
*.zip
*.tar.gz
*.tar.bz2
*.rar
*.7z

# Media files (can be large)
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm

# Large image files
*.psd
*.ai
*.eps

# Development certificates
*.pem
*.key
*.crt
*.p12

# Local configuration
config/local/
local.settings.php

# Drupal console
.console/

# PHPUnit
.phpunit.result.cache
phpunit.xml

# Behat
behat.yml

# Vagrant
.vagrant/

# Docker
.docker/
docker-compose.override.yml

# Local development
.local/
local/

# Drupal scaffold files (managed by Composer)
web/.csslintrc
web/.eslintignore
web/.eslintrc.json
web/.gitattributes
web/.htaccess
web/autoload.php
web/index.php
web/robots.txt
web/update.php
web/web.config

# Ignore directories generated by Composer
drush/contrib/
web/core/
web/modules/contrib/
web/themes/contrib/
web/profiles/contrib/
web/libraries/

# Ignore sensitive files
web/sites/*/settings.php
web/sites/*/services.yml
web/sites/*/settings.local.php
web/sites/*/services.local.yml
