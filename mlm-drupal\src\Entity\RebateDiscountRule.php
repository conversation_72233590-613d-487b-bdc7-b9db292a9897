<?php

namespace Drupal\unilevelmlm\Entity;

use Drupal\Core\Entity\ContentEntityBase;
use Drupal\Core\Field\BaseFieldDefinition;
use Drupal\Core\Entity\EntityTypeInterface;

/**
 * Defines the Rebate/Discount Rule entity.
 *
 * @ContentEntityType(
 *   id = "rebate_discount_rule",
 *   label = @Translation("Rebate/Discount Rule"),
 *   base_table = "rebate_discount_rule",
 *   entity_keys = {
 *     "id" = "id",
 *     "label" = "name",
 *     "uuid" = "uuid"
 *   },
 *   admin_permission = "administer rebate_discount_rule entity",
 *   handlers = {
 *     "view_builder" = "Drupal\Core\Entity\EntityViewBuilder",
 *     "list_builder" = "Drupal\unilevelmlm\RebateDiscountRuleListBuilder",
 *     "form" = {
 *       "default" = "Drupal\unilevelmlm\Form\RebateDiscountRuleForm",
 *       "add" = "Drupal\unilevelmlm\Form\RebateDiscountRuleForm",
 *       "edit" = "Drupal\unilevelmlm\Form\RebateDiscountRuleForm",
 *       "delete" = "Drupal\unilevelmlm\Form\RebateDiscountRuleDeleteForm",
 *     },
 *     "route_provider" = {
 *       "html" = "Drupal\Core\Entity\Routing\AdminHtmlRouteProvider",
 *     },
 *   },
 *   links = {
 *     "canonical" = "/admin/structure/rebate_discount_rule/{rebate_discount_rule}",
 *     "add-form" = "/admin/structure/rebate_discount_rule/add",
 *     "edit-form" = "/admin/structure/rebate_discount_rule/{rebate_discount_rule}/edit",
 *     "delete-form" = "/admin/structure/rebate_discount_rule/{rebate_discount_rule}/delete",
 *     "collection" = "/admin/structure/rebate_discount_rule/list"
 *   },
 *   field_ui_base_route = "rebate_discount_rule.settings"
 * )
 */
class RebateDiscountRule extends ContentEntityBase {

  /**
   * {@inheritdoc}
   */
  public static function baseFieldDefinitions(EntityTypeInterface $entity_type) {
    $fields = parent::baseFieldDefinitions($entity_type);

    $fields['name'] = BaseFieldDefinition::create('string')
      ->setLabel(t('Name'))
      ->setDescription(t('The name of the Rebate/Discount Rule entity.'))
      ->setSettings([
        'max_length' => 50,
        'text_processing' => 0,
      ])
      ->setDisplayOptions('view', [
        'label' => 'above',
        'type' => 'string',
        'weight' => -4,
      ])
      ->setDisplayOptions('form', [
        'type' => 'string_textfield',
        'weight' => -4,
      ])
      ->setDisplayConfigurable(TRUE, TRUE);

    $fields['description'] = BaseFieldDefinition::create('string_long')
      ->setLabel(t('Description'))
      ->setDescription(t('A brief description of the Rebate/Discount Rule.'))
      ->setDisplayOptions('view', [
        'label' => 'above',
        'type' => 'string',
        'weight' => -3,
      ])
      ->setDisplayOptions('form', [
        'type' => 'string_textarea',
        'weight' => -3,
      ])
      ->setDisplayConfigurable(TRUE, TRUE);

    // Add more fields here as needed, such as:
    // - Rule type (e.g., rebate, discount)
    // - Discount percentage or fixed amount
    // - Conditions (e.g., minimum order amount, product category)

    return $fields;
  }

}