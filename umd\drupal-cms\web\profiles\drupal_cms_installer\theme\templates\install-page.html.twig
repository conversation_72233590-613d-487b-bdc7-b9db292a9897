{#
/**
 * @file
 * Drupal CMS implementation to display the installation page.
 *
 * All available variables are mirrored in page.html.twig.
 * Some may be blank but they are provided for consistency.
 *
 * @see template_preprocess_install_page()
 */
#}
<div class="cms-installer">
  <header class="cms-installer__header">
    <h1 class="cms-installer__heading">
      <img src="{{ theme_path }}/images/drupal-cms-logo.svg" alt="{{ 'Drupal CMS'|t }}" />
    </h1>
  </header>
  <div class="cms-installer__wrapper">

    <main class="cms-installer__main">
      {% if title %}
        <h2 class="cms-installer__main-heading">{{ title }}</h2>
      {% endif %}
      {{ page.highlighted }}
      {{ page.content }}
    </main>

    {% if page.sidebar_second %}
      <aside class="cms-installer__sidebar-second">
        {{ page.sidebar_second }}
      </aside>
    {% endif %}

    {% if page.page_bottom %}
      <footer class="cms-installer__footer">
        {{ page.page_bottom }}
      </footer>
    {% endif %}
  </div>
</div>
