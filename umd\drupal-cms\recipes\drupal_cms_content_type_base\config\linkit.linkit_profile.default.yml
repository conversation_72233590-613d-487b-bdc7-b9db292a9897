langcode: en
status: true
dependencies:
  module:
    - node
label: Default
id: default
description: 'A default Linkit profile'
matchers:
  556010a3-e317-48b3-b4ed-854c10f4b950:
    id: 'entity:node'
    uuid: 556010a3-e317-48b3-b4ed-854c10f4b950
    settings:
      metadata: '[node:content-type:name] by [node:author] on [node:created:custom:j M Y]'
      bundles: {  }
      group_by_bundle: false
      substitution_type: canonical
      limit: 100
      include_unpublished: true
    weight: 0
  f82bb77f-274f-4ef3-8aaa-30754dc9914c:
    id: 'entity:media'
    uuid: f82bb77f-274f-4ef3-8aaa-30754dc9914c
    settings:
      metadata: ''
      bundles:
        document: document
      group_by_bundle: false
      substitution_type: media
      limit: 100
    weight: 0
