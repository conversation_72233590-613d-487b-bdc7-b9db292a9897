
ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}

dt {
  font-weight: 700;
}

dd {
  margin-bottom: .let-5rem;
  margin-left: 0;
}

blockquote {
  margin: 0 0 1rem;
}

b,
strong {
  font-weight: bolder;
}

small {
  font-size: 80%;
}

sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -.let-25em;
}

sup {
  top: -.let-5em;
}

a {
  color: #007bff;
  text-decoration: none;
  background-color: transparent;
}

a:hover {
  color: #0056b3;
  text-decoration: underline;
}

a:not([href]):not([tabindex]) {
  color: inherit;
  text-decoration: none;
}

a:not([href]):not([tabindex]):hover, a:not([href]):not([tabindex]):focus {
  color: inherit;
  text-decoration: none;
}

a:not([href]):not([tabindex]):focus {
  outline: 0;
}

pre,
code,
kbd,
samp {
  font-family: SFMono-Regular, Menlo, Monaco, Con<PERSON>as, "Liberation Mono", "Courier New", monospace;
  font-size: 1em;
}

pre {
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
}

figure {
  margin: 0 0 1rem;
}

img {
  vertical-align: middle;
  border-style: none;
}

svg {
  overflow: hidden;
  vertical-align: middle;
}

table {
  border-collapse: collapse;
}

caption {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  color: #6c757d;
  text-align: left;
  caption-side: bottom;
}

th {
  text-align: inherit;
}

label {
  display: inline-block;
  margin-bottom: 0.5rem;
}

button {
  border-radius: 0;
}

button:focus {
  outline: 1px dotted;
  outline: 5px auto -webkit-focus-ring-color;
}

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button,
input {
  overflow: visible;
}

button,
select {
  text-transform: none;
}

select {
  word-wrap: normal;
}

  table th,table td{
    text-align: center!important;
  }
button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}

button:not(:disabled),
[type="button"]:not(:disabled),
[type="reset"]:not(:disabled),
[type="submit"]:not(:disabled) {
  cursor: pointer;
}

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

input[type="radio"],
input[type="checkbox"] {
  box-sizing: border-box;
  padding: 0;
}

input[type="date"],
input[type="time"],
input[type="datetime-local"],
input[type="month"] {
  -webkit-appearance: listbox;
}

textarea {
  overflow: auto;
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  display: block;
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin-bottom: .let-5rem;
  font-size: 1.5rem;
  line-height: inherit;
  color: inherit;
  white-space: normal;
}

progress {
  vertical-align: baseline;
}

[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  outline-offset: -2px;
  -webkit-appearance: none;
}

[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}

output {
  display: inline-block;
}

summary {
  display: list-item;
  cursor: pointer;
}

template {
  display: none;
}

[hidden] {
  display: none !important;
}

h1, h2, h3, h4, h5, h6,
.let-h1, .let-h2, .let-h3, .let-h4, .let-h5, .let-h6 {
  margin-bottom: 0.5rem;
  font-weight: 500;
  line-height: 1.2;
}

h1, .let-h1 {
  font-size: 2.5rem;
}

h2, .let-h2 {
  font-size: 2rem;
}

h3, .let-h3 {
  font-size: 1.75rem;
}

h4, .let-h4 {
  font-size: 1.5rem;
}

h5, .let-h5 {
  font-size: 1.25rem;
}

h6, .let-h6 {
  font-size: 1rem;
}

.let-lead {
  font-size: 1.25rem;
  font-weight: 300;
}

.let-display-1 {
  font-size: 6rem;
  font-weight: 300;
  line-height: 1.2;
}

.let-display-2 {
  font-size: 5.5rem;
  font-weight: 300;
  line-height: 1.2;
}

.let-display-3 {
  font-size: 4.5rem;
  font-weight: 300;
  line-height: 1.2;
}

.let-display-4 {
  font-size: 3.5rem;
  font-weight: 300;
  line-height: 1.2;
}

hr {
  margin-top: 1rem;
  margin-bottom: 1rem;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

small,
.let-small {
  font-size: 80%;
  font-weight: 400;
}

mark,
.let-mark {
  padding: 0.2em;
  background-color: #fcf8e3;
}

.let-list-unstyled {
  padding-left: 0;
  list-style: none;
}

.let-list-inline {
  padding-left: 0;
  list-style: none;
}

.let-list-inline-item {
  display: inline-block;
}

.let-list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}

.let-initialism {
  font-size: 90%;
  text-transform: uppercase;
}

.let-blockquote {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.let-blockquote-footer {
  display: block;
  font-size: 80%;
  color: #6c757d;
}

.let-blockquote-footer::before {
  content: "\2014\00A0";
}

.let-img-fluid {
  max-width: 100%;
  height: auto;
}

.let-img-thumbnail {
  padding: 0.25rem;
  background-color: #fff;
  border: 1px solid #dee2e6;
  border-radius: 0.25rem;
  max-width: 100%;
  height: auto;
}

.let-figure {
  display: inline-block;
}

.let-figure-img {
  margin-bottom: 0.5rem;
  line-height: 1;
}

.let-figure-caption {
  font-size: 90%;
  color: #6c757d;
}

code {
  font-size: 87.5%;
  color: #e83e8c;
  word-break: break-word;
}

a > code {
  color: inherit;
}

kbd {
  padding: 0.2rem 0.4rem;
  font-size: 87.5%;
  color: #fff;
  background-color: #212529;
  border-radius: 0.2rem;
}

kbd kbd {
  padding: 0;
  font-size: 100%;
  font-weight: 700;
}

pre {
  display: block;
  font-size: 87.5%;
  color: #212529;
}

pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}

.let-pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}

.let-container {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .let-container {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .let-container {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .let-container {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .let-container {
    max-width: 1140px;
  }
}

.let-container-fluid {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

.let-row {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.let-no-gutters {
  margin-right: 0;
  margin-left: 0;
}

.let-no-gutters > .let-col,
.let-no-gutters > [class*="col-"] {
  padding-right: 0;
  padding-left: 0;
}

.let-col-1, .let-col-2, .let-col-3, .let-col-4, .let-col-5, .let-col-6, .let-col-7, .let-col-8, .let-col-9, .let-col-10, .let-col-11, .let-col-12, .let-col,
.let-col-auto, .let-col-sm-1, .let-col-sm-2, .let-col-sm-3, .let-col-sm-4, .let-col-sm-5, .let-col-sm-6, .let-col-sm-7, .let-col-sm-8, .let-col-sm-9, .let-col-sm-10, .let-col-sm-11, .let-col-sm-12, .let-col-sm,
.let-col-sm-auto, .let-col-md-1, .let-col-md-2, .let-col-md-3, .let-col-md-4, .let-col-md-5, .let-col-md-6, .let-col-md-7, .let-col-md-8, .let-col-md-9, .let-col-md-10, .let-col-md-11, .let-col-md-12, .let-col-md,
.let-col-md-auto, .let-col-lg-1, .let-col-lg-2, .let-col-lg-3, .let-col-lg-4, .let-col-lg-5, .let-col-lg-6, .let-col-lg-7, .let-col-lg-8, .let-col-lg-9, .let-col-lg-10, .let-col-lg-11, .let-col-lg-12, .let-col-lg,
.let-col-lg-auto, .let-col-xl-1, .let-col-xl-2, .let-col-xl-3, .let-col-xl-4, .let-col-xl-5, .let-col-xl-6, .let-col-xl-7, .let-col-xl-8, .let-col-xl-9, .let-col-xl-10, .let-col-xl-11, .let-col-xl-12, .let-col-xl,
.let-col-xl-auto {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}

.let-col {
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  -ms-flex-positive: 1;
  flex-grow: 1;
  max-width: 100%;
}

.let-col-auto {
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  width: auto;
  max-width: 100%;
}

.let-col-1 {
  -ms-flex: 0 0 8.333333%;
  flex: 0 0 8.333333%;
  max-width: 8.333333%;
}

.let-col-2 {
  -ms-flex: 0 0 16.666667%;
  flex: 0 0 16.666667%;
  max-width: 16.666667%;
}

.let-col-3 {
  -ms-flex: 0 0 25%;
  flex: 0 0 25%;
  max-width: 25%;
}

.let-col-4 {
  -ms-flex: 0 0 33.333333%;
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
}

.let-col-5 {
  -ms-flex: 0 0 41.666667%;
  flex: 0 0 41.666667%;
  max-width: 41.666667%;
}

.let-col-6 {
  -ms-flex: 0 0 50%;
  flex: 0 0 50%;
  max-width: 50%;
}

.let-col-7 {
  -ms-flex: 0 0 58.333333%;
  flex: 0 0 58.333333%;
  max-width: 58.333333%;
}

.let-col-8 {
  -ms-flex: 0 0 66.666667%;
  flex: 0 0 66.666667%;
  max-width: 66.666667%;
}

.let-col-9 {
  -ms-flex: 0 0 75%;
  flex: 0 0 75%;
  max-width: 75%;
}

.let-col-10 {
  -ms-flex: 0 0 83.333333%;
  flex: 0 0 83.333333%;
  max-width: 83.333333%;
}

.let-col-11 {
  -ms-flex: 0 0 91.666667%;
  flex: 0 0 91.666667%;
  max-width: 91.666667%;
}

.let-col-12 {
  -ms-flex: 0 0 100%;
  flex: 0 0 100%;
  max-width: 100%;
}

.let-order-first {
  -ms-flex-order: -1;
  order: -1;
}

.let-order-last {
  -ms-flex-order: 13;
  order: 13;
}

.let-order-0 {
  -ms-flex-order: 0;
  order: 0;
}

.let-order-1 {
  -ms-flex-order: 1;
  order: 1;
}

.let-order-2 {
  -ms-flex-order: 2;
  order: 2;
}

.let-order-3 {
  -ms-flex-order: 3;
  order: 3;
}

.let-order-4 {
  -ms-flex-order: 4;
  order: 4;
}

.let-order-5 {
  -ms-flex-order: 5;
  order: 5;
}

.let-order-6 {
  -ms-flex-order: 6;
  order: 6;
}

.let-order-7 {
  -ms-flex-order: 7;
  order: 7;
}

.let-order-8 {
  -ms-flex-order: 8;
  order: 8;
}

.let-order-9 {
  -ms-flex-order: 9;
  order: 9;
}

.let-order-10 {
  -ms-flex-order: 10;
  order: 10;
}

.let-order-11 {
  -ms-flex-order: 11;
  order: 11;
}

.let-order-12 {
  -ms-flex-order: 12;
  order: 12;
}

.let-offset-1 {
  margin-left: 8.333333%;
}

.let-offset-2 {
  margin-left: 16.666667%;
}

.let-offset-3 {
  margin-left: 25%;
}

.let-offset-4 {
  margin-left: 33.333333%;
}

.let-offset-5 {
  margin-left: 41.666667%;
}

.let-offset-6 {
  margin-left: 50%;
}

.let-offset-7 {
  margin-left: 58.333333%;
}

.let-offset-8 {
  margin-left: 66.666667%;
}

.let-offset-9 {
  margin-left: 75%;
}

.let-offset-10 {
  margin-left: 83.333333%;
}

.let-offset-11 {
  margin-left: 91.666667%;
}

@media (min-width: 576px) {
  .let-col-sm {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }
  .let-col-sm-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .let-col-sm-1 {
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }
  .let-col-sm-2 {
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
  .let-col-sm-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .let-col-sm-4 {
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .let-col-sm-5 {
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  .let-col-sm-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .let-col-sm-7 {
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
  .let-col-sm-8 {
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  .let-col-sm-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }
  .let-col-sm-10 {
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }
  .let-col-sm-11 {
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }
  .let-col-sm-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .let-order-sm-first {
    -ms-flex-order: -1;
    order: -1;
  }
  .let-order-sm-last {
    -ms-flex-order: 13;
    order: 13;
  }
  .let-order-sm-0 {
    -ms-flex-order: 0;
    order: 0;
  }
  .let-order-sm-1 {
    -ms-flex-order: 1;
    order: 1;
  }
  .let-order-sm-2 {
    -ms-flex-order: 2;
    order: 2;
  }
  .let-order-sm-3 {
    -ms-flex-order: 3;
    order: 3;
  }
  .let-order-sm-4 {
    -ms-flex-order: 4;
    order: 4;
  }
  .let-order-sm-5 {
    -ms-flex-order: 5;
    order: 5;
  }
  .let-order-sm-6 {
    -ms-flex-order: 6;
    order: 6;
  }
  .let-order-sm-7 {
    -ms-flex-order: 7;
    order: 7;
  }
  .let-order-sm-8 {
    -ms-flex-order: 8;
    order: 8;
  }
  .let-order-sm-9 {
    -ms-flex-order: 9;
    order: 9;
  }
  .let-order-sm-10 {
    -ms-flex-order: 10;
    order: 10;
  }
  .let-order-sm-11 {
    -ms-flex-order: 11;
    order: 11;
  }
  .let-order-sm-12 {
    -ms-flex-order: 12;
    order: 12;
  }
  .let-offset-sm-0 {
    margin-left: 0;
  }
  .let-offset-sm-1 {
    margin-left: 8.333333%;
  }
  .let-offset-sm-2 {
    margin-left: 16.666667%;
  }
  .let-offset-sm-3 {
    margin-left: 25%;
  }
  .let-offset-sm-4 {
    margin-left: 33.333333%;
  }
  .let-offset-sm-5 {
    margin-left: 41.666667%;
  }
  .let-offset-sm-6 {
    margin-left: 50%;
  }
  .let-offset-sm-7 {
    margin-left: 58.333333%;
  }
  .let-offset-sm-8 {
    margin-left: 66.666667%;
  }
  .let-offset-sm-9 {
    margin-left: 75%;
  }
  .let-offset-sm-10 {
    margin-left: 83.333333%;
  }
  .let-offset-sm-11 {
    margin-left: 91.666667%;
  }
}

@media (min-width: 768px) {
  .let-col-md {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }
  .let-col-md-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .let-col-md-1 {
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }
  .let-col-md-2 {
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
  .let-col-md-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .let-col-md-4 {
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .let-col-md-5 {
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  .let-col-md-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .let-col-md-7 {
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
  .let-col-md-8 {
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  .let-col-md-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }
  .let-col-md-10 {
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }
  .let-col-md-11 {
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }
  .let-col-md-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .let-order-md-first {
    -ms-flex-order: -1;
    order: -1;
  }
  .let-order-md-last {
    -ms-flex-order: 13;
    order: 13;
  }
  .let-order-md-0 {
    -ms-flex-order: 0;
    order: 0;
  }
  .let-order-md-1 {
    -ms-flex-order: 1;
    order: 1;
  }
  .let-order-md-2 {
    -ms-flex-order: 2;
    order: 2;
  }
  .let-order-md-3 {
    -ms-flex-order: 3;
    order: 3;
  }
  .let-order-md-4 {
    -ms-flex-order: 4;
    order: 4;
  }
  .let-order-md-5 {
    -ms-flex-order: 5;
    order: 5;
  }
  .let-order-md-6 {
    -ms-flex-order: 6;
    order: 6;
  }
  .let-order-md-7 {
    -ms-flex-order: 7;
    order: 7;
  }
  .let-order-md-8 {
    -ms-flex-order: 8;
    order: 8;
  }
  .let-order-md-9 {
    -ms-flex-order: 9;
    order: 9;
  }
  .let-order-md-10 {
    -ms-flex-order: 10;
    order: 10;
  }
  .let-order-md-11 {
    -ms-flex-order: 11;
    order: 11;
  }
  .let-order-md-12 {
    -ms-flex-order: 12;
    order: 12;
  }
  .let-offset-md-0 {
    margin-left: 0;
  }
  .let-offset-md-1 {
    margin-left: 8.333333%;
  }
  .let-offset-md-2 {
    margin-left: 16.666667%;
  }
  .let-offset-md-3 {
    margin-left: 25%;
  }
  .let-offset-md-4 {
    margin-left: 33.333333%;
  }
  .let-offset-md-5 {
    margin-left: 41.666667%;
  }
  .let-offset-md-6 {
    margin-left: 50%;
  }
  .let-offset-md-7 {
    margin-left: 58.333333%;
  }
  .let-offset-md-8 {
    margin-left: 66.666667%;
  }
  .let-offset-md-9 {
    margin-left: 75%;
  }
  .let-offset-md-10 {
    margin-left: 83.333333%;
  }
  .let-offset-md-11 {
    margin-left: 91.666667%;
  }
}

@media (min-width: 992px) {
  .let-col-lg {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }
  .let-col-lg-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .let-col-lg-1 {
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }
  .let-col-lg-2 {
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
  .let-col-lg-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .let-col-lg-4 {
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .let-col-lg-5 {
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  .let-col-lg-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .let-col-lg-7 {
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
  .let-col-lg-8 {
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  .let-col-lg-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }
  .let-col-lg-10 {
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }
  .let-col-lg-11 {
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }
  .let-col-lg-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .let-order-lg-first {
    -ms-flex-order: -1;
    order: -1;
  }
  .let-order-lg-last {
    -ms-flex-order: 13;
    order: 13;
  }
  .let-order-lg-0 {
    -ms-flex-order: 0;
    order: 0;
  }
  .let-order-lg-1 {
    -ms-flex-order: 1;
    order: 1;
  }
  .let-order-lg-2 {
    -ms-flex-order: 2;
    order: 2;
  }
  .let-order-lg-3 {
    -ms-flex-order: 3;
    order: 3;
  }
  .let-order-lg-4 {
    -ms-flex-order: 4;
    order: 4;
  }
  .let-order-lg-5 {
    -ms-flex-order: 5;
    order: 5;
  }
  .let-order-lg-6 {
    -ms-flex-order: 6;
    order: 6;
  }
  .let-order-lg-7 {
    -ms-flex-order: 7;
    order: 7;
  }
  .let-order-lg-8 {
    -ms-flex-order: 8;
    order: 8;
  }
  .let-order-lg-9 {
    -ms-flex-order: 9;
    order: 9;
  }
  .let-order-lg-10 {
    -ms-flex-order: 10;
    order: 10;
  }
  .let-order-lg-11 {
    -ms-flex-order: 11;
    order: 11;
  }
  .let-order-lg-12 {
    -ms-flex-order: 12;
    order: 12;
  }
  .let-offset-lg-0 {
    margin-left: 0;
  }
  .let-offset-lg-1 {
    margin-left: 8.333333%;
  }
  .let-offset-lg-2 {
    margin-left: 16.666667%;
  }
  .let-offset-lg-3 {
    margin-left: 25%;
  }
  .let-offset-lg-4 {
    margin-left: 33.333333%;
  }
  .let-offset-lg-5 {
    margin-left: 41.666667%;
  }
  .let-offset-lg-6 {
    margin-left: 50%;
  }
  .let-offset-lg-7 {
    margin-left: 58.333333%;
  }
  .let-offset-lg-8 {
    margin-left: 66.666667%;
  }
  .let-offset-lg-9 {
    margin-left: 75%;
  }
  .let-offset-lg-10 {
    margin-left: 83.333333%;
  }
  .let-offset-lg-11 {
    margin-left: 91.666667%;
  }
}

@media (min-width: 1200px) {
  .let-col-xl {
    -ms-flex-preferred-size: 0;
    flex-basis: 0;
    -ms-flex-positive: 1;
    flex-grow: 1;
    max-width: 100%;
  }
  .let-col-xl-auto {
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    width: auto;
    max-width: 100%;
  }
  .let-col-xl-1 {
    -ms-flex: 0 0 8.333333%;
    flex: 0 0 8.333333%;
    max-width: 8.333333%;
  }
  .let-col-xl-2 {
    -ms-flex: 0 0 16.666667%;
    flex: 0 0 16.666667%;
    max-width: 16.666667%;
  }
  .let-col-xl-3 {
    -ms-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
  }
  .let-col-xl-4 {
    -ms-flex: 0 0 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  .let-col-xl-5 {
    -ms-flex: 0 0 41.666667%;
    flex: 0 0 41.666667%;
    max-width: 41.666667%;
  }
  .let-col-xl-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
  }
  .let-col-xl-7 {
    -ms-flex: 0 0 58.333333%;
    flex: 0 0 58.333333%;
    max-width: 58.333333%;
  }
  .let-col-xl-8 {
    -ms-flex: 0 0 66.666667%;
    flex: 0 0 66.666667%;
    max-width: 66.666667%;
  }
  .let-col-xl-9 {
    -ms-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
  }
  .let-col-xl-10 {
    -ms-flex: 0 0 83.333333%;
    flex: 0 0 83.333333%;
    max-width: 83.333333%;
  }
  .let-col-xl-11 {
    -ms-flex: 0 0 91.666667%;
    flex: 0 0 91.666667%;
    max-width: 91.666667%;
  }
  .let-col-xl-12 {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    max-width: 100%;
  }
  .let-order-xl-first {
    -ms-flex-order: -1;
    order: -1;
  }
  .let-order-xl-last {
    -ms-flex-order: 13;
    order: 13;
  }
  .let-order-xl-0 {
    -ms-flex-order: 0;
    order: 0;
  }
  .let-order-xl-1 {
    -ms-flex-order: 1;
    order: 1;
  }
  .let-order-xl-2 {
    -ms-flex-order: 2;
    order: 2;
  }
  .let-order-xl-3 {
    -ms-flex-order: 3;
    order: 3;
  }
  .let-order-xl-4 {
    -ms-flex-order: 4;
    order: 4;
  }
  .let-order-xl-5 {
    -ms-flex-order: 5;
    order: 5;
  }
  .let-order-xl-6 {
    -ms-flex-order: 6;
    order: 6;
  }
  .let-order-xl-7 {
    -ms-flex-order: 7;
    order: 7;
  }
  .let-order-xl-8 {
    -ms-flex-order: 8;
    order: 8;
  }
  .let-order-xl-9 {
    -ms-flex-order: 9;
    order: 9;
  }
  .let-order-xl-10 {
    -ms-flex-order: 10;
    order: 10;
  }
  .let-order-xl-11 {
    -ms-flex-order: 11;
    order: 11;
  }
  .let-order-xl-12 {
    -ms-flex-order: 12;
    order: 12;
  }
  .let-offset-xl-0 {
    margin-left: 0;
  }
  .let-offset-xl-1 {
    margin-left: 8.333333%;
  }
  .let-offset-xl-2 {
    margin-left: 16.666667%;
  }
  .let-offset-xl-3 {
    margin-left: 25%;
  }
  .let-offset-xl-4 {
    margin-left: 33.333333%;
  }
  .let-offset-xl-5 {
    margin-left: 41.666667%;
  }
  .let-offset-xl-6 {
    margin-left: 50%;
  }
  .let-offset-xl-7 {
    margin-left: 58.333333%;
  }
  .let-offset-xl-8 {
    margin-left: 66.666667%;
  }
  .let-offset-xl-9 {
    margin-left: 75%;
  }
  .let-offset-xl-10 {
    margin-left: 83.333333%;
  }
  .let-offset-xl-11 {
    margin-left: 91.666667%;
  }
}

.let-table {
  width: 100%;
  margin-bottom: 1rem;
  color: #212529;
}

.let-table th,
.let-table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid #dee2e6;
}

.let-table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #dee2e6;
}

.let-table tbody + tbody {
  border-top: 2px solid #dee2e6;
}

.let-table-sm th,
.let-table-sm td {
  padding: 0.3rem;
}

.let-table-bordered {
  border: 1px solid #dee2e6;
}

.let-table-bordered th,
.let-table-bordered td {
  border: 1px solid #dee2e6;
}

.let-table-bordered thead th,
.let-table-bordered thead td {
  border-bottom-width: 2px;
}

.let-table-borderless th,
.let-table-borderless td,
.let-table-borderless thead th,
.let-table-borderless tbody + tbody {
  border: 0;
}

.let-table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.05);
}

.let-table-hover tbody tr:hover {
  color: #212529;
  background-color: rgba(0, 0, 0, 0.075);
}

.let-table-primary,
.let-table-primary > th,
.let-table-primary > td {
  background-color: #b8daff;
}

.let-table-primary th,
.let-table-primary td,
.let-table-primary thead th,
.let-table-primary tbody + tbody {
  border-color: #7abaff;
}

.let-table-hover .let-table-primary:hover {
  background-color: #9fcdff;
}

.let-table-hover .let-table-primary:hover > td,
.let-table-hover .let-table-primary:hover > th {
  background-color: #9fcdff;
}

.let-table-secondary,
.let-table-secondary > th,
.let-table-secondary > td {
  background-color: #d6d8db;
}

.let-table-secondary th,
.let-table-secondary td,
.let-table-secondary thead th,
.let-table-secondary tbody + tbody {
  border-color: #b3b7bb;
}

.let-table-hover .let-table-secondary:hover {
  background-color: #c8cbcf;
}

.let-table-hover .let-table-secondary:hover > td,
.let-table-hover .let-table-secondary:hover > th {
  background-color: #c8cbcf;
}

.let-table-success,
.let-table-success > th,
.let-table-success > td {
  background-color: #c3e6cb;
}

.let-table-success th,
.let-table-success td,
.let-table-success thead th,
.let-table-success tbody + tbody {
  border-color: #8fd19e;
}

.let-table-hover .let-table-success:hover {
  background-color: #b1dfbb;
}

.let-table-hover .let-table-success:hover > td,
.let-table-hover .let-table-success:hover > th {
  background-color: #b1dfbb;
}

.let-table-info,
.let-table-info > th,
.let-table-info > td {
  background-color: #bee5eb;
}

.let-table-info th,
.let-table-info td,
.let-table-info thead th,
.let-table-info tbody + tbody {
  border-color: #86cfda;
}

.let-table-hover .let-table-info:hover {
  background-color: #abdde5;
}

.let-table-hover .let-table-info:hover > td,
.let-table-hover .let-table-info:hover > th {
  background-color: #abdde5;
}

.let-table-warning,
.let-table-warning > th,
.let-table-warning > td {
  background-color: #ffeeba;
}

.let-table-warning th,
.let-table-warning td,
.let-table-warning thead th,
.let-table-warning tbody + tbody {
  border-color: #ffdf7e;
}

.let-table-hover .let-table-warning:hover {
  background-color: #ffe8a1;
}

.let-table-hover .let-table-warning:hover > td,
.let-table-hover .let-table-warning:hover > th {
  background-color: #ffe8a1;
}

.let-table-danger,
.let-table-danger > th,
.let-table-danger > td {
  background-color: #f5c6cb;
}

.let-table-danger th,
.let-table-danger td,
.let-table-danger thead th,
.let-table-danger tbody + tbody {
  border-color: #ed969e;
}

.let-table-hover .let-table-danger:hover {
  background-color: #f1b0b7;
}

.let-table-hover .let-table-danger:hover > td,
.let-table-hover .let-table-danger:hover > th {
  background-color: #f1b0b7;
}

.let-table-light,
.let-table-light > th,
.let-table-light > td {
  background-color: #fdfdfe;
}

.let-table-light th,
.let-table-light td,
.let-table-light thead th,
.let-table-light tbody + tbody {
  border-color: #fbfcfc;
}

.let-table-hover .let-table-light:hover {
  background-color: #ececf6;
}

.let-table-hover .let-table-light:hover > td,
.let-table-hover .let-table-light:hover > th {
  background-color: #ececf6;
}

.let-table-dark,
.let-table-dark > th,
.let-table-dark > td {
  background-color: #c6c8ca;
}

.let-table-dark th,
.let-table-dark td,
.let-table-dark thead th,
.let-table-dark tbody + tbody {
  border-color: #95999c;
}

.let-table-hover .let-table-dark:hover {
  background-color: #b9bbbe;
}

.let-table-hover .let-table-dark:hover > td,
.let-table-hover .let-table-dark:hover > th {
  background-color: #b9bbbe;
}

.let-table-active,
.let-table-active > th,
.let-table-active > td {
  background-color: rgba(0, 0, 0, 0.075);
}

.let-table-hover .let-table-active:hover {
  background-color: rgba(0, 0, 0, 0.075);
}

.let-table-hover .let-table-active:hover > td,
.let-table-hover .let-table-active:hover > th {
  background-color: rgba(0, 0, 0, 0.075);
}

.let-table .let-thead-dark th {
  color: #fff;
  background-color: #343a40;
  border-color: #454d55;
}

.let-table .let-thead-light th {
  color: #495057;
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.let-table-dark {
  color: #fff;
  background-color: #343a40;
}

.let-table-dark th,
.let-table-dark td,
.let-table-dark thead th {
  border-color: #454d55;
}

.let-table-dark.let-table-bordered {
  border: 0;
}

.let-table-dark.let-table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05);
}

.let-table-dark.let-table-hover tbody tr:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.075);
}

@media (max-width: 575.98px) {
  .let-table-responsive-sm {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .let-table-responsive-sm > .let-table-bordered {
    border: 0;
  }
}

@media (max-width: 767.98px) {
  .let-table-responsive-md {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .let-table-responsive-md > .let-table-bordered {
    border: 0;
  }
}

@media (max-width: 991.98px) {
  .let-table-responsive-lg {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .let-table-responsive-lg > .let-table-bordered {
    border: 0;
  }
}

@media (max-width: 1199.98px) {
  .let-table-responsive-xl {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  .let-table-responsive-xl > .let-table-bordered {
    border: 0;
  }
}

.let-table-responsive {
  display: block;
  width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.let-table-responsive > .let-table-bordered {
  border: 0;
}

.let-form-control {
  display: block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-size: 0.8rem!important;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
  .let-form-control {
    transition: none;
  }
}

.let-form-control::-ms-expand {
  background-color: transparent;
  border: 0;
}

.let-form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.let-form-control::-webkit-input-placeholder {
  color: #6c757d;
  opacity: 1;
}

.let-form-control::-moz-placeholder {
  color: #6c757d;
  opacity: 1;
}

.let-form-control:-ms-input-placeholder {
  color: #6c757d;
  opacity: 1;
}

.let-form-control::-ms-input-placeholder {
  color: #6c757d;
  opacity: 1;
}

.let-form-control::placeholder {
  color: #6c757d;
  opacity: 1;
}

.let-form-control:disabled, .let-form-control[readonly] {
  background-color: #e9ecef;
  opacity: 1;
}

select.let-form-control:focus::-ms-value {
  color: #495057;
  background-color: #fff;
}

.let-form-control-file,
.let-form-control-range {
  display: block;
  width: 100%;
}

.let-col-form-label {
  padding-top: calc(0.375rem + 1px);
  padding-bottom: calc(0.375rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.5;
}

.let-col-form-label-lg {
  padding-top: calc(0.5rem + 1px);
  padding-bottom: calc(0.5rem + 1px);
  font-size: 1.25rem;
  line-height: 1.5;
}

.let-col-form-label-sm {
  padding-top: calc(0.25rem + 1px);
  padding-bottom: calc(0.25rem + 1px);
  font-size: 0.875rem;
  line-height: 1.5;
}

.let-form-control-plaintext {
  display: block;
  width: 100%;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
  margin-bottom: 0;
  line-height: 1.5;
  color: #212529;
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0;
}

.let-form-control-plaintext.let-form-control-sm, .let-form-control-plaintext.let-form-control-lg {
  padding-right: 0;
  padding-left: 0;
}

.let-form-control-sm {
  height: calc(1.5em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.let-form-control-lg {
  height: calc(1.5em + 1rem + 2px);
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

select.let-form-control[size], select.let-form-control[multiple] {
  height: auto;
}

textarea.let-form-control {
  height: auto;
}

.let-form-group {
  margin-bottom: 1rem;
}

.let-form-text {
  display: block;
  margin-top: 0.25rem;
}

.let-form-row {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -5px;
  margin-left: -5px;
}

.let-form-row > .let-col,
.let-form-row > [class*="col-"] {
  padding-right: 5px;
  padding-left: 5px;
}

.let-form-check {
  position: relative;
  display: block;
  padding-left: 1.25rem;
}

.let-form-check-input {
  position: absolute;
  margin-top: 0.3rem;
  margin-left: -1.25rem;
}

.let-form-check-input:disabled ~ .let-form-check-label {
  color: #6c757d;
}

.let-form-check-label {
  margin-bottom: 0;
}

.let-form-check-inline {
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-align: center;
  align-items: center;
  padding-left: 0;
  margin-right: 0.75rem;
}

.let-form-check-inline .let-form-check-input {
  position: static;
  margin-top: 0;
  margin-right: 0.3125rem;
  margin-left: 0;
}

.let-valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #28a745;
}

.let-valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: .let-1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #fff;
  background-color: rgba(40, 167, 69, 0.9);
  border-radius: 0.25rem;
}

.let-was-validated .let-form-control:valid, .let-form-control.let-is-valid {
  border-color: #28a745;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.let-w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.let-6 4.53c-.let-4-1.04.46-1.4 1.1-.let-8l1.1 1.4 3.4-3.8c.let-6-.let-63 1.6-.let-27 1.2.7l-4 4.6c-.let-43.5-.let-8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center right calc(0.375em + 0.1875rem);
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.let-was-validated .let-form-control:valid:focus, .let-form-control.let-is-valid:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.let-was-validated .let-form-control:valid ~ .let-valid-feedback,
.let-was-validated .let-form-control:valid ~ .let-valid-tooltip, .let-form-control.let-is-valid ~ .let-valid-feedback,
.let-form-control.let-is-valid ~ .let-valid-tooltip {
  display: block;
}

.let-was-validated textarea.let-form-control:valid, textarea.let-form-control.let-is-valid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}

.let-was-validated .let-custom-select:valid, .let-custom-select.let-is-valid {
  border-color: #28a745;
  padding-right: calc((1em + 0.75rem) * 3 / 4 + 1.75rem);
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.let-w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px, url("data:image/svg+xml,%3csvg xmlns='http://www.let-w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.let-6 4.53c-.let-4-1.04.46-1.4 1.1-.let-8l1.1 1.4 3.4-3.8c.let-6-.let-63 1.6-.let-27 1.2.7l-4 4.6c-.let-43.5-.let-8.4-1.1.1z'/%3e%3c/svg%3e") #fff no-repeat center right 1.75rem/calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.let-was-validated .let-custom-select:valid:focus, .let-custom-select.let-is-valid:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.let-was-validated .let-custom-select:valid ~ .let-valid-feedback,
.let-was-validated .let-custom-select:valid ~ .let-valid-tooltip, .let-custom-select.let-is-valid ~ .let-valid-feedback,
.let-custom-select.let-is-valid ~ .let-valid-tooltip {
  display: block;
}

.let-was-validated .let-form-control-file:valid ~ .let-valid-feedback,
.let-was-validated .let-form-control-file:valid ~ .let-valid-tooltip, .let-form-control-file.let-is-valid ~ .let-valid-feedback,
.let-form-control-file.let-is-valid ~ .let-valid-tooltip {
  display: block;
}

.let-was-validated .let-form-check-input:valid ~ .let-form-check-label, .let-form-check-input.let-is-valid ~ .let-form-check-label {
  color: #28a745;
}

.let-was-validated .let-form-check-input:valid ~ .let-valid-feedback,
.let-was-validated .let-form-check-input:valid ~ .let-valid-tooltip, .let-form-check-input.let-is-valid ~ .let-valid-feedback,
.let-form-check-input.let-is-valid ~ .let-valid-tooltip {
  display: block;
}

.let-was-validated .let-custom-control-input:valid ~ .let-custom-control-label, .let-custom-control-input.let-is-valid ~ .let-custom-control-label {
  color: #28a745;
}

.let-was-validated .let-custom-control-input:valid ~ .let-custom-control-label::before, .let-custom-control-input.let-is-valid ~ .let-custom-control-label::before {
  border-color: #28a745;
}

.let-was-validated .let-custom-control-input:valid ~ .let-valid-feedback,
.let-was-validated .let-custom-control-input:valid ~ .let-valid-tooltip, .let-custom-control-input.let-is-valid ~ .let-valid-feedback,
.let-custom-control-input.let-is-valid ~ .let-valid-tooltip {
  display: block;
}

.let-was-validated .let-custom-control-input:valid:checked ~ .let-custom-control-label::before, .let-custom-control-input.let-is-valid:checked ~ .let-custom-control-label::before {
  border-color: #34ce57;
  background-color: #34ce57;
}

.let-was-validated .let-custom-control-input:valid:focus ~ .let-custom-control-label::before, .let-custom-control-input.let-is-valid:focus ~ .let-custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.let-was-validated .let-custom-control-input:valid:focus:not(:checked) ~ .let-custom-control-label::before, .let-custom-control-input.let-is-valid:focus:not(:checked) ~ .let-custom-control-label::before {
  border-color: #28a745;
}

.let-was-validated .let-custom-file-input:valid ~ .let-custom-file-label, .let-custom-file-input.let-is-valid ~ .let-custom-file-label {
  border-color: #28a745;
}

.let-was-validated .let-custom-file-input:valid ~ .let-valid-feedback,
.let-was-validated .let-custom-file-input:valid ~ .let-valid-tooltip, .let-custom-file-input.let-is-valid ~ .let-valid-feedback,
.let-custom-file-input.let-is-valid ~ .let-valid-tooltip {
  display: block;
}

.let-was-validated .let-custom-file-input:valid:focus ~ .let-custom-file-label, .let-custom-file-input.let-is-valid:focus ~ .let-custom-file-label {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.let-invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 80%;
  color: #dc3545;
}

.let-invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: .let-1rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #fff;
  background-color: rgba(220, 53, 69, 0.9);
  border-radius: 0.25rem;
}

.let-was-validated .let-form-control:invalid, .let-form-control.let-is-invalid {
  border-color: #dc3545;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.let-w3.org/2000/svg' fill='%23dc3545' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23dc3545' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.let-5'/%3e%3ccircle cx='3' r='.let-5'/%3e%3ccircle cy='3' r='.let-5'/%3e%3ccircle cx='3' cy='3' r='.let-5'/%3e%3c/svg%3E");
  background-repeat: no-repeat;
  background-position: center right calc(0.375em + 0.1875rem);
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.let-was-validated .let-form-control:invalid:focus, .let-form-control.let-is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.let-was-validated .let-form-control:invalid ~ .let-invalid-feedback,
.let-was-validated .let-form-control:invalid ~ .let-invalid-tooltip, .let-form-control.let-is-invalid ~ .let-invalid-feedback,
.let-form-control.let-is-invalid ~ .let-invalid-tooltip {
  display: block;
}

.let-was-validated textarea.let-form-control:invalid, textarea.let-form-control.let-is-invalid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}

.let-was-validated .let-custom-select:invalid, .let-custom-select.let-is-invalid {
  border-color: #dc3545;
  padding-right: calc((1em + 0.75rem) * 3 / 4 + 1.75rem);
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.let-w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px, url("data:image/svg+xml,%3csvg xmlns='http://www.let-w3.org/2000/svg' fill='%23dc3545' viewBox='-2 -2 7 7'%3e%3cpath stroke='%23dc3545' d='M0 0l3 3m0-3L0 3'/%3e%3ccircle r='.let-5'/%3e%3ccircle cx='3' r='.let-5'/%3e%3ccircle cy='3' r='.let-5'/%3e%3ccircle cx='3' cy='3' r='.let-5'/%3e%3c/svg%3E") #fff no-repeat center right 1.75rem/calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.let-was-validated .let-custom-select:invalid:focus, .let-custom-select.let-is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.let-was-validated .let-custom-select:invalid ~ .let-invalid-feedback,
.let-was-validated .let-custom-select:invalid ~ .let-invalid-tooltip, .let-custom-select.let-is-invalid ~ .let-invalid-feedback,
.let-custom-select.let-is-invalid ~ .let-invalid-tooltip {
  display: block;
}

.let-was-validated .let-form-control-file:invalid ~ .let-invalid-feedback,
.let-was-validated .let-form-control-file:invalid ~ .let-invalid-tooltip, .let-form-control-file.let-is-invalid ~ .let-invalid-feedback,
.let-form-control-file.let-is-invalid ~ .let-invalid-tooltip {
  display: block;
}

.let-was-validated .let-form-check-input:invalid ~ .let-form-check-label, .let-form-check-input.let-is-invalid ~ .let-form-check-label {
  color: #dc3545;
}

.let-was-validated .let-form-check-input:invalid ~ .let-invalid-feedback,
.let-was-validated .let-form-check-input:invalid ~ .let-invalid-tooltip, .let-form-check-input.let-is-invalid ~ .let-invalid-feedback,
.let-form-check-input.let-is-invalid ~ .let-invalid-tooltip {
  display: block;
}

.let-was-validated .let-custom-control-input:invalid ~ .let-custom-control-label, .let-custom-control-input.let-is-invalid ~ .let-custom-control-label {
  color: #dc3545;
}

.let-was-validated .let-custom-control-input:invalid ~ .let-custom-control-label::before, .let-custom-control-input.let-is-invalid ~ .let-custom-control-label::before {
  border-color: #dc3545;
}

.let-was-validated .let-custom-control-input:invalid ~ .let-invalid-feedback,
.let-was-validated .let-custom-control-input:invalid ~ .let-invalid-tooltip, .let-custom-control-input.let-is-invalid ~ .let-invalid-feedback,
.let-custom-control-input.let-is-invalid ~ .let-invalid-tooltip {
  display: block;
}

.let-was-validated .let-custom-control-input:invalid:checked ~ .let-custom-control-label::before, .let-custom-control-input.let-is-invalid:checked ~ .let-custom-control-label::before {
  border-color: #e4606d;
  background-color: #e4606d;
}

.let-was-validated .let-custom-control-input:invalid:focus ~ .let-custom-control-label::before, .let-custom-control-input.let-is-invalid:focus ~ .let-custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.let-was-validated .let-custom-control-input:invalid:focus:not(:checked) ~ .let-custom-control-label::before, .let-custom-control-input.let-is-invalid:focus:not(:checked) ~ .let-custom-control-label::before {
  border-color: #dc3545;
}

.let-was-validated .let-custom-file-input:invalid ~ .let-custom-file-label, .let-custom-file-input.let-is-invalid ~ .let-custom-file-label {
  border-color: #dc3545;
}

.let-was-validated .let-custom-file-input:invalid ~ .let-invalid-feedback,
.let-was-validated .let-custom-file-input:invalid ~ .let-invalid-tooltip, .let-custom-file-input.let-is-invalid ~ .let-invalid-feedback,
.let-custom-file-input.let-is-invalid ~ .let-invalid-tooltip {
  display: block;
}

.let-was-validated .let-custom-file-input:invalid:focus ~ .let-custom-file-label, .let-custom-file-input.let-is-invalid:focus ~ .let-custom-file-label {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.let-form-inline {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-flow: row wrap;
  flex-flow: row wrap;
  -ms-flex-align: center;
  align-items: center;
}

.let-form-inline .let-form-check {
  width: 100%;
}

@media (min-width: 576px) {
  .let-form-inline label {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
    margin-bottom: 0;
  }
  .let-form-inline .let-form-group {
    display: -ms-flexbox;
    display: flex;
    -ms-flex: 0 0 auto;
    flex: 0 0 auto;
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    -ms-flex-align: center;
    align-items: center;
    margin-bottom: 0;
  }
  .let-form-inline .let-form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .let-form-inline .let-form-control-plaintext {
    display: inline-block;
  }
  .let-form-inline .let-input-group,
  .let-form-inline .let-custom-select {
    width: auto;
  }
  .let-form-inline .let-form-check {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
    width: auto;
    padding-left: 0;
  }
  .let-form-inline .let-form-check-input {
    position: relative;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    margin-top: 0;
    margin-right: 0.25rem;
    margin-left: 0;
  }
  .let-form-inline .let-custom-control {
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
  .let-form-inline .let-custom-control-label {
    margin-bottom: 0;
  }
}

.let-btn {
  display: inline-block;
  font-weight: 400;
  color: #212529;
  text-align: center;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-color: transparent;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
  .let-btn {
    transition: none;
  }
}

.let-btn:hover {
  color: #212529;
  text-decoration: none;
}

.let-btn:focus, .let-btn.let-focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.let-btn.let-disabled, .let-btn:disabled {
  opacity: 0.65;
}

a.let-btn.let-disabled,
fieldset:disabled a.let-btn {
  pointer-events: none;
}

.let-btn-primary {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.let-btn-primary:hover {
  color: #fff;
  background-color: #0069d9;
  border-color: #0062cc;
}

.let-btn-primary:focus, .let-btn-primary.let-focus {
  box-shadow: 0 0 0 0.2rem rgba(38, 143, 255, 0.5);
}

.let-btn-primary.let-disabled, .let-btn-primary:disabled {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.let-btn-primary:not(:disabled):not(.let-disabled):active, .let-btn-primary:not(:disabled):not(.let-disabled).let-active,
.let-show > .let-btn-primary.let-dropdown-toggle {
  color: #fff;
  background-color: #0062cc;
  border-color: #005cbf;
}

.let-btn-primary:not(:disabled):not(.let-disabled):active:focus, .let-btn-primary:not(:disabled):not(.let-disabled).let-active:focus,
.let-show > .let-btn-primary.let-dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(38, 143, 255, 0.5);
}

.let-btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.let-btn-secondary:hover {
  color: #fff;
  background-color: #5a6268;
  border-color: #545b62;
}

.let-btn-secondary:focus, .let-btn-secondary.let-focus {
  box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);
}

.let-btn-secondary.let-disabled, .let-btn-secondary:disabled {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.let-btn-secondary:not(:disabled):not(.let-disabled):active, .let-btn-secondary:not(:disabled):not(.let-disabled).let-active,
.let-show > .let-btn-secondary.let-dropdown-toggle {
  color: #fff;
  background-color: #545b62;
  border-color: #4e555b;
}

.let-btn-secondary:not(:disabled):not(.let-disabled):active:focus, .let-btn-secondary:not(:disabled):not(.let-disabled).let-active:focus,
.let-show > .let-btn-secondary.let-dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(130, 138, 145, 0.5);
}

.let-btn-success {
  color: #fff;
  background-color: #1ebdce;
  border-color: #1ebdce;
}

.let-btn-success:hover {
  color: #1ebdce;
  background-color: transparent!important;
  border-color: #1ebdce;
}

.let-btn-success:focus, .let-btn-success.let-focus {
  box-shadow: 0 0 0 0.2rem rgba(72, 180, 97, 0.5);
}

.let-btn-success.let-disabled, .let-btn-success:disabled {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}

.let-btn-success:not(:disabled):not(.let-disabled):active, .let-btn-success:not(:disabled):not(.let-disabled).let-active,
.let-show > .let-btn-success.let-dropdown-toggle {
  color: #fff;
  background-color: #1e7e34;
  border-color: #1c7430;
}

.let-btn-success:not(:disabled):not(.let-disabled):active:focus, .let-btn-success:not(:disabled):not(.let-disabled).let-active:focus,
.let-show > .let-btn-success.let-dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(72, 180, 97, 0.5);
}

.let-btn-info {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.let-btn-info:hover {
  color: #fff;
  background-color: #138496;
  border-color: #117a8b;
}

.let-btn-info:focus, .let-btn-info.let-focus {
  box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5);
}

.let-btn-info.let-disabled, .let-btn-info:disabled {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.let-btn-info:not(:disabled):not(.let-disabled):active, .let-btn-info:not(:disabled):not(.let-disabled).let-active,
.let-show > .let-btn-info.let-dropdown-toggle {
  color: #fff;
  background-color: #117a8b;
  border-color: #10707f;
}

.let-btn-info:not(:disabled):not(.let-disabled):active:focus, .let-btn-info:not(:disabled):not(.let-disabled).let-active:focus,
.let-show > .let-btn-info.let-dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(58, 176, 195, 0.5);
}

.let-btn-warning {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}

.let-btn-warning:hover {
  color: #212529;
  background-color: #e0a800;
  border-color: #d39e00;
}

.let-btn-warning:focus, .let-btn-warning.let-focus {
  box-shadow: 0 0 0 0.2rem rgba(222, 170, 12, 0.5);
}

.let-btn-warning.let-disabled, .let-btn-warning:disabled {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}

.let-btn-warning:not(:disabled):not(.let-disabled):active, .let-btn-warning:not(:disabled):not(.let-disabled).let-active,
.let-show > .let-btn-warning.let-dropdown-toggle {
  color: #212529;
  background-color: #d39e00;
  border-color: #c69500;
}

.let-btn-warning:not(:disabled):not(.let-disabled):active:focus, .let-btn-warning:not(:disabled):not(.let-disabled).let-active:focus,
.let-show > .let-btn-warning.let-dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(222, 170, 12, 0.5);
}

.let-btn-danger {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

.let-btn-danger:hover {
  color: #fff;
  background-color: #c82333;
  border-color: #bd2130;
}

.let-btn-danger:focus, .let-btn-danger.let-focus {
  box-shadow: 0 0 0 0.2rem rgba(225, 83, 97, 0.5);
}

.let-btn-danger.let-disabled, .let-btn-danger:disabled {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

.let-btn-danger:not(:disabled):not(.let-disabled):active, .let-btn-danger:not(:disabled):not(.let-disabled).let-active,
.let-show > .let-btn-danger.let-dropdown-toggle {
  color: #fff;
  background-color: #bd2130;
  border-color: #b21f2d;
}

.let-btn-danger:not(:disabled):not(.let-disabled):active:focus, .let-btn-danger:not(:disabled):not(.let-disabled).let-active:focus,
.let-show > .let-btn-danger.let-dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(225, 83, 97, 0.5);
}

.let-btn-light {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.let-btn-light:hover {
  color: #212529;
  background-color: #e2e6ea;
  border-color: #dae0e5;
}

.let-btn-light:focus, .let-btn-light.let-focus {
  box-shadow: 0 0 0 0.2rem rgba(216, 217, 219, 0.5);
}

.let-btn-light.let-disabled, .let-btn-light:disabled {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.let-btn-light:not(:disabled):not(.let-disabled):active, .let-btn-light:not(:disabled):not(.let-disabled).let-active,
.let-show > .let-btn-light.let-dropdown-toggle {
  color: #212529;
  background-color: #dae0e5;
  border-color: #d3d9df;
}

.let-btn-light:not(:disabled):not(.let-disabled):active:focus, .let-btn-light:not(:disabled):not(.let-disabled).let-active:focus,
.let-show > .let-btn-light.let-dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(216, 217, 219, 0.5);
}

.let-btn-dark {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

.let-btn-dark:hover {
  color: #fff;
  background-color: #23272b;
  border-color: #1d2124;
}

.let-btn-dark:focus, .let-btn-dark.let-focus {
  box-shadow: 0 0 0 0.2rem rgba(82, 88, 93, 0.5);
}

.let-btn-dark.let-disabled, .let-btn-dark:disabled {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

.let-btn-dark:not(:disabled):not(.let-disabled):active, .let-btn-dark:not(:disabled):not(.let-disabled).let-active,
.let-show > .let-btn-dark.let-dropdown-toggle {
  color: #fff;
  background-color: #1d2124;
  border-color: #171a1d;
}

.let-btn-dark:not(:disabled):not(.let-disabled):active:focus, .let-btn-dark:not(:disabled):not(.let-disabled).let-active:focus,
.let-show > .let-btn-dark.let-dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(82, 88, 93, 0.5);
}

.let-btn-outline-primary {
  color: #007bff;
  border-color: #007bff;
}

.let-btn-outline-primary:hover {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.let-btn-outline-primary:focus, .let-btn-outline-primary.let-focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}

.let-btn-outline-primary.let-disabled, .let-btn-outline-primary:disabled {
  color: #007bff;
  background-color: transparent;
}

.let-btn-outline-primary:not(:disabled):not(.let-disabled):active, .let-btn-outline-primary:not(:disabled):not(.let-disabled).let-active,
.let-show > .let-btn-outline-primary.let-dropdown-toggle {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.let-btn-outline-primary:not(:disabled):not(.let-disabled):active:focus, .let-btn-outline-primary:not(:disabled):not(.let-disabled).let-active:focus,
.let-show > .let-btn-outline-primary.let-dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}

.let-btn-outline-secondary {
  color: #6c757d;
  border-color: #6c757d;
}

.let-btn-outline-secondary:hover {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.let-btn-outline-secondary:focus, .let-btn-outline-secondary.let-focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

.let-btn-outline-secondary.let-disabled, .let-btn-outline-secondary:disabled {
  color: #6c757d;
  background-color: transparent;
}

.let-btn-outline-secondary:not(:disabled):not(.let-disabled):active, .let-btn-outline-secondary:not(:disabled):not(.let-disabled).let-active,
.let-show > .let-btn-outline-secondary.let-dropdown-toggle {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.let-btn-outline-secondary:not(:disabled):not(.let-disabled):active:focus, .let-btn-outline-secondary:not(:disabled):not(.let-disabled).let-active:focus,
.let-show > .let-btn-outline-secondary.let-dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

.let-btn-outline-success {
  color: #28a745;
  border-color: #28a745;
}

.let-btn-outline-success:hover {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}

.let-btn-outline-success:focus, .let-btn-outline-success.let-focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}

.let-btn-outline-success.let-disabled, .let-btn-outline-success:disabled {
  color: #28a745;
  background-color: transparent;
}

.let-btn-outline-success:not(:disabled):not(.let-disabled):active, .let-btn-outline-success:not(:disabled):not(.let-disabled).let-active,
.let-show > .let-btn-outline-success.let-dropdown-toggle {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}

.let-btn-outline-success:not(:disabled):not(.let-disabled):active:focus, .let-btn-outline-success:not(:disabled):not(.let-disabled).let-active:focus,
.let-show > .let-btn-outline-success.let-dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}

.let-btn-outline-info {
  color: #17a2b8;
  border-color: #17a2b8;
}

.let-btn-outline-info:hover {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.let-btn-outline-info:focus, .let-btn-outline-info.let-focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

.let-btn-outline-info.let-disabled, .let-btn-outline-info:disabled {
  color: #17a2b8;
  background-color: transparent;
}

.let-btn-outline-info:not(:disabled):not(.let-disabled):active, .let-btn-outline-info:not(:disabled):not(.let-disabled).let-active,
.let-show > .let-btn-outline-info.let-dropdown-toggle {
  color: #fff;
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.let-btn-outline-info:not(:disabled):not(.let-disabled):active:focus, .let-btn-outline-info:not(:disabled):not(.let-disabled).let-active:focus,
.let-show > .let-btn-outline-info.let-dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

.let-btn-outline-warning {
  color: #ffc107;
  border-color: #ffc107;
}

.let-btn-outline-warning:hover {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}

.let-btn-outline-warning:focus, .let-btn-outline-warning.let-focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}

.let-btn-outline-warning.let-disabled, .let-btn-outline-warning:disabled {
  color: #ffc107;
  background-color: transparent;
}

.let-btn-outline-warning:not(:disabled):not(.let-disabled):active, .let-btn-outline-warning:not(:disabled):not(.let-disabled).let-active,
.let-show > .let-btn-outline-warning.let-dropdown-toggle {
  color: #212529;
  background-color: #ffc107;
  border-color: #ffc107;
}

.let-btn-outline-warning:not(:disabled):not(.let-disabled):active:focus, .let-btn-outline-warning:not(:disabled):not(.let-disabled).let-active:focus,
.let-show > .let-btn-outline-warning.let-dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}

.let-btn-outline-danger {
  color: #dc3545;
  border-color: #dc3545;
}

.let-btn-outline-danger:hover {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

.let-btn-outline-danger:focus, .let-btn-outline-danger.let-focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}

.let-btn-outline-danger.let-disabled, .let-btn-outline-danger:disabled {
  color: #dc3545;
  background-color: transparent;
}

.let-btn-outline-danger:not(:disabled):not(.let-disabled):active, .let-btn-outline-danger:not(:disabled):not(.let-disabled).let-active,
.let-show > .let-btn-outline-danger.let-dropdown-toggle {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

.let-btn-outline-danger:not(:disabled):not(.let-disabled):active:focus, .let-btn-outline-danger:not(:disabled):not(.let-disabled).let-active:focus,
.let-show > .let-btn-outline-danger.let-dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}

.let-btn-outline-light {
  color: #f8f9fa;
  border-color: #f8f9fa;
}

.let-btn-outline-light:hover {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.let-btn-outline-light:focus, .let-btn-outline-light.let-focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}

.let-btn-outline-light.let-disabled, .let-btn-outline-light:disabled {
  color: #f8f9fa;
  background-color: transparent;
}

.let-btn-outline-light:not(:disabled):not(.let-disabled):active, .let-btn-outline-light:not(:disabled):not(.let-disabled).let-active,
.let-show > .let-btn-outline-light.let-dropdown-toggle {
  color: #212529;
  background-color: #f8f9fa;
  border-color: #f8f9fa;
}

.let-btn-outline-light:not(:disabled):not(.let-disabled):active:focus, .let-btn-outline-light:not(:disabled):not(.let-disabled).let-active:focus,
.let-show > .let-btn-outline-light.let-dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}

.let-btn-outline-dark {
  color: #343a40;
  border-color: #343a40;
}

.let-btn-outline-dark:hover {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

.let-btn-outline-dark:focus, .let-btn-outline-dark.let-focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}

.let-btn-outline-dark.let-disabled, .let-btn-outline-dark:disabled {
  color: #343a40;
  background-color: transparent;
}

.let-btn-outline-dark:not(:disabled):not(.let-disabled):active, .let-btn-outline-dark:not(:disabled):not(.let-disabled).let-active,
.let-show > .let-btn-outline-dark.let-dropdown-toggle {
  color: #fff;
  background-color: #343a40;
  border-color: #343a40;
}

.let-btn-outline-dark:not(:disabled):not(.let-disabled):active:focus, .let-btn-outline-dark:not(:disabled):not(.let-disabled).let-active:focus,
.let-show > .let-btn-outline-dark.let-dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}

.let-btn-link {
  font-weight: 400;
  color: #007bff;
  text-decoration: none;
}

.let-btn-link:hover {
  color: #0056b3;
  text-decoration: underline;
}

.let-btn-link:focus, .let-btn-link.let-focus {
  text-decoration: underline;
  box-shadow: none;
}

.let-btn-link:disabled, .let-btn-link.let-disabled {
  color: #6c757d;
  pointer-events: none;
}

.let-btn-lg, .let-btn-group-lg > .let-btn {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

.let-btn-sm, .let-btn-group-sm > .let-btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.let-btn-block {
  display: block;
  width: 100%;
}

.let-btn-block + .let-btn-block {
  margin-top: 0.5rem;
}

input[type="submit"].let-btn-block,
input[type="reset"].let-btn-block,
input[type="button"].let-btn-block {
  width: 100%;
}

.let-fade {
  transition: opacity 0.15s linear;
}

@media (prefers-reduced-motion: reduce) {
  .let-fade {
    transition: none;
  }
}

.let-fade:not(.let-show) {
  opacity: 0;
}

.let-collapse:not(.let-show) {
  display: none;
}

.let-collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}

@media (prefers-reduced-motion: reduce) {
  .let-collapsing {
    transition: none;
  }
}

.let-dropup,
.let-dropright,
.let-dropdown,
.let-dropleft {
  position: relative;
}

.let-dropdown-toggle {
  white-space: nowrap;
}

.let-dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}

.let-dropdown-toggle:empty::after {
  margin-left: 0;
}

.let-dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 1rem;
  color: #212529;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
}

.let-dropdown-menu-left {
  right: auto;
  left: 0;
}

.let-dropdown-menu-right {
  right: 0;
  left: auto;
}

@media (min-width: 576px) {
  .let-dropdown-menu-sm-left {
    right: auto;
    left: 0;
  }
  .let-dropdown-menu-sm-right {
    right: 0;
    left: auto;
  }
}

@media (min-width: 768px) {
  .let-dropdown-menu-md-left {
    right: auto;
    left: 0;
  }
  .let-dropdown-menu-md-right {
    right: 0;
    left: auto;
  }
}

@media (min-width: 992px) {
  .let-dropdown-menu-lg-left {
    right: auto;
    left: 0;
  }
  .let-dropdown-menu-lg-right {
    right: 0;
    left: auto;
  }
}

@media (min-width: 1200px) {
  .let-dropdown-menu-xl-left {
    right: auto;
    left: 0;
  }
  .let-dropdown-menu-xl-right {
    right: 0;
    left: auto;
  }
}

.let-dropup .let-dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: 0.125rem;
}

.let-dropup .let-dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0;
  border-right: 0.3em solid transparent;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent;
}

.let-dropup .let-dropdown-toggle:empty::after {
  margin-left: 0;
}

.let-dropright .let-dropdown-menu {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: 0.125rem;
}

.let-dropright .let-dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
}

.let-dropright .let-dropdown-toggle:empty::after {
  margin-left: 0;
}

.let-dropright .let-dropdown-toggle::after {
  vertical-align: 0;
}

.let-dropleft .let-dropdown-menu {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: 0.125rem;
}

.let-dropleft .let-dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
}

.let-dropleft .let-dropdown-toggle::after {
  display: none;
}

.let-dropleft .let-dropdown-toggle::before {
  display: inline-block;
  margin-right: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0.3em solid;
  border-bottom: 0.3em solid transparent;
}

.let-dropleft .let-dropdown-toggle:empty::after {
  margin-left: 0;
}

.let-dropleft .let-dropdown-toggle::before {
  vertical-align: 0;
}

.let-dropdown-menu[x-placement^="top"], .let-dropdown-menu[x-placement^="right"], .let-dropdown-menu[x-placement^="bottom"], .let-dropdown-menu[x-placement^="left"] {
  right: auto;
  bottom: auto;
}

.let-dropdown-divider {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid #e9ecef;
}

.let-dropdown-item {
  display: block;
  width: 100%;
  padding: 0.25rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}

.let-dropdown-item:hover, .let-dropdown-item:focus {
  color: #16181b;
  text-decoration: none;
  background-color: #f8f9fa;
}

.let-dropdown-item.let-active, .let-dropdown-item:active {
  color: #fff;
  text-decoration: none;
  background-color: #007bff;
}

.let-dropdown-item.let-disabled, .let-dropdown-item:disabled {
  color: #6c757d;
  pointer-events: none;
  background-color: transparent;
}

.let-dropdown-menu.let-show {
  display: block;
}

.let-dropdown-header {
  display: block;
  padding: 0.5rem 1.5rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  color: #6c757d;
  white-space: nowrap;
}

.let-dropdown-item-text {
  display: block;
  padding: 0.25rem 1.5rem;
  color: #212529;
}

.let-btn-group,
.let-btn-group-vertical {
  position: relative;
  display: -ms-inline-flexbox;
  display: inline-flex;
  vertical-align: middle;
}

.let-btn-group > .let-btn,
.let-btn-group-vertical > .let-btn {
  position: relative;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
}

.let-btn-group > .let-btn:hover,
.let-btn-group-vertical > .let-btn:hover {
  z-index: 1;
}

.let-btn-group > .let-btn:focus, .let-btn-group > .let-btn:active, .let-btn-group > .let-btn.let-active,
.let-btn-group-vertical > .let-btn:focus,
.let-btn-group-vertical > .let-btn:active,
.let-btn-group-vertical > .let-btn.let-active {
  z-index: 1;
}

.let-btn-toolbar {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.let-btn-toolbar .let-input-group {
  width: auto;
}

.let-btn-group > .let-btn:not(:first-child),
.let-btn-group > .let-btn-group:not(:first-child) {
  margin-left: -1px;
}

.let-btn-group > .let-btn:not(:last-child):not(.let-dropdown-toggle),
.let-btn-group > .let-btn-group:not(:last-child) > .let-btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.let-btn-group > .let-btn:not(:first-child),
.let-btn-group > .let-btn-group:not(:first-child) > .let-btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.let-dropdown-toggle-split {
  padding-right: 0.5625rem;
  padding-left: 0.5625rem;
}

.let-dropdown-toggle-split::after,
.let-dropup .let-dropdown-toggle-split::after,
.let-dropright .let-dropdown-toggle-split::after {
  margin-left: 0;
}

.let-dropleft .let-dropdown-toggle-split::before {
  margin-right: 0;
}

.let-btn-sm + .let-dropdown-toggle-split, .let-btn-group-sm > .let-btn + .let-dropdown-toggle-split {
  padding-right: 0.375rem;
  padding-left: 0.375rem;
}

.let-btn-lg + .let-dropdown-toggle-split, .let-btn-group-lg > .let-btn + .let-dropdown-toggle-split {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

.let-btn-group-vertical {
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-pack: center;
  justify-content: center;
}

.let-btn-group-vertical > .let-btn,
.let-btn-group-vertical > .let-btn-group {
  width: 100%;
}

.let-btn-group-vertical > .let-btn:not(:first-child),
.let-btn-group-vertical > .let-btn-group:not(:first-child) {
  margin-top: -1px;
}

.let-btn-group-vertical > .let-btn:not(:last-child):not(.let-dropdown-toggle),
.let-btn-group-vertical > .let-btn-group:not(:last-child) > .let-btn {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.let-btn-group-vertical > .let-btn:not(:first-child),
.let-btn-group-vertical > .let-btn-group:not(:first-child) > .let-btn {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.let-btn-group-toggle > .let-btn,
.let-btn-group-toggle > .let-btn-group > .let-btn {
  margin-bottom: 0;
}

.let-btn-group-toggle > .let-btn input[type="radio"],
.let-btn-group-toggle > .let-btn input[type="checkbox"],
.let-btn-group-toggle > .let-btn-group > .let-btn input[type="radio"],
.let-btn-group-toggle > .let-btn-group > .let-btn input[type="checkbox"] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}

.let-input-group {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: stretch;
  align-items: stretch;
  width: 100%;
}

.let-input-group > .let-form-control,
.let-input-group > .let-form-control-plaintext,
.let-input-group > .let-custom-select,
.let-input-group > .let-custom-file {
  position: relative;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  width: 1%;
  margin-bottom: 0;
}

.let-input-group > .let-form-control + .let-form-control,
.let-input-group > .let-form-control + .let-custom-select,
.let-input-group > .let-form-control + .let-custom-file,
.let-input-group > .let-form-control-plaintext + .let-form-control,
.let-input-group > .let-form-control-plaintext + .let-custom-select,
.let-input-group > .let-form-control-plaintext + .let-custom-file,
.let-input-group > .let-custom-select + .let-form-control,
.let-input-group > .let-custom-select + .let-custom-select,
.let-input-group > .let-custom-select + .let-custom-file,
.let-input-group > .let-custom-file + .let-form-control,
.let-input-group > .let-custom-file + .let-custom-select,
.let-input-group > .let-custom-file + .let-custom-file {
  margin-left: -1px;
}

.let-input-group > .let-form-control:focus,
.let-input-group > .let-custom-select:focus,
.let-input-group > .let-custom-file .let-custom-file-input:focus ~ .let-custom-file-label {
  z-index: 3;
}

.let-input-group > .let-custom-file .let-custom-file-input:focus {
  z-index: 4;
}

.let-input-group > .let-form-control:not(:last-child),
.let-input-group > .let-custom-select:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.let-input-group > .let-form-control:not(:first-child),
.let-input-group > .let-custom-select:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.let-input-group > .let-custom-file {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
}

.let-input-group > .let-custom-file:not(:last-child) .let-custom-file-label,
.let-input-group > .let-custom-file:not(:last-child) .let-custom-file-label::after {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.let-input-group > .let-custom-file:not(:first-child) .let-custom-file-label {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.let-input-group-prepend,
.let-input-group-append {
  display: -ms-flexbox;
  display: flex;
}

.let-input-group-prepend .let-btn,
.let-input-group-append .let-btn {
  position: relative;
  z-index: 2;
}

.let-input-group-prepend .let-btn:focus,
.let-input-group-append .let-btn:focus {
  z-index: 3;
}

.let-input-group-prepend .let-btn + .let-btn,
.let-input-group-prepend .let-btn + .let-input-group-text,
.let-input-group-prepend .let-input-group-text + .let-input-group-text,
.let-input-group-prepend .let-input-group-text + .let-btn,
.let-input-group-append .let-btn + .let-btn,
.let-input-group-append .let-btn + .let-input-group-text,
.let-input-group-append .let-input-group-text + .let-input-group-text,
.let-input-group-append .let-input-group-text + .let-btn {
  margin-left: -1px;
}

.let-input-group-prepend {
  margin-right: -1px;
}

.let-input-group-append {
  margin-left: -1px;
}

.let-input-group-text {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  padding: 0.375rem 0.75rem;
  margin-bottom: 0;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  text-align: center;
  white-space: nowrap;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
}

.let-input-group-text input[type="radio"],
.let-input-group-text input[type="checkbox"] {
  margin-top: 0;
}

.let-input-group-lg > .let-form-control:not(textarea),
.let-input-group-lg > .let-custom-select {
  height: calc(1.5em + 1rem + 2px);
}

.let-input-group-lg > .let-form-control,
.let-input-group-lg > .let-custom-select,
.let-input-group-lg > .let-input-group-prepend > .let-input-group-text,
.let-input-group-lg > .let-input-group-append > .let-input-group-text,
.let-input-group-lg > .let-input-group-prepend > .let-btn,
.let-input-group-lg > .let-input-group-append > .let-btn {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  line-height: 1.5;
  border-radius: 0.3rem;
}

.let-input-group-sm > .let-form-control:not(textarea),
.let-input-group-sm > .let-custom-select {
  height: calc(1.5em + 0.5rem + 2px);
}

.let-input-group-sm > .let-form-control,
.let-input-group-sm > .let-custom-select,
.let-input-group-sm > .let-input-group-prepend > .let-input-group-text,
.let-input-group-sm > .let-input-group-append > .let-input-group-text,
.let-input-group-sm > .let-input-group-prepend > .let-btn,
.let-input-group-sm > .let-input-group-append > .let-btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.let-input-group-lg > .let-custom-select,
.let-input-group-sm > .let-custom-select {
  padding-right: 1.75rem;
}

.let-input-group > .let-input-group-prepend > .let-btn,
.let-input-group > .let-input-group-prepend > .let-input-group-text,
.let-input-group > .let-input-group-append:not(:last-child) > .let-btn,
.let-input-group > .let-input-group-append:not(:last-child) > .let-input-group-text,
.let-input-group > .let-input-group-append:last-child > .let-btn:not(:last-child):not(.let-dropdown-toggle),
.let-input-group > .let-input-group-append:last-child > .let-input-group-text:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.let-input-group > .let-input-group-append > .let-btn,
.let-input-group > .let-input-group-append > .let-input-group-text,
.let-input-group > .let-input-group-prepend:not(:first-child) > .let-btn,
.let-input-group > .let-input-group-prepend:not(:first-child) > .let-input-group-text,
.let-input-group > .let-input-group-prepend:first-child > .let-btn:not(:first-child),
.let-input-group > .let-input-group-prepend:first-child > .let-input-group-text:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.let-custom-control {
  position: relative;
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5rem;
}

.let-custom-control-inline {
  display: -ms-inline-flexbox;
  display: inline-flex;
  margin-right: 1rem;
}

.let-custom-control-input {
  position: absolute;
  z-index: -1;
  opacity: 0;
}

.let-custom-control-input:checked ~ .let-custom-control-label::before {
  color: #fff;
  border-color: #007bff;
  background-color: #007bff;
}

.let-custom-control-input:focus ~ .let-custom-control-label::before {
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.let-custom-control-input:focus:not(:checked) ~ .let-custom-control-label::before {
  border-color: #80bdff;
}

.let-custom-control-input:not(:disabled):active ~ .let-custom-control-label::before {
  color: #fff;
  background-color: #b3d7ff;
  border-color: #b3d7ff;
}

.let-custom-control-input:disabled ~ .let-custom-control-label {
  color: #6c757d;
}

.let-custom-control-input:disabled ~ .let-custom-control-label::before {
  background-color: #e9ecef;
}

.let-custom-control-label {
  position: relative;
  margin-bottom: 0;
  vertical-align: top;
}

.let-custom-control-label::before {
  position: absolute;
  top: 0.25rem;
  left: -1.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  pointer-events: none;
  content: "";
  background-color: #fff;
  border: #adb5bd solid 1px;
}

.let-custom-control-label::after {
  position: absolute;
  top: 0.25rem;
  left: -1.5rem;
  display: block;
  width: 1rem;
  height: 1rem;
  content: "";
  background: no-repeat 50% / 50% 50%;
}

.let-custom-checkbox .let-custom-control-label::before {
  border-radius: 0.25rem;
}

.let-custom-checkbox .let-custom-control-input:checked ~ .let-custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.let-w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3e%3c/svg%3e");
}

.let-custom-checkbox .let-custom-control-input:indeterminate ~ .let-custom-control-label::before {
  border-color: #007bff;
  background-color: #007bff;
}

.let-custom-checkbox .let-custom-control-input:indeterminate ~ .let-custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.let-w3.org/2000/svg' viewBox='0 0 4 4'%3e%3cpath stroke='%23fff' d='M0 2h4'/%3e%3c/svg%3e");
}

.let-custom-checkbox .let-custom-control-input:disabled:checked ~ .let-custom-control-label::before {
  background-color: rgba(0, 123, 255, 0.5);
}

.let-custom-checkbox .let-custom-control-input:disabled:indeterminate ~ .let-custom-control-label::before {
  background-color: rgba(0, 123, 255, 0.5);
}

.let-custom-radio .let-custom-control-label::before {
  border-radius: 50%;
}

.let-custom-radio .let-custom-control-input:checked ~ .let-custom-control-label::after {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.let-w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}

.let-custom-radio .let-custom-control-input:disabled:checked ~ .let-custom-control-label::before {
  background-color: rgba(0, 123, 255, 0.5);
}

.let-custom-switch {
  padding-left: 2.25rem;
}

.let-custom-switch .let-custom-control-label::before {
  left: -2.25rem;
  width: 1.75rem;
  pointer-events: all;
  border-radius: 0.5rem;
}

.let-custom-switch .let-custom-control-label::after {
  top: calc(0.25rem + 2px);
  left: calc(-2.25rem + 2px);
  width: calc(1rem - 4px);
  height: calc(1rem - 4px);
  background-color: #adb5bd;
  border-radius: 0.5rem;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-transform 0.15s ease-in-out;
  transition: transform 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: transform 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, -webkit-transform 0.15s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
  .let-custom-switch .let-custom-control-label::after {
    transition: none;
  }
}

.let-custom-switch .let-custom-control-input:checked ~ .let-custom-control-label::after {
  background-color: #fff;
  -webkit-transform: translateX(0.75rem);
  transform: translateX(0.75rem);
}

.let-custom-switch .let-custom-control-input:disabled:checked ~ .let-custom-control-label::before {
  background-color: rgba(0, 123, 255, 0.5);
}

.let-custom-select {
  display: inline-block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 1.75rem 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  vertical-align: middle;
  background: url("data:image/svg+xml,%3csvg xmlns='http://www.let-w3.org/2000/svg' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.let-custom-select:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.let-custom-select:focus::-ms-value {
  color: #495057;
  background-color: #fff;
}

.let-custom-select[multiple], .let-custom-select[size]:not([size="1"]) {
  height: auto;
  padding-right: 0.75rem;
  background-image: none;
}

.let-custom-select:disabled {
  color: #6c757d;
  background-color: #e9ecef;
}

.let-custom-select::-ms-expand {
  display: none;
}

.let-custom-select-sm {
  height: calc(1.5em + 0.5rem + 2px);
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  font-size: 0.875rem;
}

.let-custom-select-lg {
  height: calc(1.5em + 1rem + 2px);
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  font-size: 1.25rem;
}

.let-custom-file {
  position: relative;
  display: inline-block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  margin-bottom: 0;
}

.let-custom-file-input {
  position: relative;
  z-index: 2;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  margin: 0;
  opacity: 0;
}

.let-custom-file-input:focus ~ .let-custom-file-label {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.let-custom-file-input:disabled ~ .let-custom-file-label {
  background-color: #e9ecef;
}

.let-custom-file-input:lang(en) ~ .let-custom-file-label::after {
  content: "Browse";
}

.let-custom-file-input ~ .let-custom-file-label[data-browse]::after {
  content: attr(data-browse);
}

.let-custom-file-label {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
}

.let-custom-file-label::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
  display: block;
  height: calc(1.5em + 0.75rem);
  padding: 0.375rem 0.75rem;
  line-height: 1.5;
  color: #495057;
  content: "Browse";
  background-color: #e9ecef;
  border-left: inherit;
  border-radius: 0 0.25rem 0.25rem 0;
}

.let-custom-range {
  width: 100%;
  height: calc(1rem + 0.4rem);
  padding: 0;
  background-color: transparent;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.let-custom-range:focus {
  outline: none;
}

.let-custom-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.let-custom-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.let-custom-range:focus::-ms-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.let-custom-range::-moz-focus-outer {
  border: 0;
}

.let-custom-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  background-color: #007bff;
  border: 0;
  border-radius: 1rem;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
  appearance: none;
}

@media (prefers-reduced-motion: reduce) {
  .let-custom-range::-webkit-slider-thumb {
    transition: none;
  }
}

.let-custom-range::-webkit-slider-thumb:active {
  background-color: #b3d7ff;
}

.let-custom-range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
}

.let-custom-range::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  background-color: #007bff;
  border: 0;
  border-radius: 1rem;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -moz-appearance: none;
  appearance: none;
}

@media (prefers-reduced-motion: reduce) {
  .let-custom-range::-moz-range-thumb {
    transition: none;
  }
}

.let-custom-range::-moz-range-thumb:active {
  background-color: #b3d7ff;
}

.let-custom-range::-moz-range-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
}

.let-custom-range::-ms-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: 0;
  margin-right: 0.2rem;
  margin-left: 0.2rem;
  background-color: #007bff;
  border: 0;
  border-radius: 1rem;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  appearance: none;
}

@media (prefers-reduced-motion: reduce) {
  .let-custom-range::-ms-thumb {
    transition: none;
  }
}

.let-custom-range::-ms-thumb:active {
  background-color: #b3d7ff;
}

.let-custom-range::-ms-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: transparent;
  border-color: transparent;
  border-width: 0.5rem;
}

.let-custom-range::-ms-fill-lower {
  background-color: #dee2e6;
  border-radius: 1rem;
}

.let-custom-range::-ms-fill-upper {
  margin-right: 15px;
  background-color: #dee2e6;
  border-radius: 1rem;
}

.let-custom-range:disabled::-webkit-slider-thumb {
  background-color: #adb5bd;
}

.let-custom-range:disabled::-webkit-slider-runnable-track {
  cursor: default;
}

.let-custom-range:disabled::-moz-range-thumb {
  background-color: #adb5bd;
}

.let-custom-range:disabled::-moz-range-track {
  cursor: default;
}

.let-custom-range:disabled::-ms-thumb {
  background-color: #adb5bd;
}

.let-custom-control-label::before,
.let-custom-file-label,
.let-custom-select {
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
  .let-custom-control-label::before,
  .let-custom-file-label,
  .let-custom-select {
    transition: none;
  }
}

.let-nav{
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.let-navlink {
  display: block;
  padding: 0.5rem 1rem;
}

.let-navlink:hover, .let-navlink:focus {
  text-decoration: none;
}

.let-navlink.let-disabled {
  color: #6c757d;
  pointer-events: none;
  cursor: default;
}

.let-navtabs {
  border-bottom: 1px solid #dee2e6;
}

.let-navtabs .let-navitem {
  margin-bottom: -1px;
}

.let-navtabs .let-navlink {
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.let-navtabs .let-navlink:hover, .let-navtabs .let-navlink:focus {
  border-color: #e9ecef #e9ecef #dee2e6;
}

.let-navtabs .let-navlink.let-disabled {
  color: #6c757d;
  background-color: transparent;
  border-color: transparent;
}

.let-navtabs .let-navlink.let-active,
.let-navtabs .let-navitem.let-show .let-navlink {
  color: #495057;
  background-color: #fff;
  border-color: #dee2e6 #dee2e6 #fff;
}

.let-navtabs .let-dropdown-menu {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.let-navpills .let-navlink {
  border-radius: 0.25rem;
}

.let-navpills .let-navlink.let-active,
.let-navpills .let-show > .let-navlink {
  color: #fff;
  background-color: #007bff;
}

.let-navfill .let-navitem {
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  text-align: center;
}

.let-navjustified .let-navitem {
  -ms-flex-preferred-size: 0;
  flex-basis: 0;
  -ms-flex-positive: 1;
  flex-grow: 1;
  text-align: center;
}

.let-tab-content > .let-tab-pane {
  display: none;
}

.let-tab-content > .let-active {
  display: block;
}

.let-navbar {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 0.5rem 1rem;
}

.let-navbar > .let-container,
.let-navbar > .let-container-fluid {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.let-navbar-brand {
  display: inline-block;
  padding-top: 0.3125rem;
  padding-bottom: 0.3125rem;
  margin-right: 1rem;
  font-size: 1.25rem;
  line-height: inherit;
  white-space: nowrap;
}

.let-navbar-brand:hover, .let-navbar-brand:focus {
  text-decoration: none;
}

.let-navbar-nav {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.let-navbar-nav .let-navlink {
  padding-right: 0;
  padding-left: 0;
}

.let-navbar-nav .let-dropdown-menu {
  position: static;
  float: none;
}

.let-navbar-text {
  display: inline-block;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.let-navbar-collapse {
  -ms-flex-preferred-size: 100%;
  flex-basis: 100%;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -ms-flex-align: center;
  align-items: center;
}

.let-navbar-toggler {
  padding: 0.25rem 0.75rem;
  font-size: 1.25rem;
  line-height: 1;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.let-navbar-toggler:hover, .let-navbar-toggler:focus {
  text-decoration: none;
}

.let-navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  content: "";
  background: no-repeat center center;
  background-size: 100% 100%;
}

@media (max-width: 575.98px) {
  .let-navbar-expand-sm > .let-container,
  .let-navbar-expand-sm > .let-container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 576px) {
  .let-navbar-expand-sm {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .let-navbar-expand-sm .let-navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .let-navbar-expand-sm .let-navbar-nav .let-dropdown-menu {
    position: absolute;
  }
  .let-navbar-expand-sm .let-navbar-nav .let-navlink {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .let-navbar-expand-sm > .let-container,
  .let-navbar-expand-sm > .let-container-fluid {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }
  .let-navbar-expand-sm .let-navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .let-navbar-expand-sm .let-navbar-toggler {
    display: none;
  }
}

@media (max-width: 767.98px) {
  .let-navbar-expand-md > .let-container,
  .let-navbar-expand-md > .let-container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 768px) {
  .let-navbar-expand-md {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .let-navbar-expand-md .let-navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .let-navbar-expand-md .let-navbar-nav .let-dropdown-menu {
    position: absolute;
  }
  .let-navbar-expand-md .let-navbar-nav .let-navlink {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .let-navbar-expand-md > .let-container,
  .let-navbar-expand-md > .let-container-fluid {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }
  .let-navbar-expand-md .let-navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .let-navbar-expand-md .let-navbar-toggler {
    display: none;
  }
}

@media (max-width: 991.98px) {
  .let-navbar-expand-lg > .let-container,
  .let-navbar-expand-lg > .let-container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 992px) {
  .let-navbar-expand-lg {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .let-navbar-expand-lg .let-navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .let-navbar-expand-lg .let-navbar-nav .let-dropdown-menu {
    position: absolute;
  }
  .let-navbar-expand-lg .let-navbar-nav .let-navlink {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .let-navbar-expand-lg > .let-container,
  .let-navbar-expand-lg > .let-container-fluid {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }
  .let-navbar-expand-lg .let-navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .let-navbar-expand-lg .let-navbar-toggler {
    display: none;
  }
}

@media (max-width: 1199.98px) {
  .let-navbar-expand-xl > .let-container,
  .let-navbar-expand-xl > .let-container-fluid {
    padding-right: 0;
    padding-left: 0;
  }
}

@media (min-width: 1200px) {
  .let-navbar-expand-xl {
    -ms-flex-flow: row nowrap;
    flex-flow: row nowrap;
    -ms-flex-pack: start;
    justify-content: flex-start;
  }
  .let-navbar-expand-xl .let-navbar-nav {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .let-navbar-expand-xl .let-navbar-nav .let-dropdown-menu {
    position: absolute;
  }
  .let-navbar-expand-xl .let-navbar-nav .let-navlink {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .let-navbar-expand-xl > .let-container,
  .let-navbar-expand-xl > .let-container-fluid {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
  }
  .let-navbar-expand-xl .let-navbar-collapse {
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-preferred-size: auto;
    flex-basis: auto;
  }
  .let-navbar-expand-xl .let-navbar-toggler {
    display: none;
  }
}

.let-navbar-expand {
  -ms-flex-flow: row nowrap;
  flex-flow: row nowrap;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.let-navbar-expand > .let-container,
.let-navbar-expand > .let-container-fluid {
  padding-right: 0;
  padding-left: 0;
}

.let-navbar-expand .let-navbar-nav {
  -ms-flex-direction: row;
  flex-direction: row;
}

.let-navbar-expand .let-navbar-nav .let-dropdown-menu {
  position: absolute;
}

.let-navbar-expand .let-navbar-nav .let-navlink {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}

.let-navbar-expand > .let-container,
.let-navbar-expand > .let-container-fluid {
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
}

.let-navbar-expand .let-navbar-collapse {
  display: -ms-flexbox !important;
  display: flex !important;
  -ms-flex-preferred-size: auto;
  flex-basis: auto;
}

.let-navbar-expand .let-navbar-toggler {
  display: none;
}

.let-navbar-light .let-navbar-brand {
  color: rgba(0, 0, 0, 0.9);
}

.let-navbar-light .let-navbar-brand:hover, .let-navbar-light .let-navbar-brand:focus {
  color: rgba(0, 0, 0, 0.9);
}

.let-navbar-light .let-navbar-nav .let-navlink {
  color: rgba(0, 0, 0, 0.5);
}

.let-navbar-light .let-navbar-nav .let-navlink:hover, .let-navbar-light .let-navbar-nav .let-navlink:focus {
  color: rgba(0, 0, 0, 0.7);
}

.let-navbar-light .let-navbar-nav .let-navlink.let-disabled {
  color: rgba(0, 0, 0, 0.3);
}

.let-navbar-light .let-navbar-nav .let-show > .let-navlink,
.let-navbar-light .let-navbar-nav .let-active > .let-navlink,
.let-navbar-light .let-navbar-nav .let-navlink.let-show,
.let-navbar-light .let-navbar-nav .let-navlink.let-active {
  color: rgba(0, 0, 0, 0.9);
}

.let-navbar-light .let-navbar-toggler {
  color: rgba(0, 0, 0, 0.5);
  border-color: rgba(0, 0, 0, 0.1);
}

.let-navbar-light .let-navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.let-w3.org/2000/svg'%3e%3cpath stroke='rgba(0, 0, 0, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.let-navbar-light .let-navbar-text {
  color: rgba(0, 0, 0, 0.5);
}

.let-navbar-light .let-navbar-text a {
  color: rgba(0, 0, 0, 0.9);
}

.let-navbar-light .let-navbar-text a:hover, .let-navbar-light .let-navbar-text a:focus {
  color: rgba(0, 0, 0, 0.9);
}

.let-navbar-dark .let-navbar-brand {
  color: #fff;
}

.let-navbar-dark .let-navbar-brand:hover, .let-navbar-dark .let-navbar-brand:focus {
  color: #fff;
}

.let-navbar-dark .let-navbar-nav .let-navlink {
  color: rgba(255, 255, 255, 0.5);
}

.let-navbar-dark .let-navbar-nav .let-navlink:hover, .let-navbar-dark .let-navbar-nav .let-navlink:focus {
  color: rgba(255, 255, 255, 0.75);
}

.let-navbar-dark .let-navbar-nav .let-navlink.let-disabled {
  color: rgba(255, 255, 255, 0.25);
}

.let-navbar-dark .let-navbar-nav .let-show > .let-navlink,
.let-navbar-dark .let-navbar-nav .let-active > .let-navlink,
.let-navbar-dark .let-navbar-nav .let-navlink.let-show,
.let-navbar-dark .let-navbar-nav .let-navlink.let-active {
  color: #fff;
}

.let-navbar-dark .let-navbar-toggler {
  color: rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.1);
}

.let-navbar-dark .let-navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 30 30' xmlns='http://www.let-w3.org/2000/svg'%3e%3cpath stroke='rgba(255, 255, 255, 0.5)' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.let-navbar-dark .let-navbar-text {
  color: rgba(255, 255, 255, 0.5);
}

.let-navbar-dark .let-navbar-text a {
  color: #fff;
}

.let-navbar-dark .let-navbar-text a:hover, .let-navbar-dark .let-navbar-text a:focus {
  color: #fff;
}

.let-card {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-radius: 0.25rem;
}

.let-card > hr {
  margin-right: 0;
  margin-left: 0;
}

.let-card > .let-list-group:first-child .let-list-group-item:first-child {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.let-card > .let-list-group:last-child .let-list-group-item:last-child {
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.let-card-body {
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 1.25rem;
}

.let-card-title {
  margin-bottom: 0.75rem;
}

.let-card-subtitle {
  margin-top: -0.375rem;
  margin-bottom: 0;
}

.let-card-text:last-child {
  margin-bottom: 0;
}

.let-card-link:hover {
  text-decoration: none;
}

.let-card-link + .let-card-link {
  margin-left: 1.25rem;
}

.let-card-header {
  padding: 0.75rem 1.25rem;
  margin-bottom: 0;
  background-color: rgba(0, 0, 0, 0.03);
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.let-card-header:first-child {
  border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
}

.let-card-header + .let-list-group .let-list-group-item:first-child {
  border-top: 0;
}

.let-card-footer {
  padding: 0.75rem 1.25rem;
  background-color: rgba(0, 0, 0, 0.03);
  border-top: 1px solid rgba(0, 0, 0, 0.125);
}

.let-card-footer:last-child {
  border-radius: 0 0 calc(0.25rem - 1px) calc(0.25rem - 1px);
}

.let-card-header-tabs {
  margin-right: -0.625rem;
  margin-bottom: -0.75rem;
  margin-left: -0.625rem;
  border-bottom: 0;
}

.let-card-header-pills {
  margin-right: -0.625rem;
  margin-left: -0.625rem;
}

.let-card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 1.25rem;
}

.let-card-img {
  width: 100%;
  border-radius: calc(0.25rem - 1px);
}

.let-card-img-top {
  width: 100%;
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}

.let-card-img-bottom {
  width: 100%;
  border-bottom-right-radius: calc(0.25rem - 1px);
  border-bottom-left-radius: calc(0.25rem - 1px);
}

.let-card-deck {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
}

.let-card-deck .let-card {
  margin-bottom: 15px;
}

@media (min-width: 576px) {
  .let-card-deck {
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
    margin-right: -15px;
    margin-left: -15px;
  }
  .let-card-deck .let-card {
    display: -ms-flexbox;
    display: flex;
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
    -ms-flex-direction: column;
    flex-direction: column;
    margin-right: 15px;
    margin-bottom: 0;
    margin-left: 15px;
  }
}

.let-card-group {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
}

.let-card-group > .let-card {
  margin-bottom: 15px;
}

@media (min-width: 576px) {
  .let-card-group {
    -ms-flex-flow: row wrap;
    flex-flow: row wrap;
  }
  .let-card-group > .let-card {
    -ms-flex: 1 0 0%;
    flex: 1 0 0%;
    margin-bottom: 0;
  }
  .let-card-group > .let-card + .let-card {
    margin-left: 0;
    border-left: 0;
  }
  .let-card-group > .let-card:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .let-card-group > .let-card:not(:last-child) .let-card-img-top,
  .let-card-group > .let-card:not(:last-child) .let-card-header {
    border-top-right-radius: 0;
  }
  .let-card-group > .let-card:not(:last-child) .let-card-img-bottom,
  .let-card-group > .let-card:not(:last-child) .let-card-footer {
    border-bottom-right-radius: 0;
  }
  .let-card-group > .let-card:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .let-card-group > .let-card:not(:first-child) .let-card-img-top,
  .let-card-group > .let-card:not(:first-child) .let-card-header {
    border-top-left-radius: 0;
  }
  .let-card-group > .let-card:not(:first-child) .let-card-img-bottom,
  .let-card-group > .let-card:not(:first-child) .let-card-footer {
    border-bottom-left-radius: 0;
  }
}

.let-card-columns .let-card {
  margin-bottom: 0.75rem;
}

@media (min-width: 576px) {
  .let-card-columns {
    -webkit-column-count: 3;
    -moz-column-count: 3;
    column-count: 3;
    -webkit-column-gap: 1.25rem;
    -moz-column-gap: 1.25rem;
    column-gap: 1.25rem;
    orphans: 1;
    widows: 1;
  }
  .let-card-columns .let-card {
    display: inline-block;
    width: 100%;
  }
}

.let-accordion > .let-card {
  overflow: hidden;
}

.let-accordion > .let-card:not(:first-of-type) .let-card-header:first-child {
  border-radius: 0;
}

.let-accordion > .let-card:not(:first-of-type):not(:last-of-type) {
  border-bottom: 0;
  border-radius: 0;
}

.let-accordion > .let-card:first-of-type {
  border-bottom: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}

.let-accordion > .let-card:last-of-type {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.let-accordion > .let-card .let-card-header {
  margin-bottom: -1px;
}

.let-breadcrumb {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  list-style: none;
  background-color: #e9ecef;
  border-radius: 0.25rem;
}

.let-breadcrumb-item + .let-breadcrumb-item {
  padding-left: 0.5rem;
}

.let-breadcrumb-item + .let-breadcrumb-item::before {
  display: inline-block;
  padding-right: 0.5rem;
  color: #6c757d;
  content: "/";
}

.let-breadcrumb-item + .let-breadcrumb-item:hover::before {
  text-decoration: underline;
}

.let-breadcrumb-item + .let-breadcrumb-item:hover::before {
  text-decoration: none;
}

.let-breadcrumb-item.let-active {
  color: #6c757d;
}

.let-pagination {
  display: -ms-flexbox;
  display: flex;
  padding-left: 0;
  list-style: none;
  border-radius: 0.25rem;
}

.let-page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: #007bff;
  background-color: #fff;
  border: 1px solid #dee2e6;
}

.let-page-link:hover {
  z-index: 2;
  color: #0056b3;
  text-decoration: none;
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.let-page-link:focus {
  z-index: 2;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.let-page-item:first-child .let-page-link {
  margin-left: 0;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.let-page-item:last-child .let-page-link {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.let-page-item.let-active .let-page-link {
  z-index: 1;
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.let-page-item.let-disabled .let-page-link {
  color: #6c757d;
  pointer-events: none;
  cursor: auto;
  background-color: #fff;
  border-color: #dee2e6;
}

.let-pagination-lg .let-page-link {
  padding: 0.75rem 1.5rem;
  font-size: 1.25rem;
  line-height: 1.5;
}

.let-pagination-lg .let-page-item:first-child .let-page-link {
  border-top-left-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
}

.let-pagination-lg .let-page-item:last-child .let-page-link {
  border-top-right-radius: 0.3rem;
  border-bottom-right-radius: 0.3rem;
}

.let-pagination-sm .let-page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
}

.let-pagination-sm .let-page-item:first-child .let-page-link {
  border-top-left-radius: 0.2rem;
  border-bottom-left-radius: 0.2rem;
}

.let-pagination-sm .let-page-item:last-child .let-page-link {
  border-top-right-radius: 0.2rem;
  border-bottom-right-radius: 0.2rem;
}

.let-badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
  .let-badge {
    transition: none;
  }
}

a.let-badge:hover, a.let-badge:focus {
  text-decoration: none;
}

.let-badge:empty {
  display: none;
}

.let-btn .let-badge {
  position: relative;
  top: -1px;
}

.let-badge-pill {
  padding-right: 0.6em;
  padding-left: 0.6em;
  border-radius: 10rem;
}

.let-badge-primary {
  color: #fff;
  background-color: #007bff;
}

a.let-badge-primary:hover, a.let-badge-primary:focus {
  color: #fff;
  background-color: #0062cc;
}

a.let-badge-primary:focus, a.let-badge-primary.let-focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.5);
}

.let-badge-secondary {
  color: #fff;
  background-color: #6c757d;
}

a.let-badge-secondary:hover, a.let-badge-secondary:focus {
  color: #fff;
  background-color: #545b62;
}

a.let-badge-secondary:focus, a.let-badge-secondary.let-focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5);
}

.let-badge-success {
  color: #fff;
  background-color: #28a745;
}

a.let-badge-success:hover, a.let-badge-success:focus {
  color: #fff;
  background-color: #1e7e34;
}

a.let-badge-success:focus, a.let-badge-success.let-focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5);
}

.let-badge-info {
  color: #fff;
  background-color: #17a2b8;
}

a.let-badge-info:hover, a.let-badge-info:focus {
  color: #fff;
  background-color: #117a8b;
}

a.let-badge-info:focus, a.let-badge-info.let-focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

.let-badge-warning {
  color: #212529;
  background-color: #ffc107;
}

a.let-badge-warning:hover, a.let-badge-warning:focus {
  color: #212529;
  background-color: #d39e00;
}

a.let-badge-warning:focus, a.let-badge-warning.let-focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.5);
}

.let-badge-danger {
  color: #fff;
  background-color: #dc3545;
}

a.let-badge-danger:hover, a.let-badge-danger:focus {
  color: #fff;
  background-color: #bd2130;
}

a.let-badge-danger:focus, a.let-badge-danger.let-focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5);
}

.let-badge-light {
  color: #212529;
  background-color: #f8f9fa;
}

a.let-badge-light:hover, a.let-badge-light:focus {
  color: #212529;
  background-color: #dae0e5;
}

a.let-badge-light:focus, a.let-badge-light.let-focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(248, 249, 250, 0.5);
}

.let-badge-dark {
  color: #fff;
  background-color: #343a40;
}

a.let-badge-dark:hover, a.let-badge-dark:focus {
  color: #fff;
  background-color: #1d2124;
}

a.let-badge-dark:focus, a.let-badge-dark.let-focus {
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(52, 58, 64, 0.5);
}

.let-jumbotron {
  padding: 2rem 1rem;
  margin-bottom: 2rem;
  background-color: #e9ecef;
  border-radius: 0.3rem;
}

@media (min-width: 576px) {
  .let-jumbotron {
    padding: 4rem 2rem;
  }
}

.let-jumbotron-fluid {
  padding-right: 0;
  padding-left: 0;
  border-radius: 0;
}

.let-alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.let-alert-heading {
  color: inherit;
}

.let-alert-link {
  font-weight: 700;
}

.let-alert-dismissible {
  padding-right: 4rem;
}

.let-alert-dismissible .let-close {
  position: absolute;
  top: 0;
  right: 0;
  padding: 0.75rem 1.25rem;
  color: inherit;
}

.let-alert-primary {
  color: #004085;
  background-color: #cce5ff;
  border-color: #b8daff;
}

.let-alert-primary hr {
  border-top-color: #9fcdff;
}

.let-alert-primary .let-alert-link {
  color: #002752;
}

.let-alert-secondary {
  color: #383d41;
  background-color: #e2e3e5;
  border-color: #d6d8db;
}

.let-alert-secondary hr {
  border-top-color: #c8cbcf;
}

.let-alert-secondary .let-alert-link {
  color: #202326;
}

.let-alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.let-alert-success hr {
  border-top-color: #b1dfbb;
}

.let-alert-success .let-alert-link {
  color: #0b2e13;
}

.let-alert-info {
  color: #0c5460;
  background-color: #d1ecf1;
  border-color: #bee5eb;
}

.let-alert-info hr {
  border-top-color: #abdde5;
}

.let-alert-info .let-alert-link {
  color: #062c33;
}

.let-alert-warning {
  color: #856404;
  background-color: #fff3cd;
  border-color: #ffeeba;
}

.let-alert-warning hr {
  border-top-color: #ffe8a1;
}

.let-alert-warning .let-alert-link {
  color: #533f03;
}

.let-alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.let-alert-danger hr {
  border-top-color: #f1b0b7;
}

.let-alert-danger .let-alert-link {
  color: #491217;
}

.let-alert-light {
  color: #818182;
  background-color: #fefefe;
  border-color: #fdfdfe;
}

.let-alert-light hr {
  border-top-color: #ececf6;
}

.let-alert-light .let-alert-link {
  color: #686868;
}

.let-alert-dark {
  color: #1b1e21;
  background-color: #d6d8d9;
  border-color: #c6c8ca;
}

.let-alert-dark hr {
  border-top-color: #b9bbbe;
}

.let-alert-dark .let-alert-link {
  color: #040505;
}

@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}

@keyframes progress-bar-stripes {
  from {
    background-position: 1rem 0;
  }
  to {
    background-position: 0 0;
  }
}

.let-progress {
  display: -ms-flexbox;
  display: flex;
  height: 1rem;
  overflow: hidden;
  font-size: 0.75rem;
  background-color: #e9ecef;
  border-radius: 0.25rem;
}

.let-progress-bar {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-pack: center;
  justify-content: center;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: #007bff;
  transition: width 0.6s ease;
}

@media (prefers-reduced-motion: reduce) {
  .let-progress-bar {
    transition: none;
  }
}

.let-progress-bar-striped {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem;
}

.let-progress-bar-animated {
  -webkit-animation: progress-bar-stripes 1s linear infinite;
  animation: progress-bar-stripes 1s linear infinite;
}

@media (prefers-reduced-motion: reduce) {
  .let-progress-bar-animated {
    -webkit-animation: none;
    animation: none;
  }
}

.let-media {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: start;
  align-items: flex-start;
}

.let-media-body {
  -ms-flex: 1;
  flex: 1;
}

.let-list-group {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
}

.let-list-group-item-action {
  width: 100%;
  color: #495057;
  text-align: inherit;
}

.let-list-group-item-action:hover, .let-list-group-item-action:focus {
  z-index: 1;
  color: #495057;
  text-decoration: none;
  background-color: #f8f9fa;
}

.let-list-group-item-action:active {
  color: #212529;
  background-color: #e9ecef;
}

.let-list-group-item {
  position: relative;
  display: block;
  padding: 0.75rem 1.25rem;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.let-list-group-item:first-child {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}

.let-list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.let-list-group-item.let-disabled, .let-list-group-item:disabled {
  color: #6c757d;
  pointer-events: none;
  background-color: #fff;
}

.let-list-group-item.let-active {
  z-index: 2;
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.let-list-group-horizontal {
  -ms-flex-direction: row;
  flex-direction: row;
}

.let-list-group-horizontal .let-list-group-item {
  margin-right: -1px;
  margin-bottom: 0;
}

.let-list-group-horizontal .let-list-group-item:first-child {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
  border-top-right-radius: 0;
}

.let-list-group-horizontal .let-list-group-item:last-child {
  margin-right: 0;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0;
}

@media (min-width: 576px) {
  .let-list-group-horizontal-sm {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .let-list-group-horizontal-sm .let-list-group-item {
    margin-right: -1px;
    margin-bottom: 0;
  }
  .let-list-group-horizontal-sm .let-list-group-item:first-child {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .let-list-group-horizontal-sm .let-list-group-item:last-child {
    margin-right: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
}

@media (min-width: 768px) {
  .let-list-group-horizontal-md {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .let-list-group-horizontal-md .let-list-group-item {
    margin-right: -1px;
    margin-bottom: 0;
  }
  .let-list-group-horizontal-md .let-list-group-item:first-child {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .let-list-group-horizontal-md .let-list-group-item:last-child {
    margin-right: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
}

@media (min-width: 992px) {
  .let-list-group-horizontal-lg {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .let-list-group-horizontal-lg .let-list-group-item {
    margin-right: -1px;
    margin-bottom: 0;
  }
  .let-list-group-horizontal-lg .let-list-group-item:first-child {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .let-list-group-horizontal-lg .let-list-group-item:last-child {
    margin-right: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
}

@media (min-width: 1200px) {
  .let-list-group-horizontal-xl {
    -ms-flex-direction: row;
    flex-direction: row;
  }
  .let-list-group-horizontal-xl .let-list-group-item {
    margin-right: -1px;
    margin-bottom: 0;
  }
  .let-list-group-horizontal-xl .let-list-group-item:first-child {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
    border-top-right-radius: 0;
  }
  .let-list-group-horizontal-xl .let-list-group-item:last-child {
    margin-right: 0;
    border-top-right-radius: 0.25rem;
    border-bottom-right-radius: 0.25rem;
    border-bottom-left-radius: 0;
  }
}

.let-list-group-flush .let-list-group-item {
  border-right: 0;
  border-left: 0;
  border-radius: 0;
}

.let-list-group-flush .let-list-group-item:last-child {
  margin-bottom: -1px;
}

.let-list-group-flush:first-child .let-list-group-item:first-child {
  border-top: 0;
}

.let-list-group-flush:last-child .let-list-group-item:last-child {
  margin-bottom: 0;
  border-bottom: 0;
}

.let-list-group-item-primary {
  color: #004085;
  background-color: #b8daff;
}

.let-list-group-item-primary.let-list-group-item-action:hover, .let-list-group-item-primary.let-list-group-item-action:focus {
  color: #004085;
  background-color: #9fcdff;
}

.let-list-group-item-primary.let-list-group-item-action.let-active {
  color: #fff;
  background-color: #004085;
  border-color: #004085;
}

.let-list-group-item-secondary {
  color: #383d41;
  background-color: #d6d8db;
}

.let-list-group-item-secondary.let-list-group-item-action:hover, .let-list-group-item-secondary.let-list-group-item-action:focus {
  color: #383d41;
  background-color: #c8cbcf;
}

.let-list-group-item-secondary.let-list-group-item-action.let-active {
  color: #fff;
  background-color: #383d41;
  border-color: #383d41;
}

.let-list-group-item-success {
  color: #155724;
  background-color: #c3e6cb;
}

.let-list-group-item-success.let-list-group-item-action:hover, .let-list-group-item-success.let-list-group-item-action:focus {
  color: #155724;
  background-color: #b1dfbb;
}

.let-list-group-item-success.let-list-group-item-action.let-active {
  color: #fff;
  background-color: #155724;
  border-color: #155724;
}

.let-list-group-item-info {
  color: #0c5460;
  background-color: #bee5eb;
}

.let-list-group-item-info.let-list-group-item-action:hover, .let-list-group-item-info.let-list-group-item-action:focus {
  color: #0c5460;
  background-color: #abdde5;
}

.let-list-group-item-info.let-list-group-item-action.let-active {
  color: #fff;
  background-color: #0c5460;
  border-color: #0c5460;
}

.let-list-group-item-warning {
  color: #856404;
  background-color: #ffeeba;
}

.let-list-group-item-warning.let-list-group-item-action:hover, .let-list-group-item-warning.let-list-group-item-action:focus {
  color: #856404;
  background-color: #ffe8a1;
}

.let-list-group-item-warning.let-list-group-item-action.let-active {
  color: #fff;
  background-color: #856404;
  border-color: #856404;
}

.let-list-group-item-danger {
  color: #721c24;
  background-color: #f5c6cb;
}

.let-list-group-item-danger.let-list-group-item-action:hover, .let-list-group-item-danger.let-list-group-item-action:focus {
  color: #721c24;
  background-color: #f1b0b7;
}

.let-list-group-item-danger.let-list-group-item-action.let-active {
  color: #fff;
  background-color: #721c24;
  border-color: #721c24;
}

.let-list-group-item-light {
  color: #818182;
  background-color: #fdfdfe;
}

.let-list-group-item-light.let-list-group-item-action:hover, .let-list-group-item-light.let-list-group-item-action:focus {
  color: #818182;
  background-color: #ececf6;
}

.let-list-group-item-light.let-list-group-item-action.let-active {
  color: #fff;
  background-color: #818182;
  border-color: #818182;
}

.let-list-group-item-dark {
  color: #1b1e21;
  background-color: #c6c8ca;
}

.let-list-group-item-dark.let-list-group-item-action:hover, .let-list-group-item-dark.let-list-group-item-action:focus {
  color: #1b1e21;
  background-color: #b9bbbe;
}

.let-list-group-item-dark.let-list-group-item-action.let-active {
  color: #fff;
  background-color: #1b1e21;
  border-color: #1b1e21;
}

.let-close {
  float: right;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: .let-5;
}

.let-close:hover {
  color: #000;
  text-decoration: none;
}

.let-close:not(:disabled):not(.let-disabled):hover, .let-close:not(:disabled):not(.let-disabled):focus {
  opacity: .let-75;
}

button.let-close {
  padding: 0;
  background-color: transparent;
  border: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

a.let-close.let-disabled {
  pointer-events: none;
}

.let-toast {
  max-width: 350px;
  overflow: hidden;
  font-size: 0.875rem;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  opacity: 0;
  border-radius: 0.25rem;
}

.let-toast:not(:last-child) {
  margin-bottom: 0.75rem;
}

.let-toast.let-showing {
  opacity: 1;
}

.let-toast.let-show {
  display: block;
  opacity: 1;
}

.let-toast.let-hide {
  display: none;
}

.let-toast-header {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  padding: 0.25rem 0.75rem;
  color: #6c757d;
  background-color: rgba(255, 255, 255, 0.85);
  background-clip: padding-box;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.let-toast-body {
  padding: 0.75rem;
}

.let-modal-open {
  overflow: hidden;
}

.let-modal-open .let-modal {
  overflow-x: hidden;
  overflow-y: auto;
}

.let-modal {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  display: none;
  width: 100%;
  height: 100%;
  overflow: hidden;
  outline: 0;
}

.let-modal-dialog {
  position: relative;
  width: auto;
  margin: 0.5rem;
  pointer-events: none;
}

.let-modal.let-fade .let-modal-dialog {
  transition: -webkit-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
  transition: transform 0.3s ease-out, -webkit-transform 0.3s ease-out;
  -webkit-transform: translate(0, -50px);
  transform: translate(0, -50px);
}

@media (prefers-reduced-motion: reduce) {
  .let-modal.let-fade .let-modal-dialog {
    transition: none;
  }
}

.let-modal.let-show .let-modal-dialog {
  -webkit-transform: none;
  transform: none;
}

.let-modal-dialog-scrollable {
  display: -ms-flexbox;
  display: flex;
  max-height: calc(100% - 1rem);
}

.let-modal-dialog-scrollable .let-modal-content {
  max-height: calc(100vh - 1rem);
  overflow: hidden;
}

.let-modal-dialog-scrollable .let-modal-header,
.let-modal-dialog-scrollable .let-modal-footer {
  -ms-flex-negative: 0;
  flex-shrink: 0;
}

.let-modal-dialog-scrollable .let-modal-body {
  overflow-y: auto;
}

.let-modal-dialog-centered {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  min-height: calc(100% - 1rem);
}

.let-modal-dialog-centered::before {
  display: block;
  height: calc(100vh - 1rem);
  content: "";
}

.let-modal-dialog-centered.let-modal-dialog-scrollable {
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-pack: center;
  justify-content: center;
  height: 100%;
}

.let-modal-dialog-centered.let-modal-dialog-scrollable .let-modal-content {
  max-height: none;
}

.let-modal-dialog-centered.let-modal-dialog-scrollable::before {
  content: none;
}

.let-modal-content {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  width: 100%;
  pointer-events: auto;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
  outline: 0;
}

.let-modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000;
}

.let-modal-backdrop.let-fade {
  opacity: 0;
}

.let-modal-backdrop.let-show {
  opacity: 0.5;
}

.let-modal-header {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: start;
  align-items: flex-start;
  -ms-flex-pack: justify;
  justify-content: space-between;
  padding: 1rem 1rem;
  border-bottom: 1px solid #dee2e6;
  border-top-left-radius: 0.3rem;
  border-top-right-radius: 0.3rem;
}

.let-modal-header .let-close {
  padding: 1rem 1rem;
  margin: -1rem -1rem -1rem auto;
}

.let-modal-title {
  margin-bottom: 0;
  line-height: 1.5;
}

.let-modal-body {
  position: relative;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 1rem;
}

.let-modal-footer {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: end;
  justify-content: flex-end;
  padding: 1rem;
  border-top: 1px solid #dee2e6;
  border-bottom-right-radius: 0.3rem;
  border-bottom-left-radius: 0.3rem;
}

.let-modal-footer > :not(:first-child) {
  margin-left: .let-25rem;
}

.let-modal-footer > :not(:last-child) {
  margin-right: .let-25rem;
}

.let-modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}

@media (min-width: 576px) {
  .let-modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
  }
  .let-modal-dialog-scrollable {
    max-height: calc(100% - 3.5rem);
  }
  .let-modal-dialog-scrollable .let-modal-content {
    max-height: calc(100vh - 3.5rem);
  }
  .let-modal-dialog-centered {
    min-height: calc(100% - 3.5rem);
  }
  .let-modal-dialog-centered::before {
    height: calc(100vh - 3.5rem);
  }
  .let-modal-sm {
    max-width: 300px;
  }
}

@media (min-width: 992px) {
  .let-modal-lg,
  .let-modal-xl {
    max-width: 800px;
  }
}

@media (min-width: 1200px) {
  .let-modal-xl {
    max-width: 1140px;
  }
}

.let-tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  opacity: 0;
}

.let-tooltip.let-show {
  opacity: 0.9;
}

.let-tooltip .let-arrow {
  position: absolute;
  display: block;
  width: 0.8rem;
  height: 0.4rem;
}

.let-tooltip .let-arrow::before {
  position: absolute;
  content: "";
  border-color: transparent;
  border-style: solid;
}

.let-bs-tooltip-top, .let-bs-tooltip-auto[x-placement^="top"] {
  padding: 0.4rem 0;
}

.let-bs-tooltip-top .let-arrow, .let-bs-tooltip-auto[x-placement^="top"] .let-arrow {
  bottom: 0;
}

.let-bs-tooltip-top .let-arrow::before, .let-bs-tooltip-auto[x-placement^="top"] .let-arrow::before {
  top: 0;
  border-width: 0.4rem 0.4rem 0;
  border-top-color: #000;
}

.let-bs-tooltip-right, .let-bs-tooltip-auto[x-placement^="right"] {
  padding: 0 0.4rem;
}

.let-bs-tooltip-right .let-arrow, .let-bs-tooltip-auto[x-placement^="right"] .let-arrow {
  left: 0;
  width: 0.4rem;
  height: 0.8rem;
}

.let-bs-tooltip-right .let-arrow::before, .let-bs-tooltip-auto[x-placement^="right"] .let-arrow::before {
  right: 0;
  border-width: 0.4rem 0.4rem 0.4rem 0;
  border-right-color: #000;
}

.let-bs-tooltip-bottom, .let-bs-tooltip-auto[x-placement^="bottom"] {
  padding: 0.4rem 0;
}

.let-bs-tooltip-bottom .let-arrow, .let-bs-tooltip-auto[x-placement^="bottom"] .let-arrow {
  top: 0;
}

.let-bs-tooltip-bottom .let-arrow::before, .let-bs-tooltip-auto[x-placement^="bottom"] .let-arrow::before {
  bottom: 0;
  border-width: 0 0.4rem 0.4rem;
  border-bottom-color: #000;
}

.let-bs-tooltip-left, .let-bs-tooltip-auto[x-placement^="left"] {
  padding: 0 0.4rem;
}

.let-bs-tooltip-left .let-arrow, .let-bs-tooltip-auto[x-placement^="left"] .let-arrow {
  right: 0;
  width: 0.4rem;
  height: 0.8rem;
}

.let-bs-tooltip-left .let-arrow::before, .let-bs-tooltip-auto[x-placement^="left"] .let-arrow::before {
  left: 0;
  border-width: 0.4rem 0 0.4rem 0.4rem;
  border-left-color: #000;
}

.let-tooltip-inner {
  max-width: 200px;
  padding: 0.25rem 0.5rem;
  color: #fff;
  text-align: center;
  background-color: #000;
  border-radius: 0.25rem;
}

.let-popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1060;
  display: block;
  max-width: 276px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  white-space: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 0.3rem;
}

.let-popover .let-arrow {
  position: absolute;
  display: block;
  width: 1rem;
  height: 0.5rem;
  margin: 0 0.3rem;
}

.let-popover .let-arrow::before, .let-popover .let-arrow::after {
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
}

.let-bs-popover-top, .let-bs-popover-auto[x-placement^="top"] {
  margin-bottom: 0.5rem;
}

.let-bs-popover-top > .let-arrow, .let-bs-popover-auto[x-placement^="top"] > .let-arrow {
  bottom: calc((0.5rem + 1px) * -1);
}

.let-bs-popover-top > .let-arrow::before, .let-bs-popover-auto[x-placement^="top"] > .let-arrow::before {
  bottom: 0;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: rgba(0, 0, 0, 0.25);
}

.let-bs-popover-top > .let-arrow::after, .let-bs-popover-auto[x-placement^="top"] > .let-arrow::after {
  bottom: 1px;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: #fff;
}

.let-bs-popover-right, .let-bs-popover-auto[x-placement^="right"] {
  margin-left: 0.5rem;
}

.let-bs-popover-right > .let-arrow, .let-bs-popover-auto[x-placement^="right"] > .let-arrow {
  left: calc((0.5rem + 1px) * -1);
  width: 0.5rem;
  height: 1rem;
  margin: 0.3rem 0;
}

.let-bs-popover-right > .let-arrow::before, .let-bs-popover-auto[x-placement^="right"] > .let-arrow::before {
  left: 0;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: rgba(0, 0, 0, 0.25);
}

.let-bs-popover-right > .let-arrow::after, .let-bs-popover-auto[x-placement^="right"] > .let-arrow::after {
  left: 1px;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: #fff;
}

.let-bs-popover-bottom, .let-bs-popover-auto[x-placement^="bottom"] {
  margin-top: 0.5rem;
}

.let-bs-popover-bottom > .let-arrow, .let-bs-popover-auto[x-placement^="bottom"] > .let-arrow {
  top: calc((0.5rem + 1px) * -1);
}

.let-bs-popover-bottom > .let-arrow::before, .let-bs-popover-auto[x-placement^="bottom"] > .let-arrow::before {
  top: 0;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: rgba(0, 0, 0, 0.25);
}

.let-bs-popover-bottom > .let-arrow::after, .let-bs-popover-auto[x-placement^="bottom"] > .let-arrow::after {
  top: 1px;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: #fff;
}

.let-bs-popover-bottom .let-popover-header::before, .let-bs-popover-auto[x-placement^="bottom"] .let-popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 1rem;
  margin-left: -0.5rem;
  content: "";
  border-bottom: 1px solid #f7f7f7;
}

.let-bs-popover-left, .let-bs-popover-auto[x-placement^="left"] {
  margin-right: 0.5rem;
}

.let-bs-popover-left > .let-arrow, .let-bs-popover-auto[x-placement^="left"] > .let-arrow {
  right: calc((0.5rem + 1px) * -1);
  width: 0.5rem;
  height: 1rem;
  margin: 0.3rem 0;
}

.let-bs-popover-left > .let-arrow::before, .let-bs-popover-auto[x-placement^="left"] > .let-arrow::before {
  right: 0;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: rgba(0, 0, 0, 0.25);
}

.let-bs-popover-left > .let-arrow::after, .let-bs-popover-auto[x-placement^="left"] > .let-arrow::after {
  right: 1px;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: #fff;
}

.let-popover-header {
  padding: 0.5rem 0.75rem;
  margin-bottom: 0;
  font-size: 1rem;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}

.let-popover-header:empty {
  display: none;
}

.let-popover-body {
  padding: 0.5rem 0.75rem;
  color: #212529;
}

.let-carousel {
  position: relative;
}

.let-carousel.let-pointer-event {
  -ms-touch-action: pan-y;
  touch-action: pan-y;
}

.let-carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.let-carousel-inner::after {
  display: block;
  clear: both;
  content: "";
}

.let-carousel-item {
  position: relative;
  display: none;
  float: left;
  width: 100%;
  margin-right: -100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  transition: -webkit-transform 0.6s ease-in-out;
  transition: transform 0.6s ease-in-out;
  transition: transform 0.6s ease-in-out, -webkit-transform 0.6s ease-in-out;
}

@media (prefers-reduced-motion: reduce) {
  .let-carousel-item {
    transition: none;
  }
}

.let-carousel-item.let-active,
.let-carousel-item-next,
.let-carousel-item-prev {
  display: block;
}

.let-carousel-item-next:not(.let-carousel-item-left),
.let-active.let-carousel-item-right {
  -webkit-transform: translateX(100%);
  transform: translateX(100%);
}

.let-carousel-item-prev:not(.let-carousel-item-right),
.let-active.let-carousel-item-left {
  -webkit-transform: translateX(-100%);
  transform: translateX(-100%);
}

.let-carousel-fade .let-carousel-item {
  opacity: 0;
  transition-property: opacity;
  -webkit-transform: none;
  transform: none;
}

.let-carousel-fade .let-carousel-item.let-active,
.let-carousel-fade .let-carousel-item-next.let-carousel-item-left,
.let-carousel-fade .let-carousel-item-prev.let-carousel-item-right {
  z-index: 1;
  opacity: 1;
}

.let-carousel-fade .let-active.let-carousel-item-left,
.let-carousel-fade .let-active.let-carousel-item-right {
  z-index: 0;
  opacity: 0;
  transition: 0s 0.6s opacity;
}

@media (prefers-reduced-motion: reduce) {
  .let-carousel-fade .let-active.let-carousel-item-left,
  .let-carousel-fade .let-active.let-carousel-item-right {
    transition: none;
  }
}

.let-carousel-control-prev,
.let-carousel-control-next {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 15%;
  color: #fff;
  text-align: center;
  opacity: 0.5;
  transition: opacity 0.15s ease;
}

@media (prefers-reduced-motion: reduce) {
  .let-carousel-control-prev,
  .let-carousel-control-next {
    transition: none;
  }
}

.let-carousel-control-prev:hover, .let-carousel-control-prev:focus,
.let-carousel-control-next:hover,
.let-carousel-control-next:focus {
  color: #fff;
  text-decoration: none;
  outline: 0;
  opacity: 0.9;
}

.let-carousel-control-prev {
  left: 0;
}

.let-carousel-control-next {
  right: 0;
}

.let-carousel-control-prev-icon,
.let-carousel-control-next-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: no-repeat 50% / 100% 100%;
}

.let-carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.let-w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3e%3cpath d='M5.25 0l-4 4 4 4 1.5-1.5-2.5-2.5 2.5-2.5-1.5-1.5z'/%3e%3c/svg%3e");
}

.let-carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.let-w3.org/2000/svg' fill='%23fff' viewBox='0 0 8 8'%3e%3cpath d='M2.75 0l-1.5 1.5 2.5 2.5-2.5 2.5 1.5 1.5 4-4-4-4z'/%3e%3c/svg%3e");
}

.let-carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 15;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
  justify-content: center;
  padding-left: 0;
  margin-right: 15%;
  margin-left: 15%;
  list-style: none;
}

.let-carousel-indicators li {
  box-sizing: content-box;
  -ms-flex: 0 1 auto;
  flex: 0 1 auto;
  width: 30px;
  height: 3px;
  margin-right: 3px;
  margin-left: 3px;
  text-indent: -999px;
  cursor: pointer;
  background-color: #fff;
  background-clip: padding-box;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  opacity: .let-5;
  transition: opacity 0.6s ease;
}

@media (prefers-reduced-motion: reduce) {
  .let-carousel-indicators li {
    transition: none;
  }
}

.let-carousel-indicators .let-active {
  opacity: 1;
}

.let-carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 20px;
  left: 15%;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: #fff;
  text-align: center;
}

@-webkit-keyframes spinner-border {
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes spinner-border {
  to {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.let-spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  -webkit-animation: spinner-border .let-75s linear infinite;
  animation: spinner-border .let-75s linear infinite;
}

.let-spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.2em;
}

@-webkit-keyframes spinner-grow {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  50% {
    opacity: 1;
  }
}

@keyframes spinner-grow {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  50% {
    opacity: 1;
  }
}

.let-spinner-grow {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: text-bottom;
  background-color: currentColor;
  border-radius: 50%;
  opacity: 0;
  -webkit-animation: spinner-grow .let-75s linear infinite;
  animation: spinner-grow .let-75s linear infinite;
}

.let-spinner-grow-sm {
  width: 1rem;
  height: 1rem;
}

.let-align-baseline {
  vertical-align: baseline !important;
}

.let-align-top {
  vertical-align: top !important;
}

.let-align-middle {
  vertical-align: middle !important;
}

.let-align-bottom {
  vertical-align: bottom !important;
}

.let-align-text-bottom {
  vertical-align: text-bottom !important;
}

.let-align-text-top {
  vertical-align: text-top !important;
}

.let-bg-primary {
  background-color: #007bff !important;
}

a.let-bg-primary:hover, a.let-bg-primary:focus,
button.let-bg-primary:hover,
button.let-bg-primary:focus {
  background-color: #0062cc !important;
}

.let-bg-secondary {
  background-color: #6c757d !important;
}

a.let-bg-secondary:hover, a.let-bg-secondary:focus,
button.let-bg-secondary:hover,
button.let-bg-secondary:focus {
  background-color: #545b62 !important;
}

.let-bg-success {
  background-color: #1ebdce !important;
}

a.let-bg-success:hover, a.let-bg-success:focus,
button.let-bg-success:hover,
button.let-bg-success:focus {
  background-color: #1ebdce !important;
}

.let-bg-info {
  background-color: #17a2b8 !important;
}

a.let-bg-info:hover, a.let-bg-info:focus,
button.let-bg-info:hover,
button.let-bg-info:focus {
  background-color: #117a8b !important;
}

.let-bg-warning {
  background-color: #ffc107 !important;
}

a.let-bg-warning:hover, a.let-bg-warning:focus,
button.let-bg-warning:hover,
button.let-bg-warning:focus {
  background-color: #d39e00 !important;
}

.let-bg-danger {
  background-color: #dc3545 !important;
}

a.let-bg-danger:hover, a.let-bg-danger:focus,
button.let-bg-danger:hover,
button.let-bg-danger:focus {
  background-color: #bd2130 !important;
}

.let-bg-light {
  background-color: #f8f9fa !important;
}

a.let-bg-light:hover, a.let-bg-light:focus,
button.let-bg-light:hover,
button.let-bg-light:focus {
  background-color: #dae0e5 !important;
}

.let-bg-dark {
  background-color: #343a40 !important;
}

a.let-bg-dark:hover, a.let-bg-dark:focus,
button.let-bg-dark:hover,
button.let-bg-dark:focus {
  background-color: #1d2124 !important;
}

.let-bg-white {
  background-color: #fff !important;
}

.let-bg-transparent {
  background-color: transparent !important;
}

.let-border {
  border: 1px solid #dee2e6 !important;
}

.let-border-top {
  border-top: 1px solid #dee2e6 !important;
}

.let-border-right {
  border-right: 1px solid #dee2e6 !important;
}

.let-border-bottom {
  border-bottom: 1px solid #dee2e6 !important;
}

.let-border-left {
  border-left: 1px solid #dee2e6 !important;
}

.let-border-0 {
  border: 0 !important;
}

.let-border-top-0 {
  border-top: 0 !important;
}

.let-border-right-0 {
  border-right: 0 !important;
}

.let-border-bottom-0 {
  border-bottom: 0 !important;
}

.let-border-left-0 {
  border-left: 0 !important;
}

.let-border-primary {
  border-color: #007bff !important;
}

.let-border-secondary {
  border-color: #6c757d !important;
}

.let-border-success {
  border-color: #1ebdce !important;
}

.let-border-info {
  border-color: #17a2b8 !important;
}

.let-border-warning {
  border-color: #ffc107 !important;
}

.let-border-danger {
  border-color: #dc3545 !important;
}

.let-border-light {
  border-color: #f8f9fa !important;
}

.let-border-dark {
  border-color: #343a40 !important;
}

.let-border-white {
  border-color: #fff !important;
}

.let-rounded-sm {
  border-radius: 0.2rem !important;
}

.let-rounded {
  border-radius: 0.25rem !important;
}

.let-rounded-top {
  border-top-left-radius: 0.25rem !important;
  border-top-right-radius: 0.25rem !important;
}

.let-rounded-right {
  border-top-right-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
}

.let-rounded-bottom {
  border-bottom-right-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}

.let-rounded-left {
  border-top-left-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}

.let-rounded-lg {
  border-radius: 0.3rem !important;
}

.let-rounded-circle {
  border-radius: 50% !important;
}

.let-rounded-pill {
  border-radius: 50rem !important;
}

.let-rounded-0 {
  border-radius: 0 !important;
}

.let-clearfix::after {
  display: block;
  clear: both;
  content: "";
}

.let-d-none {
  display: none !important;
}

.let-d-inline {
  display: inline !important;
}

.let-d-inline-block {
  display: inline-block !important;
}

.let-d-block {
  display: block !important;
}

.let-d-table {
  display: table !important;
}

.let-d-table-row {
  display: table-row !important;
}

.let-d-table-cell {
  display: table-cell !important;
}

.let-d-flex {
  display: -ms-flexbox !important;
  display: flex !important;
}

.let-d-inline-flex {
  display: -ms-inline-flexbox !important;
  display: inline-flex !important;
}

@media (min-width: 576px) {
  .let-d-sm-none {
    display: none !important;
  }
  .let-d-sm-inline {
    display: inline !important;
  }
  .let-d-sm-inline-block {
    display: inline-block !important;
  }
  .let-d-sm-block {
    display: block !important;
  }
  .let-d-sm-table {
    display: table !important;
  }
  .let-d-sm-table-row {
    display: table-row !important;
  }
  .let-d-sm-table-cell {
    display: table-cell !important;
  }
  .let-d-sm-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .let-d-sm-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}

@media (min-width: 768px) {
  .let-d-md-none {
    display: none !important;
  }
  .let-d-md-inline {
    display: inline !important;
  }
  .let-d-md-inline-block {
    display: inline-block !important;
  }
  .let-d-md-block {
    display: block !important;
  }
  .let-d-md-table {
    display: table !important;
  }
  .let-d-md-table-row {
    display: table-row !important;
  }
  .let-d-md-table-cell {
    display: table-cell !important;
  }
  .let-d-md-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .let-d-md-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}

@media (min-width: 992px) {
  .let-d-lg-none {
    display: none !important;
  }
  .let-d-lg-inline {
    display: inline !important;
  }
  .let-d-lg-inline-block {
    display: inline-block !important;
  }
  .let-d-lg-block {
    display: block !important;
  }
  .let-d-lg-table {
    display: table !important;
  }
  .let-d-lg-table-row {
    display: table-row !important;
  }
  .let-d-lg-table-cell {
    display: table-cell !important;
  }
  .let-d-lg-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .let-d-lg-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}

@media (min-width: 1200px) {
  .let-d-xl-none {
    display: none !important;
  }
  .let-d-xl-inline {
    display: inline !important;
  }
  .let-d-xl-inline-block {
    display: inline-block !important;
  }
  .let-d-xl-block {
    display: block !important;
  }
  .let-d-xl-table {
    display: table !important;
  }
  .let-d-xl-table-row {
    display: table-row !important;
  }
  .let-d-xl-table-cell {
    display: table-cell !important;
  }
  .let-d-xl-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .let-d-xl-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}

@media print {
  .let-d-print-none {
    display: none !important;
  }
  .let-d-print-inline {
    display: inline !important;
  }
  .let-d-print-inline-block {
    display: inline-block !important;
  }
  .let-d-print-block {
    display: block !important;
  }
  .let-d-print-table {
    display: table !important;
  }
  .let-d-print-table-row {
    display: table-row !important;
  }
  .let-d-print-table-cell {
    display: table-cell !important;
  }
  .let-d-print-flex {
    display: -ms-flexbox !important;
    display: flex !important;
  }
  .let-d-print-inline-flex {
    display: -ms-inline-flexbox !important;
    display: inline-flex !important;
  }
}

.let-embed-responsive {
  position: relative;
  display: block;
  width: 100%;
  padding: 0;
  overflow: hidden;
}

.let-embed-responsive::before {
  display: block;
  content: "";
}

.let-embed-responsive .let-embed-responsive-item,
.let-embed-responsive iframe,
.let-embed-responsive embed,
.let-embed-responsive object,
.let-embed-responsive video {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
}

.let-embed-responsive-21by9::before {
  padding-top: 42.857143%;
}

.let-embed-responsive-16by9::before {
  padding-top: 56.25%;
}

.let-embed-responsive-4by3::before {
  padding-top: 75%;
}

.let-embed-responsive-1by1::before {
  padding-top: 100%;
}

.let-flex-row {
  -ms-flex-direction: row !important;
  flex-direction: row !important;
}

.let-flex-column {
  -ms-flex-direction: column !important;
  flex-direction: column !important;
}

.let-flex-row-reverse {
  -ms-flex-direction: row-reverse !important;
  flex-direction: row-reverse !important;
}

.let-flex-column-reverse {
  -ms-flex-direction: column-reverse !important;
  flex-direction: column-reverse !important;
}

.let-flex-wrap {
  -ms-flex-wrap: wrap !important;
  flex-wrap: wrap !important;
}

.let-flex-nowrap {
  -ms-flex-wrap: nowrap !important;
  flex-wrap: nowrap !important;
}

.let-flex-wrap-reverse {
  -ms-flex-wrap: wrap-reverse !important;
  flex-wrap: wrap-reverse !important;
}

.let-flex-fill {
  -ms-flex: 1 1 auto !important;
  flex: 1 1 auto !important;
}

.let-flex-grow-0 {
  -ms-flex-positive: 0 !important;
  flex-grow: 0 !important;
}

.let-flex-grow-1 {
  -ms-flex-positive: 1 !important;
  flex-grow: 1 !important;
}

.let-flex-shrink-0 {
  -ms-flex-negative: 0 !important;
  flex-shrink: 0 !important;
}

.let-flex-shrink-1 {
  -ms-flex-negative: 1 !important;
  flex-shrink: 1 !important;
}

.let-justify-content-start {
  -ms-flex-pack: start !important;
  justify-content: flex-start !important;
}

.let-justify-content-end {
  -ms-flex-pack: end !important;
  justify-content: flex-end !important;
}

.let-justify-content-center {
  -ms-flex-pack: center !important;
  justify-content: center !important;
}

.let-justify-content-between {
  -ms-flex-pack: justify !important;
  justify-content: space-between !important;
}

.let-justify-content-around {
  -ms-flex-pack: distribute !important;
  justify-content: space-around !important;
}

.let-align-items-start {
  -ms-flex-align: start !important;
  align-items: flex-start !important;
}

.let-align-items-end {
  -ms-flex-align: end !important;
  align-items: flex-end !important;
}

.let-align-items-center {
  -ms-flex-align: center !important;
  align-items: center !important;
}

.let-align-items-baseline {
  -ms-flex-align: baseline !important;
  align-items: baseline !important;
}

.let-align-items-stretch {
  -ms-flex-align: stretch !important;
  align-items: stretch !important;
}

.let-align-content-start {
  -ms-flex-line-pack: start !important;
  align-content: flex-start !important;
}

.let-align-content-end {
  -ms-flex-line-pack: end !important;
  align-content: flex-end !important;
}

.let-align-content-center {
  -ms-flex-line-pack: center !important;
  align-content: center !important;
}

.let-align-content-between {
  -ms-flex-line-pack: justify !important;
  align-content: space-between !important;
}

.let-align-content-around {
  -ms-flex-line-pack: distribute !important;
  align-content: space-around !important;
}

.let-align-content-stretch {
  -ms-flex-line-pack: stretch !important;
  align-content: stretch !important;
}

.let-align-self-auto {
  -ms-flex-item-align: auto !important;
  align-self: auto !important;
}

.let-align-self-start {
  -ms-flex-item-align: start !important;
  align-self: flex-start !important;
}

.let-align-self-end {
  -ms-flex-item-align: end !important;
  align-self: flex-end !important;
}

.let-align-self-center {
  -ms-flex-item-align: center !important;
  align-self: center !important;
}

.let-align-self-baseline {
  -ms-flex-item-align: baseline !important;
  align-self: baseline !important;
}

.let-align-self-stretch {
  -ms-flex-item-align: stretch !important;
  align-self: stretch !important;
}

@media (min-width: 576px) {
  .let-flex-sm-row {
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }
  .let-flex-sm-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }
  .let-flex-sm-row-reverse {
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }
  .let-flex-sm-column-reverse {
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }
  .let-flex-sm-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }
  .let-flex-sm-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }
  .let-flex-sm-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }
  .let-flex-sm-fill {
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }
  .let-flex-sm-grow-0 {
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }
  .let-flex-sm-grow-1 {
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }
  .let-flex-sm-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }
  .let-flex-sm-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }
  .let-justify-content-sm-start {
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }
  .let-justify-content-sm-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }
  .let-justify-content-sm-center {
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }
  .let-justify-content-sm-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }
  .let-justify-content-sm-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }
  .let-align-items-sm-start {
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }
  .let-align-items-sm-end {
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }
  .let-align-items-sm-center {
    -ms-flex-align: center !important;
    align-items: center !important;
  }
  .let-align-items-sm-baseline {
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }
  .let-align-items-sm-stretch {
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }
  .let-align-content-sm-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }
  .let-align-content-sm-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }
  .let-align-content-sm-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }
  .let-align-content-sm-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }
  .let-align-content-sm-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }
  .let-align-content-sm-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }
  .let-align-self-sm-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }
  .let-align-self-sm-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }
  .let-align-self-sm-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }
  .let-align-self-sm-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }
  .let-align-self-sm-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }
  .let-align-self-sm-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }
}

@media (min-width: 768px) {
  .let-flex-md-row {
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }
  .let-flex-md-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }
  .let-flex-md-row-reverse {
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }
  .let-flex-md-column-reverse {
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }
  .let-flex-md-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }
  .let-flex-md-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }
  .let-flex-md-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }
  .let-flex-md-fill {
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }
  .let-flex-md-grow-0 {
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }
  .let-flex-md-grow-1 {
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }
  .let-flex-md-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }
  .let-flex-md-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }
  .let-justify-content-md-start {
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }
  .let-justify-content-md-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }
  .let-justify-content-md-center {
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }
  .let-justify-content-md-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }
  .let-justify-content-md-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }
  .let-align-items-md-start {
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }
  .let-align-items-md-end {
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }
  .let-align-items-md-center {
    -ms-flex-align: center !important;
    align-items: center !important;
  }
  .let-align-items-md-baseline {
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }
  .let-align-items-md-stretch {
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }
  .let-align-content-md-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }
  .let-align-content-md-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }
  .let-align-content-md-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }
  .let-align-content-md-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }
  .let-align-content-md-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }
  .let-align-content-md-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }
  .let-align-self-md-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }
  .let-align-self-md-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }
  .let-align-self-md-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }
  .let-align-self-md-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }
  .let-align-self-md-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }
  .let-align-self-md-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }
}

@media (min-width: 992px) {
  .let-flex-lg-row {
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }
  .let-flex-lg-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }
  .let-flex-lg-row-reverse {
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }
  .let-flex-lg-column-reverse {
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }
  .let-flex-lg-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }
  .let-flex-lg-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }
  .let-flex-lg-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }
  .let-flex-lg-fill {
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }
  .let-flex-lg-grow-0 {
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }
  .let-flex-lg-grow-1 {
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }
  .let-flex-lg-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }
  .let-flex-lg-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }
  .let-justify-content-lg-start {
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }
  .let-justify-content-lg-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }
  .let-justify-content-lg-center {
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }
  .let-justify-content-lg-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }
  .let-justify-content-lg-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }
  .let-align-items-lg-start {
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }
  .let-align-items-lg-end {
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }
  .let-align-items-lg-center {
    -ms-flex-align: center !important;
    align-items: center !important;
  }
  .let-align-items-lg-baseline {
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }
  .let-align-items-lg-stretch {
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }
  .let-align-content-lg-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }
  .let-align-content-lg-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }
  .let-align-content-lg-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }
  .let-align-content-lg-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }
  .let-align-content-lg-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }
  .let-align-content-lg-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }
  .let-align-self-lg-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }
  .let-align-self-lg-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }
  .let-align-self-lg-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }
  .let-align-self-lg-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }
  .let-align-self-lg-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }
  .let-align-self-lg-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }
}

@media (min-width: 1200px) {
  .let-flex-xl-row {
    -ms-flex-direction: row !important;
    flex-direction: row !important;
  }
  .let-flex-xl-column {
    -ms-flex-direction: column !important;
    flex-direction: column !important;
  }
  .let-flex-xl-row-reverse {
    -ms-flex-direction: row-reverse !important;
    flex-direction: row-reverse !important;
  }
  .let-flex-xl-column-reverse {
    -ms-flex-direction: column-reverse !important;
    flex-direction: column-reverse !important;
  }
  .let-flex-xl-wrap {
    -ms-flex-wrap: wrap !important;
    flex-wrap: wrap !important;
  }
  .let-flex-xl-nowrap {
    -ms-flex-wrap: nowrap !important;
    flex-wrap: nowrap !important;
  }
  .let-flex-xl-wrap-reverse {
    -ms-flex-wrap: wrap-reverse !important;
    flex-wrap: wrap-reverse !important;
  }
  .let-flex-xl-fill {
    -ms-flex: 1 1 auto !important;
    flex: 1 1 auto !important;
  }
  .let-flex-xl-grow-0 {
    -ms-flex-positive: 0 !important;
    flex-grow: 0 !important;
  }
  .let-flex-xl-grow-1 {
    -ms-flex-positive: 1 !important;
    flex-grow: 1 !important;
  }
  .let-flex-xl-shrink-0 {
    -ms-flex-negative: 0 !important;
    flex-shrink: 0 !important;
  }
  .let-flex-xl-shrink-1 {
    -ms-flex-negative: 1 !important;
    flex-shrink: 1 !important;
  }
  .let-justify-content-xl-start {
    -ms-flex-pack: start !important;
    justify-content: flex-start !important;
  }
  .let-justify-content-xl-end {
    -ms-flex-pack: end !important;
    justify-content: flex-end !important;
  }
  .let-justify-content-xl-center {
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }
  .let-justify-content-xl-between {
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
  }
  .let-justify-content-xl-around {
    -ms-flex-pack: distribute !important;
    justify-content: space-around !important;
  }
  .let-align-items-xl-start {
    -ms-flex-align: start !important;
    align-items: flex-start !important;
  }
  .let-align-items-xl-end {
    -ms-flex-align: end !important;
    align-items: flex-end !important;
  }
  .let-align-items-xl-center {
    -ms-flex-align: center !important;
    align-items: center !important;
  }
  .let-align-items-xl-baseline {
    -ms-flex-align: baseline !important;
    align-items: baseline !important;
  }
  .let-align-items-xl-stretch {
    -ms-flex-align: stretch !important;
    align-items: stretch !important;
  }
  .let-align-content-xl-start {
    -ms-flex-line-pack: start !important;
    align-content: flex-start !important;
  }
  .let-align-content-xl-end {
    -ms-flex-line-pack: end !important;
    align-content: flex-end !important;
  }
  .let-align-content-xl-center {
    -ms-flex-line-pack: center !important;
    align-content: center !important;
  }
  .let-align-content-xl-between {
    -ms-flex-line-pack: justify !important;
    align-content: space-between !important;
  }
  .let-align-content-xl-around {
    -ms-flex-line-pack: distribute !important;
    align-content: space-around !important;
  }
  .let-align-content-xl-stretch {
    -ms-flex-line-pack: stretch !important;
    align-content: stretch !important;
  }
  .let-align-self-xl-auto {
    -ms-flex-item-align: auto !important;
    align-self: auto !important;
  }
  .let-align-self-xl-start {
    -ms-flex-item-align: start !important;
    align-self: flex-start !important;
  }
  .let-align-self-xl-end {
    -ms-flex-item-align: end !important;
    align-self: flex-end !important;
  }
  .let-align-self-xl-center {
    -ms-flex-item-align: center !important;
    align-self: center !important;
  }
  .let-align-self-xl-baseline {
    -ms-flex-item-align: baseline !important;
    align-self: baseline !important;
  }
  .let-align-self-xl-stretch {
    -ms-flex-item-align: stretch !important;
    align-self: stretch !important;
  }
}

.let-float-left {
  float: left !important;
}

.let-float-right {
  float: right !important;
}

.let-float-none {
  float: none !important;
}

@media (min-width: 576px) {
  .let-float-sm-left {
    float: left !important;
  }
  .let-float-sm-right {
    float: right !important;
  }
  .let-float-sm-none {
    float: none !important;
  }
}

@media (min-width: 768px) {
  .let-float-md-left {
    float: left !important;
  }
  .let-float-md-right {
    float: right !important;
  }
  .let-float-md-none {
    float: none !important;
  }
}

@media (min-width: 992px) {
  .let-float-lg-left {
    float: left !important;
  }
  .let-float-lg-right {
    float: right !important;
  }
  .let-float-lg-none {
    float: none !important;
  }
}

@media (min-width: 1200px) {
  .let-float-xl-left {
    float: left !important;
  }
  .let-float-xl-right {
    float: right !important;
  }
  .let-float-xl-none {
    float: none !important;
  }
}

.let-overflow-auto {
  overflow: auto !important;
}

.let-overflow-hidden {
  overflow: hidden !important;
}

.let-position-static {
  position: static !important;
}

.let-position-relative {
  position: relative !important;
}

.let-position-absolute {
  position: absolute !important;
}

.let-position-fixed {
  position: fixed !important;
}

.let-position-sticky {
  position: -webkit-sticky !important;
  position: sticky !important;
}

.let-fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}

.let-fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}

@supports ((position: -webkit-sticky) or (position: sticky)) {
  .let-sticky-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}

.let-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.let-sr-only-focusable:active, .let-sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

.let-shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.let-shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.let-shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.let-shadow-none {
  box-shadow: none !important;
}

.let-w-25 {
  width: 25% !important;
}

.let-w-50 {
  width: 50% !important;
}

.let-w-75 {
  width: 75% !important;
}

.let-w-100 {
  width: 100% !important;
}

.let-w-auto {
  width: auto !important;
}

.let-h-25 {
  height: 25% !important;
}

.let-h-50 {
  height: 50% !important;
}

.let-h-75 {
  height: 75% !important;
}

.let-h-100 {
  height: 100% !important;
}

.let-h-auto {
  height: auto !important;
}

.let-mw-100 {
  max-width: 100% !important;
}

.let-mh-100 {
  max-height: 100% !important;
}

.let-min-vw-100 {
  min-width: 100vw !important;
}

.let-min-vh-100 {
  min-height: 100vh !important;
}

.let-vw-100 {
  width: 100vw !important;
}

.let-vh-100 {
  height: 100vh !important;
}

.let-stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  pointer-events: auto;
  content: "";
  background-color: rgba(0, 0, 0, 0);
}

.let-m-0 {
  margin: 0 !important;
}

.let-mt-0,
.let-my-0 {
  margin-top: 0 !important;
}

.let-mr-0,
.let-mx-0 {
  margin-right: 0 !important;
}

.let-mb-0,
.let-my-0 {
  margin-bottom: 0 !important;
}

.let-ml-0,
.let-mx-0 {
  margin-left: 0 !important;
}

.let-m-1 {
  margin: 0.25rem !important;
}

.let-mt-1,
.let-my-1 {
  margin-top: 0.25rem !important;
}

.let-mr-1,
.let-mx-1 {
  margin-right: 0.25rem !important;
}

.let-mb-1,
.let-my-1 {
  margin-bottom: 0.25rem !important;
}

.let-ml-1,
.let-mx-1 {
  margin-left: 0.25rem !important;
}

.let-m-2 {
  margin: 0.5rem !important;
}

.let-mt-2,
.let-my-2 {
  margin-top: 0.5rem !important;
}

.let-mr-2,
.let-mx-2 {
  margin-right: 0.5rem !important;
}

.let-mb-2,
.let-my-2 {
  margin-bottom: 0.5rem !important;
}

.let-ml-2,
.let-mx-2 {
  margin-left: 0.5rem !important;
}

.let-m-3 {
  margin: 1rem !important;
}

.let-mt-3,
.let-my-3 {
  margin-top: 1rem !important;
}

.let-mr-3,
.let-mx-3 {
  margin-right: 1rem !important;
}

.let-mb-3,
.let-my-3 {
  margin-bottom: 1rem !important;
}

.let-ml-3,
.let-mx-3 {
  margin-left: 1rem !important;
}

.let-m-4 {
  margin: 1.5rem !important;
}

.let-mt-4,
.let-my-4 {
  margin-top: 1.5rem !important;
}

.let-mr-4,
.let-mx-4 {
  margin-right: 1.5rem !important;
}

.let-mb-4,
.let-my-4 {
  margin-bottom: 1.5rem !important;
}

.let-ml-4,
.let-mx-4 {
  margin-left: 1.5rem !important;
}

.let-m-5 {
  margin: 3rem !important;
}

.let-mt-5,
.let-my-5 {
  margin-top: 3rem !important;
}

.let-mr-5,
.let-mx-5 {
  margin-right: 3rem !important;
}

.let-mb-5,
.let-my-5 {
  margin-bottom: 3rem !important;
}

.let-ml-5,
.let-mx-5 {
  margin-left: 3rem !important;
}

.let-p-0 {
  padding: 0 !important;
}

.let-pt-0,
.let-py-0 {
  padding-top: 0 !important;
}

.let-pr-0,
.let-px-0 {
  padding-right: 0 !important;
}

.let-pb-0,
.let-py-0 {
  padding-bottom: 0 !important;
}

.let-pl-0,
.let-px-0 {
  padding-left: 0 !important;
}

.let-p-1 {
  padding: 0.25rem !important;
}

.let-pt-1,
.let-py-1 {
  padding-top: 0.25rem !important;
}

.let-pr-1,
.let-px-1 {
  padding-right: 0.25rem !important;
}

.let-pb-1,
.let-py-1 {
  padding-bottom: 0.25rem !important;
}

.let-pl-1,
.let-px-1 {
  padding-left: 0.25rem !important;
}

.let-p-2 {
  padding: 0.5rem !important;
}

.let-pt-2,
.let-py-2 {
  padding-top: 0.5rem !important;
}

.let-pr-2,
.let-px-2 {
  padding-right: 0.5rem !important;
}

.let-pb-2,
.let-py-2 {
  padding-bottom: 0.5rem !important;
}

.let-pl-2,
.let-px-2 {
  padding-left: 0.5rem !important;
}

.let-p-3 {
  padding: 1rem !important;
}

.let-pt-3,
.let-py-3 {
  padding-top: 1rem !important;
}

.let-pr-3,
.let-px-3 {
  padding-right: 1rem !important;
}

.let-pb-3,
.let-py-3 {
  padding-bottom: 1rem !important;
}

.let-pl-3,
.let-px-3 {
  padding-left: 1rem !important;
}

.let-p-4 {
  padding: 1.5rem !important;
}

.let-pt-4,
.let-py-4 {
  padding-top: 1.5rem !important;
}

.let-pr-4,
.let-px-4 {
  padding-right: 1.5rem !important;
}

.let-pb-4,
.let-py-4 {
  padding-bottom: 1.5rem !important;
}

.let-pl-4,
.let-px-4 {
  padding-left: 1.5rem !important;
}

.let-p-5 {
  padding: 3rem !important;
}

.let-pt-5,
.let-py-5 {
  padding-top: 3rem !important;
}

.let-pr-5,
.let-px-5 {
  padding-right: 3rem !important;
}

.let-pb-5,
.let-py-5 {
  padding-bottom: 3rem !important;
}

.let-pl-5,
.let-px-5 {
  padding-left: 3rem !important;
}

.let-m-n1 {
  margin: -0.25rem !important;
}

.let-mt-n1,
.let-my-n1 {
  margin-top: -0.25rem !important;
}

.let-mr-n1,
.let-mx-n1 {
  margin-right: -0.25rem !important;
}

.let-mb-n1,
.let-my-n1 {
  margin-bottom: -0.25rem !important;
}

.let-ml-n1,
.let-mx-n1 {
  margin-left: -0.25rem !important;
}

.let-m-n2 {
  margin: -0.5rem !important;
}

.let-mt-n2,
.let-my-n2 {
  margin-top: -0.5rem !important;
}

.let-mr-n2,
.let-mx-n2 {
  margin-right: -0.5rem !important;
}

.let-mb-n2,
.let-my-n2 {
  margin-bottom: -0.5rem !important;
}

.let-ml-n2,
.let-mx-n2 {
  margin-left: -0.5rem !important;
}

.let-m-n3 {
  margin: -1rem !important;
}

.let-mt-n3,
.let-my-n3 {
  margin-top: -1rem !important;
}

.let-mr-n3,
.let-mx-n3 {
  margin-right: -1rem !important;
}

.let-mb-n3,
.let-my-n3 {
  margin-bottom: -1rem !important;
}

.let-ml-n3,
.let-mx-n3 {
  margin-left: -1rem !important;
}

.let-m-n4 {
  margin: -1.5rem !important;
}

.let-mt-n4,
.let-my-n4 {
  margin-top: -1.5rem !important;
}

.let-mr-n4,
.let-mx-n4 {
  margin-right: -1.5rem !important;
}

.let-mb-n4,
.let-my-n4 {
  margin-bottom: -1.5rem !important;
}

.let-ml-n4,
.let-mx-n4 {
  margin-left: -1.5rem !important;
}

.let-m-n5 {
  margin: -3rem !important;
}

.let-mt-n5,
.let-my-n5 {
  margin-top: -3rem !important;
}

.let-mr-n5,
.let-mx-n5 {
  margin-right: -3rem !important;
}

.let-mb-n5,
.let-my-n5 {
  margin-bottom: -3rem !important;
}

.let-ml-n5,
.let-mx-n5 {
  margin-left: -3rem !important;
}

.let-m-auto {
  margin: auto !important;
}

.let-mt-auto,
.let-my-auto {
  margin-top: auto !important;
}

.let-mr-auto,
.let-mx-auto {
  margin-right: auto !important;
}

.let-mb-auto,
.let-my-auto {
  margin-bottom: auto !important;
}

.let-ml-auto,
.let-mx-auto {
  margin-left: auto !important;
}

@media (min-width: 576px) {
  .let-m-sm-0 {
    margin: 0 !important;
  }
  .let-mt-sm-0,
  .let-my-sm-0 {
    margin-top: 0 !important;
  }
  .let-mr-sm-0,
  .let-mx-sm-0 {
    margin-right: 0 !important;
  }
  .let-mb-sm-0,
  .let-my-sm-0 {
    margin-bottom: 0 !important;
  }
  .let-ml-sm-0,
  .let-mx-sm-0 {
    margin-left: 0 !important;
  }
  .let-m-sm-1 {
    margin: 0.25rem !important;
  }
  .let-mt-sm-1,
  .let-my-sm-1 {
    margin-top: 0.25rem !important;
  }
  .let-mr-sm-1,
  .let-mx-sm-1 {
    margin-right: 0.25rem !important;
  }
  .let-mb-sm-1,
  .let-my-sm-1 {
    margin-bottom: 0.25rem !important;
  }
  .let-ml-sm-1,
  .let-mx-sm-1 {
    margin-left: 0.25rem !important;
  }
  .let-m-sm-2 {
    margin: 0.5rem !important;
  }
  .let-mt-sm-2,
  .let-my-sm-2 {
    margin-top: 0.5rem !important;
  }
  .let-mr-sm-2,
  .let-mx-sm-2 {
    margin-right: 0.5rem !important;
  }
  .let-mb-sm-2,
  .let-my-sm-2 {
    margin-bottom: 0.5rem !important;
  }
  .let-ml-sm-2,
  .let-mx-sm-2 {
    margin-left: 0.5rem !important;
  }
  .let-m-sm-3 {
    margin: 1rem !important;
  }
  .let-mt-sm-3,
  .let-my-sm-3 {
    margin-top: 1rem !important;
  }
  .let-mr-sm-3,
  .let-mx-sm-3 {
    margin-right: 1rem !important;
  }
  .let-mb-sm-3,
  .let-my-sm-3 {
    margin-bottom: 1rem !important;
  }
  .let-ml-sm-3,
  .let-mx-sm-3 {
    margin-left: 1rem !important;
  }
  .let-m-sm-4 {
    margin: 1.5rem !important;
  }
  .let-mt-sm-4,
  .let-my-sm-4 {
    margin-top: 1.5rem !important;
  }
  .let-mr-sm-4,
  .let-mx-sm-4 {
    margin-right: 1.5rem !important;
  }
  .let-mb-sm-4,
  .let-my-sm-4 {
    margin-bottom: 1.5rem !important;
  }
  .let-ml-sm-4,
  .let-mx-sm-4 {
    margin-left: 1.5rem !important;
  }
  .let-m-sm-5 {
    margin: 3rem !important;
  }
  .let-mt-sm-5,
  .let-my-sm-5 {
    margin-top: 3rem !important;
  }
  .let-mr-sm-5,
  .let-mx-sm-5 {
    margin-right: 3rem !important;
  }
  .let-mb-sm-5,
  .let-my-sm-5 {
    margin-bottom: 3rem !important;
  }
  .let-ml-sm-5,
  .let-mx-sm-5 {
    margin-left: 3rem !important;
  }
  .let-p-sm-0 {
    padding: 0 !important;
  }
  .let-pt-sm-0,
  .let-py-sm-0 {
    padding-top: 0 !important;
  }
  .let-pr-sm-0,
  .let-px-sm-0 {
    padding-right: 0 !important;
  }
  .let-pb-sm-0,
  .let-py-sm-0 {
    padding-bottom: 0 !important;
  }
  .let-pl-sm-0,
  .let-px-sm-0 {
    padding-left: 0 !important;
  }
  .let-p-sm-1 {
    padding: 0.25rem !important;
  }
  .let-pt-sm-1,
  .let-py-sm-1 {
    padding-top: 0.25rem !important;
  }
  .let-pr-sm-1,
  .let-px-sm-1 {
    padding-right: 0.25rem !important;
  }
  .let-pb-sm-1,
  .let-py-sm-1 {
    padding-bottom: 0.25rem !important;
  }
  .let-pl-sm-1,
  .let-px-sm-1 {
    padding-left: 0.25rem !important;
  }
  .let-p-sm-2 {
    padding: 0.5rem !important;
  }
  .let-pt-sm-2,
  .let-py-sm-2 {
    padding-top: 0.5rem !important;
  }
  .let-pr-sm-2,
  .let-px-sm-2 {
    padding-right: 0.5rem !important;
  }
  .let-pb-sm-2,
  .let-py-sm-2 {
    padding-bottom: 0.5rem !important;
  }
  .let-pl-sm-2,
  .let-px-sm-2 {
    padding-left: 0.5rem !important;
  }
  .let-p-sm-3 {
    padding: 1rem !important;
  }
  .let-pt-sm-3,
  .let-py-sm-3 {
    padding-top: 1rem !important;
  }
  .let-pr-sm-3,
  .let-px-sm-3 {
    padding-right: 1rem !important;
  }
  .let-pb-sm-3,
  .let-py-sm-3 {
    padding-bottom: 1rem !important;
  }
  .let-pl-sm-3,
  .let-px-sm-3 {
    padding-left: 1rem !important;
  }
  .let-p-sm-4 {
    padding: 1.5rem !important;
  }
  .let-pt-sm-4,
  .let-py-sm-4 {
    padding-top: 1.5rem !important;
  }
  .let-pr-sm-4,
  .let-px-sm-4 {
    padding-right: 1.5rem !important;
  }
  .let-pb-sm-4,
  .let-py-sm-4 {
    padding-bottom: 1.5rem !important;
  }
  .let-pl-sm-4,
  .let-px-sm-4 {
    padding-left: 1.5rem !important;
  }
  .let-p-sm-5 {
    padding: 3rem !important;
  }
  .let-pt-sm-5,
  .let-py-sm-5 {
    padding-top: 3rem !important;
  }
  .let-pr-sm-5,
  .let-px-sm-5 {
    padding-right: 3rem !important;
  }
  .let-pb-sm-5,
  .let-py-sm-5 {
    padding-bottom: 3rem !important;
  }
  .let-pl-sm-5,
  .let-px-sm-5 {
    padding-left: 3rem !important;
  }
  .let-m-sm-n1 {
    margin: -0.25rem !important;
  }
  .let-mt-sm-n1,
  .let-my-sm-n1 {
    margin-top: -0.25rem !important;
  }
  .let-mr-sm-n1,
  .let-mx-sm-n1 {
    margin-right: -0.25rem !important;
  }
  .let-mb-sm-n1,
  .let-my-sm-n1 {
    margin-bottom: -0.25rem !important;
  }
  .let-ml-sm-n1,
  .let-mx-sm-n1 {
    margin-left: -0.25rem !important;
  }
  .let-m-sm-n2 {
    margin: -0.5rem !important;
  }
  .let-mt-sm-n2,
  .let-my-sm-n2 {
    margin-top: -0.5rem !important;
  }
  .let-mr-sm-n2,
  .let-mx-sm-n2 {
    margin-right: -0.5rem !important;
  }
  .let-mb-sm-n2,
  .let-my-sm-n2 {
    margin-bottom: -0.5rem !important;
  }
  .let-ml-sm-n2,
  .let-mx-sm-n2 {
    margin-left: -0.5rem !important;
  }
  .let-m-sm-n3 {
    margin: -1rem !important;
  }
  .let-mt-sm-n3,
  .let-my-sm-n3 {
    margin-top: -1rem !important;
  }
  .let-mr-sm-n3,
  .let-mx-sm-n3 {
    margin-right: -1rem !important;
  }
  .let-mb-sm-n3,
  .let-my-sm-n3 {
    margin-bottom: -1rem !important;
  }
  .let-ml-sm-n3,
  .let-mx-sm-n3 {
    margin-left: -1rem !important;
  }
  .let-m-sm-n4 {
    margin: -1.5rem !important;
  }
  .let-mt-sm-n4,
  .let-my-sm-n4 {
    margin-top: -1.5rem !important;
  }
  .let-mr-sm-n4,
  .let-mx-sm-n4 {
    margin-right: -1.5rem !important;
  }
  .let-mb-sm-n4,
  .let-my-sm-n4 {
    margin-bottom: -1.5rem !important;
  }
  .let-ml-sm-n4,
  .let-mx-sm-n4 {
    margin-left: -1.5rem !important;
  }
  .let-m-sm-n5 {
    margin: -3rem !important;
  }
  .let-mt-sm-n5,
  .let-my-sm-n5 {
    margin-top: -3rem !important;
  }
  .let-mr-sm-n5,
  .let-mx-sm-n5 {
    margin-right: -3rem !important;
  }
  .let-mb-sm-n5,
  .let-my-sm-n5 {
    margin-bottom: -3rem !important;
  }
  .let-ml-sm-n5,
  .let-mx-sm-n5 {
    margin-left: -3rem !important;
  }
  .let-m-sm-auto {
    margin: auto !important;
  }
  .let-mt-sm-auto,
  .let-my-sm-auto {
    margin-top: auto !important;
  }
  .let-mr-sm-auto,
  .let-mx-sm-auto {
    margin-right: auto !important;
  }
  .let-mb-sm-auto,
  .let-my-sm-auto {
    margin-bottom: auto !important;
  }
  .let-ml-sm-auto,
  .let-mx-sm-auto {
    margin-left: auto !important;
  }
}

@media (min-width: 768px) {
  .let-m-md-0 {
    margin: 0 !important;
  }
  .let-mt-md-0,
  .let-my-md-0 {
    margin-top: 0 !important;
  }
  .let-mr-md-0,
  .let-mx-md-0 {
    margin-right: 0 !important;
  }
  .let-mb-md-0,
  .let-my-md-0 {
    margin-bottom: 0 !important;
  }
  .let-ml-md-0,
  .let-mx-md-0 {
    margin-left: 0 !important;
  }
  .let-m-md-1 {
    margin: 0.25rem !important;
  }
  .let-mt-md-1,
  .let-my-md-1 {
    margin-top: 0.25rem !important;
  }
  .let-mr-md-1,
  .let-mx-md-1 {
    margin-right: 0.25rem !important;
  }
  .let-mb-md-1,
  .let-my-md-1 {
    margin-bottom: 0.25rem !important;
  }
  .let-ml-md-1,
  .let-mx-md-1 {
    margin-left: 0.25rem !important;
  }
  .let-m-md-2 {
    margin: 0.5rem !important;
  }
  .let-mt-md-2,
  .let-my-md-2 {
    margin-top: 0.5rem !important;
  }
  .let-mr-md-2,
  .let-mx-md-2 {
    margin-right: 0.5rem !important;
  }
  .let-mb-md-2,
  .let-my-md-2 {
    margin-bottom: 0.5rem !important;
  }
  .let-ml-md-2,
  .let-mx-md-2 {
    margin-left: 0.5rem !important;
  }
  .let-m-md-3 {
    margin: 1rem !important;
  }
  .let-mt-md-3,
  .let-my-md-3 {
    margin-top: 1rem !important;
  }
  .let-mr-md-3,
  .let-mx-md-3 {
    margin-right: 1rem !important;
  }
  .let-mb-md-3,
  .let-my-md-3 {
    margin-bottom: 1rem !important;
  }
  .let-ml-md-3,
  .let-mx-md-3 {
    margin-left: 1rem !important;
  }
  .let-m-md-4 {
    margin: 1.5rem !important;
  }
  .let-mt-md-4,
  .let-my-md-4 {
    margin-top: 1.5rem !important;
  }
  .let-mr-md-4,
  .let-mx-md-4 {
    margin-right: 1.5rem !important;
  }
  .let-mb-md-4,
  .let-my-md-4 {
    margin-bottom: 1.5rem !important;
  }
  .let-ml-md-4,
  .let-mx-md-4 {
    margin-left: 1.5rem !important;
  }
  .let-m-md-5 {
    margin: 3rem !important;
  }
  .let-mt-md-5,
  .let-my-md-5 {
    margin-top: 3rem !important;
  }
  .let-mr-md-5,
  .let-mx-md-5 {
    margin-right: 3rem !important;
  }
  .let-mb-md-5,
  .let-my-md-5 {
    margin-bottom: 3rem !important;
  }
  .let-ml-md-5,
  .let-mx-md-5 {
    margin-left: 3rem !important;
  }
  .let-p-md-0 {
    padding: 0 !important;
  }
  .let-pt-md-0,
  .let-py-md-0 {
    padding-top: 0 !important;
  }
  .let-pr-md-0,
  .let-px-md-0 {
    padding-right: 0 !important;
  }
  .let-pb-md-0,
  .let-py-md-0 {
    padding-bottom: 0 !important;
  }
  .let-pl-md-0,
  .let-px-md-0 {
    padding-left: 0 !important;
  }
  .let-p-md-1 {
    padding: 0.25rem !important;
  }
  .let-pt-md-1,
  .let-py-md-1 {
    padding-top: 0.25rem !important;
  }
  .let-pr-md-1,
  .let-px-md-1 {
    padding-right: 0.25rem !important;
  }
  .let-pb-md-1,
  .let-py-md-1 {
    padding-bottom: 0.25rem !important;
  }
  .let-pl-md-1,
  .let-px-md-1 {
    padding-left: 0.25rem !important;
  }
  .let-p-md-2 {
    padding: 0.5rem !important;
  }
  .let-pt-md-2,
  .let-py-md-2 {
    padding-top: 0.5rem !important;
  }
  .let-pr-md-2,
  .let-px-md-2 {
    padding-right: 0.5rem !important;
  }
  .let-pb-md-2,
  .let-py-md-2 {
    padding-bottom: 0.5rem !important;
  }
  .let-pl-md-2,
  .let-px-md-2 {
    padding-left: 0.5rem !important;
  }
  .let-p-md-3 {
    padding: 1rem !important;
  }
  .let-pt-md-3,
  .let-py-md-3 {
    padding-top: 1rem !important;
  }
  .let-pr-md-3,
  .let-px-md-3 {
    padding-right: 1rem !important;
  }
  .let-pb-md-3,
  .let-py-md-3 {
    padding-bottom: 1rem !important;
  }
  .let-pl-md-3,
  .let-px-md-3 {
    padding-left: 1rem !important;
  }
  .let-p-md-4 {
    padding: 1.5rem !important;
  }
  .let-pt-md-4,
  .let-py-md-4 {
    padding-top: 1.5rem !important;
  }
  .let-pr-md-4,
  .let-px-md-4 {
    padding-right: 1.5rem !important;
  }
  .let-pb-md-4,
  .let-py-md-4 {
    padding-bottom: 1.5rem !important;
  }
  .let-pl-md-4,
  .let-px-md-4 {
    padding-left: 1.5rem !important;
  }
  .let-p-md-5 {
    padding: 3rem !important;
  }
  .let-pt-md-5,
  .let-py-md-5 {
    padding-top: 3rem !important;
  }
  .let-pr-md-5,
  .let-px-md-5 {
    padding-right: 3rem !important;
  }
  .let-pb-md-5,
  .let-py-md-5 {
    padding-bottom: 3rem !important;
  }
  .let-pl-md-5,
  .let-px-md-5 {
    padding-left: 3rem !important;
  }
  .let-m-md-n1 {
    margin: -0.25rem !important;
  }
  .let-mt-md-n1,
  .let-my-md-n1 {
    margin-top: -0.25rem !important;
  }
  .let-mr-md-n1,
  .let-mx-md-n1 {
    margin-right: -0.25rem !important;
  }
  .let-mb-md-n1,
  .let-my-md-n1 {
    margin-bottom: -0.25rem !important;
  }
  .let-ml-md-n1,
  .let-mx-md-n1 {
    margin-left: -0.25rem !important;
  }
  .let-m-md-n2 {
    margin: -0.5rem !important;
  }
  .let-mt-md-n2,
  .let-my-md-n2 {
    margin-top: -0.5rem !important;
  }
  .let-mr-md-n2,
  .let-mx-md-n2 {
    margin-right: -0.5rem !important;
  }
  .let-mb-md-n2,
  .let-my-md-n2 {
    margin-bottom: -0.5rem !important;
  }
  .let-ml-md-n2,
  .let-mx-md-n2 {
    margin-left: -0.5rem !important;
  }
  .let-m-md-n3 {
    margin: -1rem !important;
  }
  .let-mt-md-n3,
  .let-my-md-n3 {
    margin-top: -1rem !important;
  }
  .let-mr-md-n3,
  .let-mx-md-n3 {
    margin-right: -1rem !important;
  }
  .let-mb-md-n3,
  .let-my-md-n3 {
    margin-bottom: -1rem !important;
  }
  .let-ml-md-n3,
  .let-mx-md-n3 {
    margin-left: -1rem !important;
  }
  .let-m-md-n4 {
    margin: -1.5rem !important;
  }
  .let-mt-md-n4,
  .let-my-md-n4 {
    margin-top: -1.5rem !important;
  }
  .let-mr-md-n4,
  .let-mx-md-n4 {
    margin-right: -1.5rem !important;
  }
  .let-mb-md-n4,
  .let-my-md-n4 {
    margin-bottom: -1.5rem !important;
  }
  .let-ml-md-n4,
  .let-mx-md-n4 {
    margin-left: -1.5rem !important;
  }
  .let-m-md-n5 {
    margin: -3rem !important;
  }
  .let-mt-md-n5,
  .let-my-md-n5 {
    margin-top: -3rem !important;
  }
  .let-mr-md-n5,
  .let-mx-md-n5 {
    margin-right: -3rem !important;
  }
  .let-mb-md-n5,
  .let-my-md-n5 {
    margin-bottom: -3rem !important;
  }
  .let-ml-md-n5,
  .let-mx-md-n5 {
    margin-left: -3rem !important;
  }
  .let-m-md-auto {
    margin: auto !important;
  }
  .let-mt-md-auto,
  .let-my-md-auto {
    margin-top: auto !important;
  }
  .let-mr-md-auto,
  .let-mx-md-auto {
    margin-right: auto !important;
  }
  .let-mb-md-auto,
  .let-my-md-auto {
    margin-bottom: auto !important;
  }
  .let-ml-md-auto,
  .let-mx-md-auto {
    margin-left: auto !important;
  }
}

@media (min-width: 992px) {
  .let-m-lg-0 {
    margin: 0 !important;
  }
  .let-mt-lg-0,
  .let-my-lg-0 {
    margin-top: 0 !important;
  }
  .let-mr-lg-0,
  .let-mx-lg-0 {
    margin-right: 0 !important;
  }
  .let-mb-lg-0,
  .let-my-lg-0 {
    margin-bottom: 0 !important;
  }
  .let-ml-lg-0,
  .let-mx-lg-0 {
    margin-left: 0 !important;
  }
  .let-m-lg-1 {
    margin: 0.25rem !important;
  }
  .let-mt-lg-1,
  .let-my-lg-1 {
    margin-top: 0.25rem !important;
  }
  .let-mr-lg-1,
  .let-mx-lg-1 {
    margin-right: 0.25rem !important;
  }
  .let-mb-lg-1,
  .let-my-lg-1 {
    margin-bottom: 0.25rem !important;
  }
  .let-ml-lg-1,
  .let-mx-lg-1 {
    margin-left: 0.25rem !important;
  }
  .let-m-lg-2 {
    margin: 0.5rem !important;
  }
  .let-mt-lg-2,
  .let-my-lg-2 {
    margin-top: 0.5rem !important;
  }
  .let-mr-lg-2,
  .let-mx-lg-2 {
    margin-right: 0.5rem !important;
  }
  .let-mb-lg-2,
  .let-my-lg-2 {
    margin-bottom: 0.5rem !important;
  }
  .let-ml-lg-2,
  .let-mx-lg-2 {
    margin-left: 0.5rem !important;
  }
  .let-m-lg-3 {
    margin: 1rem !important;
  }
  .let-mt-lg-3,
  .let-my-lg-3 {
    margin-top: 1rem !important;
  }
  .let-mr-lg-3,
  .let-mx-lg-3 {
    margin-right: 1rem !important;
  }
  .let-mb-lg-3,
  .let-my-lg-3 {
    margin-bottom: 1rem !important;
  }
  .let-ml-lg-3,
  .let-mx-lg-3 {
    margin-left: 1rem !important;
  }
  .let-m-lg-4 {
    margin: 1.5rem !important;
  }
  .let-mt-lg-4,
  .let-my-lg-4 {
    margin-top: 1.5rem !important;
  }
  .let-mr-lg-4,
  .let-mx-lg-4 {
    margin-right: 1.5rem !important;
  }
  .let-mb-lg-4,
  .let-my-lg-4 {
    margin-bottom: 1.5rem !important;
  }
  .let-ml-lg-4,
  .let-mx-lg-4 {
    margin-left: 1.5rem !important;
  }
  .let-m-lg-5 {
    margin: 3rem !important;
  }
  .let-mt-lg-5,
  .let-my-lg-5 {
    margin-top: 3rem !important;
  }
  .let-mr-lg-5,
  .let-mx-lg-5 {
    margin-right: 3rem !important;
  }
  .let-mb-lg-5,
  .let-my-lg-5 {
    margin-bottom: 3rem !important;
  }
  .let-ml-lg-5,
  .let-mx-lg-5 {
    margin-left: 3rem !important;
  }
  .let-p-lg-0 {
    padding: 0 !important;
  }
  .let-pt-lg-0,
  .let-py-lg-0 {
    padding-top: 0 !important;
  }
  .let-pr-lg-0,
  .let-px-lg-0 {
    padding-right: 0 !important;
  }
  .let-pb-lg-0,
  .let-py-lg-0 {
    padding-bottom: 0 !important;
  }
  .let-pl-lg-0,
  .let-px-lg-0 {
    padding-left: 0 !important;
  }
  .let-p-lg-1 {
    padding: 0.25rem !important;
  }
  .let-pt-lg-1,
  .let-py-lg-1 {
    padding-top: 0.25rem !important;
  }
  .let-pr-lg-1,
  .let-px-lg-1 {
    padding-right: 0.25rem !important;
  }
  .let-pb-lg-1,
  .let-py-lg-1 {
    padding-bottom: 0.25rem !important;
  }
  .let-pl-lg-1,
  .let-px-lg-1 {
    padding-left: 0.25rem !important;
  }
  .let-p-lg-2 {
    padding: 0.5rem !important;
  }
  .let-pt-lg-2,
  .let-py-lg-2 {
    padding-top: 0.5rem !important;
  }
  .let-pr-lg-2,
  .let-px-lg-2 {
    padding-right: 0.5rem !important;
  }
  .let-pb-lg-2,
  .let-py-lg-2 {
    padding-bottom: 0.5rem !important;
  }
  .let-pl-lg-2,
  .let-px-lg-2 {
    padding-left: 0.5rem !important;
  }
  .let-p-lg-3 {
    padding: 1rem !important;
  }
  .let-pt-lg-3,
  .let-py-lg-3 {
    padding-top: 1rem !important;
  }
  .let-pr-lg-3,
  .let-px-lg-3 {
    padding-right: 1rem !important;
  }
  .let-pb-lg-3,
  .let-py-lg-3 {
    padding-bottom: 1rem !important;
  }
  .let-pl-lg-3,
  .let-px-lg-3 {
    padding-left: 1rem !important;
  }
  .let-p-lg-4 {
    padding: 1.5rem !important;
  }
  .let-pt-lg-4,
  .let-py-lg-4 {
    padding-top: 1.5rem !important;
  }
  .let-pr-lg-4,
  .let-px-lg-4 {
    padding-right: 1.5rem !important;
  }
  .let-pb-lg-4,
  .let-py-lg-4 {
    padding-bottom: 1.5rem !important;
  }
  .let-pl-lg-4,
  .let-px-lg-4 {
    padding-left: 1.5rem !important;
  }
  .let-p-lg-5 {
    padding: 3rem !important;
  }
  .let-pt-lg-5,
  .let-py-lg-5 {
    padding-top: 3rem !important;
  }
  .let-pr-lg-5,
  .let-px-lg-5 {
    padding-right: 3rem !important;
  }
  .let-pb-lg-5,
  .let-py-lg-5 {
    padding-bottom: 3rem !important;
  }
  .let-pl-lg-5,
  .let-px-lg-5 {
    padding-left: 3rem !important;
  }
  .let-m-lg-n1 {
    margin: -0.25rem !important;
  }
  .let-mt-lg-n1,
  .let-my-lg-n1 {
    margin-top: -0.25rem !important;
  }
  .let-mr-lg-n1,
  .let-mx-lg-n1 {
    margin-right: -0.25rem !important;
  }
  .let-mb-lg-n1,
  .let-my-lg-n1 {
    margin-bottom: -0.25rem !important;
  }
  .let-ml-lg-n1,
  .let-mx-lg-n1 {
    margin-left: -0.25rem !important;
  }
  .let-m-lg-n2 {
    margin: -0.5rem !important;
  }
  .let-mt-lg-n2,
  .let-my-lg-n2 {
    margin-top: -0.5rem !important;
  }
  .let-mr-lg-n2,
  .let-mx-lg-n2 {
    margin-right: -0.5rem !important;
  }
  .let-mb-lg-n2,
  .let-my-lg-n2 {
    margin-bottom: -0.5rem !important;
  }
  .let-ml-lg-n2,
  .let-mx-lg-n2 {
    margin-left: -0.5rem !important;
  }
  .let-m-lg-n3 {
    margin: -1rem !important;
  }
  .let-mt-lg-n3,
  .let-my-lg-n3 {
    margin-top: -1rem !important;
  }
  .let-mr-lg-n3,
  .let-mx-lg-n3 {
    margin-right: -1rem !important;
  }
  .let-mb-lg-n3,
  .let-my-lg-n3 {
    margin-bottom: -1rem !important;
  }
  .let-ml-lg-n3,
  .let-mx-lg-n3 {
    margin-left: -1rem !important;
  }
  .let-m-lg-n4 {
    margin: -1.5rem !important;
  }
  .let-mt-lg-n4,
  .let-my-lg-n4 {
    margin-top: -1.5rem !important;
  }
  .let-mr-lg-n4,
  .let-mx-lg-n4 {
    margin-right: -1.5rem !important;
  }
  .let-mb-lg-n4,
  .let-my-lg-n4 {
    margin-bottom: -1.5rem !important;
  }
  .let-ml-lg-n4,
  .let-mx-lg-n4 {
    margin-left: -1.5rem !important;
  }
  .let-m-lg-n5 {
    margin: -3rem !important;
  }
  .let-mt-lg-n5,
  .let-my-lg-n5 {
    margin-top: -3rem !important;
  }
  .let-mr-lg-n5,
  .let-mx-lg-n5 {
    margin-right: -3rem !important;
  }
  .let-mb-lg-n5,
  .let-my-lg-n5 {
    margin-bottom: -3rem !important;
  }
  .let-ml-lg-n5,
  .let-mx-lg-n5 {
    margin-left: -3rem !important;
  }
  .let-m-lg-auto {
    margin: auto !important;
  }
  .let-mt-lg-auto,
  .let-my-lg-auto {
    margin-top: auto !important;
  }
  .let-mr-lg-auto,
  .let-mx-lg-auto {
    margin-right: auto !important;
  }
  .let-mb-lg-auto,
  .let-my-lg-auto {
    margin-bottom: auto !important;
  }
  .let-ml-lg-auto,
  .let-mx-lg-auto {
    margin-left: auto !important;
  }
}

@media (min-width: 1200px) {
  .let-m-xl-0 {
    margin: 0 !important;
  }
  .let-mt-xl-0,
  .let-my-xl-0 {
    margin-top: 0 !important;
  }
  .let-mr-xl-0,
  .let-mx-xl-0 {
    margin-right: 0 !important;
  }
  .let-mb-xl-0,
  .let-my-xl-0 {
    margin-bottom: 0 !important;
  }
  .let-ml-xl-0,
  .let-mx-xl-0 {
    margin-left: 0 !important;
  }
  .let-m-xl-1 {
    margin: 0.25rem !important;
  }
  .let-mt-xl-1,
  .let-my-xl-1 {
    margin-top: 0.25rem !important;
  }
  .let-mr-xl-1,
  .let-mx-xl-1 {
    margin-right: 0.25rem !important;
  }
  .let-mb-xl-1,
  .let-my-xl-1 {
    margin-bottom: 0.25rem !important;
  }
  .let-ml-xl-1,
  .let-mx-xl-1 {
    margin-left: 0.25rem !important;
  }
  .let-m-xl-2 {
    margin: 0.5rem !important;
  }
  .let-mt-xl-2,
  .let-my-xl-2 {
    margin-top: 0.5rem !important;
  }
  .let-mr-xl-2,
  .let-mx-xl-2 {
    margin-right: 0.5rem !important;
  }
  .let-mb-xl-2,
  .let-my-xl-2 {
    margin-bottom: 0.5rem !important;
  }
  .let-ml-xl-2,
  .let-mx-xl-2 {
    margin-left: 0.5rem !important;
  }
  .let-m-xl-3 {
    margin: 1rem !important;
  }
  .let-mt-xl-3,
  .let-my-xl-3 {
    margin-top: 1rem !important;
  }
  .let-mr-xl-3,
  .let-mx-xl-3 {
    margin-right: 1rem !important;
  }
  .let-mb-xl-3,
  .let-my-xl-3 {
    margin-bottom: 1rem !important;
  }
  .let-ml-xl-3,
  .let-mx-xl-3 {
    margin-left: 1rem !important;
  }
  .let-m-xl-4 {
    margin: 1.5rem !important;
  }
  .let-mt-xl-4,
  .let-my-xl-4 {
    margin-top: 1.5rem !important;
  }
  .let-mr-xl-4,
  .let-mx-xl-4 {
    margin-right: 1.5rem !important;
  }
  .let-mb-xl-4,
  .let-my-xl-4 {
    margin-bottom: 1.5rem !important;
  }
  .let-ml-xl-4,
  .let-mx-xl-4 {
    margin-left: 1.5rem !important;
  }
  .let-m-xl-5 {
    margin: 3rem !important;
  }
  .let-mt-xl-5,
  .let-my-xl-5 {
    margin-top: 3rem !important;
  }
  .let-mr-xl-5,
  .let-mx-xl-5 {
    margin-right: 3rem !important;
  }
  .let-mb-xl-5,
  .let-my-xl-5 {
    margin-bottom: 3rem !important;
  }
  .let-ml-xl-5,
  .let-mx-xl-5 {
    margin-left: 3rem !important;
  }
  .let-p-xl-0 {
    padding: 0 !important;
  }
  .let-pt-xl-0,
  .let-py-xl-0 {
    padding-top: 0 !important;
  }
  .let-pr-xl-0,
  .let-px-xl-0 {
    padding-right: 0 !important;
  }
  .let-pb-xl-0,
  .let-py-xl-0 {
    padding-bottom: 0 !important;
  }
  .let-pl-xl-0,
  .let-px-xl-0 {
    padding-left: 0 !important;
  }
  .let-p-xl-1 {
    padding: 0.25rem !important;
  }
  .let-pt-xl-1,
  .let-py-xl-1 {
    padding-top: 0.25rem !important;
  }
  .let-pr-xl-1,
  .let-px-xl-1 {
    padding-right: 0.25rem !important;
  }
  .let-pb-xl-1,
  .let-py-xl-1 {
    padding-bottom: 0.25rem !important;
  }
  .let-pl-xl-1,
  .let-px-xl-1 {
    padding-left: 0.25rem !important;
  }
  .let-p-xl-2 {
    padding: 0.5rem !important;
  }
  .let-pt-xl-2,
  .let-py-xl-2 {
    padding-top: 0.5rem !important;
  }
  .let-pr-xl-2,
  .let-px-xl-2 {
    padding-right: 0.5rem !important;
  }
  .let-pb-xl-2,
  .let-py-xl-2 {
    padding-bottom: 0.5rem !important;
  }
  .let-pl-xl-2,
  .let-px-xl-2 {
    padding-left: 0.5rem !important;
  }
  .let-p-xl-3 {
    padding: 1rem !important;
  }
  .let-pt-xl-3,
  .let-py-xl-3 {
    padding-top: 1rem !important;
  }
  .let-pr-xl-3,
  .let-px-xl-3 {
    padding-right: 1rem !important;
  }
  .let-pb-xl-3,
  .let-py-xl-3 {
    padding-bottom: 1rem !important;
  }
  .let-pl-xl-3,
  .let-px-xl-3 {
    padding-left: 1rem !important;
  }
  .let-p-xl-4 {
    padding: 1.5rem !important;
  }
  .let-pt-xl-4,
  .let-py-xl-4 {
    padding-top: 1.5rem !important;
  }
  .let-pr-xl-4,
  .let-px-xl-4 {
    padding-right: 1.5rem !important;
  }
  .let-pb-xl-4,
  .let-py-xl-4 {
    padding-bottom: 1.5rem !important;
  }
  .let-pl-xl-4,
  .let-px-xl-4 {
    padding-left: 1.5rem !important;
  }
  .let-p-xl-5 {
    padding: 3rem !important;
  }
  .let-pt-xl-5,
  .let-py-xl-5 {
    padding-top: 3rem !important;
  }
  .let-pr-xl-5,
  .let-px-xl-5 {
    padding-right: 3rem !important;
  }
  .let-pb-xl-5,
  .let-py-xl-5 {
    padding-bottom: 3rem !important;
  }
  .let-pl-xl-5,
  .let-px-xl-5 {
    padding-left: 3rem !important;
  }
  .let-m-xl-n1 {
    margin: -0.25rem !important;
  }
  .let-mt-xl-n1,
  .let-my-xl-n1 {
    margin-top: -0.25rem !important;
  }
  .let-mr-xl-n1,
  .let-mx-xl-n1 {
    margin-right: -0.25rem !important;
  }
  .let-mb-xl-n1,
  .let-my-xl-n1 {
    margin-bottom: -0.25rem !important;
  }
  .let-ml-xl-n1,
  .let-mx-xl-n1 {
    margin-left: -0.25rem !important;
  }
  .let-m-xl-n2 {
    margin: -0.5rem !important;
  }
  .let-mt-xl-n2,
  .let-my-xl-n2 {
    margin-top: -0.5rem !important;
  }
  .let-mr-xl-n2,
  .let-mx-xl-n2 {
    margin-right: -0.5rem !important;
  }
  .let-mb-xl-n2,
  .let-my-xl-n2 {
    margin-bottom: -0.5rem !important;
  }
  .let-ml-xl-n2,
  .let-mx-xl-n2 {
    margin-left: -0.5rem !important;
  }
  .let-m-xl-n3 {
    margin: -1rem !important;
  }
  .let-mt-xl-n3,
  .let-my-xl-n3 {
    margin-top: -1rem !important;
  }
  .let-mr-xl-n3,
  .let-mx-xl-n3 {
    margin-right: -1rem !important;
  }
  .let-mb-xl-n3,
  .let-my-xl-n3 {
    margin-bottom: -1rem !important;
  }
  .let-ml-xl-n3,
  .let-mx-xl-n3 {
    margin-left: -1rem !important;
  }
  .let-m-xl-n4 {
    margin: -1.5rem !important;
  }
  .let-mt-xl-n4,
  .let-my-xl-n4 {
    margin-top: -1.5rem !important;
  }
  .let-mr-xl-n4,
  .let-mx-xl-n4 {
    margin-right: -1.5rem !important;
  }
  .let-mb-xl-n4,
  .let-my-xl-n4 {
    margin-bottom: -1.5rem !important;
  }
  .let-ml-xl-n4,
  .let-mx-xl-n4 {
    margin-left: -1.5rem !important;
  }
  .let-m-xl-n5 {
    margin: -3rem !important;
  }
  .let-mt-xl-n5,
  .let-my-xl-n5 {
    margin-top: -3rem !important;
  }
  .let-mr-xl-n5,
  .let-mx-xl-n5 {
    margin-right: -3rem !important;
  }
  .let-mb-xl-n5,
  .let-my-xl-n5 {
    margin-bottom: -3rem !important;
  }
  .let-ml-xl-n5,
  .let-mx-xl-n5 {
    margin-left: -3rem !important;
  }
  .let-m-xl-auto {
    margin: auto !important;
  }
  .let-mt-xl-auto,
  .let-my-xl-auto {
    margin-top: auto !important;
  }
  .let-mr-xl-auto,
  .let-mx-xl-auto {
    margin-right: auto !important;
  }
  .let-mb-xl-auto,
  .let-my-xl-auto {
    margin-bottom: auto !important;
  }
  .let-ml-xl-auto,
  .let-mx-xl-auto {
    margin-left: auto !important;
  }
}

.let-text-monospace {
  font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace !important;
}

.let-text-justify {
  text-align: justify !important;
}

.let-text-wrap {
  white-space: normal !important;
}

.let-text-nowrap {
  white-space: nowrap !important;
}

.let-text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.let-text-left {
  text-align: left !important;
}

.let-text-right {
  text-align: right !important;
}

.let-text-center {
  text-align: center !important;
}

@media (min-width: 576px) {
  .let-text-sm-left {
    text-align: left !important;
  }
  .let-text-sm-right {
    text-align: right !important;
  }
  .let-text-sm-center {
    text-align: center !important;
  }
}

@media (min-width: 768px) {
  .let-text-md-left {
    text-align: left !important;
  }
  .let-text-md-right {
    text-align: right !important;
  }
  .let-text-md-center {
    text-align: center !important;
  }
}

@media (min-width: 992px) {
  .let-text-lg-left {
    text-align: left !important;
  }
  .let-text-lg-right {
    text-align: right !important;
  }
  .let-text-lg-center {
    text-align: center !important;
  }
}

@media (min-width: 1200px) {
  .let-text-xl-left {
    text-align: left !important;
  }
  .let-text-xl-right {
    text-align: right !important;
  }
  .let-text-xl-center {
    text-align: center !important;
  }
}

.let-text-lowercase {
  text-transform: lowercase !important;
}

.let-text-uppercase {
  text-transform: uppercase !important;
}

.let-text-capitalize {
  text-transform: capitalize !important;
}

.let-font-weight-light {
  font-weight: 300 !important;
}

.let-font-weight-lighter {
  font-weight: lighter !important;
}

.let-font-weight-normal {
  font-weight: 400 !important;
}

.let-font-weight-bold {
  font-weight: 700 !important;
}

.let-font-weight-bolder {
  font-weight: bolder !important;
}

.let-font-italic {
  font-style: italic !important;
}

.let-text-white {
  color: #fff !important;
}

.let-text-primary {
  color: #007bff !important;
}

a.let-text-primary:hover, a.let-text-primary:focus {
  color: #0056b3 !important;
}

.let-text-secondary {
  color: #6c757d !important;
}

a.let-text-secondary:hover, a.let-text-secondary:focus {
  color: #494f54 !important;
}

.let-text-success {
  color: #1ebdce !important;
}

a.let-text-success:hover, a.let-text-success:focus {
  color: #19692c !important;
}

.let-text-info {
  color: #17a2b8 !important;
}

a.let-text-info:hover, a.let-text-info:focus {
  color: #0f6674 !important;
}

.let-text-warning {
  color: #ffc107 !important;
}

a.let-text-warning:hover, a.let-text-warning:focus {
  color: #ba8b00 !important;
}

.let-text-danger {
  color: #dc3545 !important;
}

a.let-text-danger:hover, a.let-text-danger:focus {
  color: #a71d2a !important;
}

.let-text-light {
  color: #f8f9fa !important;
}

a.let-text-light:hover, a.let-text-light:focus {
  color: #cbd3da !important;
}

.let-text-dark {
  color: #343a40 !important;
}

a.let-text-dark:hover, a.let-text-dark:focus {
  color: #121416 !important;
}

.let-text-body {
  color: #212529 !important;
}

.let-text-muted {
  color: #6c757d !important;
}

.let-text-black-50 {
  color: rgba(0, 0, 0, 0.5) !important;
}

.let-text-white-50 {
  color: rgba(255, 255, 255, 0.5) !important;
}

.let-text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}

.let-text-decoration-none {
  text-decoration: none !important;
}

.let-text-break {
  word-break: break-word !important;
  overflow-wrap: break-word !important;
}

.let-text-reset {
  color: inherit !important;
}

.let-visible {
  visibility: visible !important;
}

.let-invisible {
  visibility: hidden !important;
}

@media print {
  *,
  *::before,
  *::after {
    text-shadow: none !important;
    box-shadow: none !important;
  }
  a:not(.let-btn) {
    text-decoration: underline;
  }
  abbr[title]::after {
    content: " (" attr(title) ")";
  }
  pre {
    white-space: pre-wrap !important;
  }
  pre,
  blockquote {
    border: 1px solid #adb5bd;
    page-break-inside: avoid;
  }
  thead {
    display: table-header-group;
  }
  tr,
  img {
    page-break-inside: avoid;
  }
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  h2,
  h3 {
    page-break-after: avoid;
  }
  @page {
    size: a3;
  }
  body {
    min-width: 992px !important;
  }
  .let-container {
    min-width: 992px !important;
  }
  .let-navbar {
    display: none;
  }
  .let-badge {
    border: 1px solid #000;
  }
  .let-table {
    border-collapse: collapse !important;
  }
  .let-table td,
  .let-table th {
    background-color: #fff !important;
  }
  .let-table-bordered th,
  .let-table-bordered td {
    border: 1px solid #dee2e6 !important;
  }
  .let-table-dark {
    color: inherit;
  }
  .let-table-dark th,
  .let-table-dark td,
  .let-table-dark thead th,
  .let-table-dark tbody + tbody {
    border-color: #dee2e6;
  }
  .let-table .let-thead-dark th {
    color: inherit;
    border-color: #dee2e6;
  }
}

.col-full
{
  max-width: 100%!important
}
.let-pie-table tr td
{
text-align: left!important
}

.bmw_help_words
{
  font-size: 1em!important
}
/*# sourceMappingURL=bootstrap.let-css.let-map */


