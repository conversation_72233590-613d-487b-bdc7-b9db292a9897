<?php
use <PERSON>upal\Core\Url;
use Drupal\Core\Database\Database;
use Drupal\Core\Link;
use <PERSON><PERSON><PERSON>\user\Entity\User;
/**
 * @file
 * Contains unilevelmlm.module.
 */ 
:start_line:10
-------
/**
 * Implements hook_mail().
 *
 * Prepares and formats emails for the Unilevel MLM module.
 *
 * @param string $key
 *   The email key.
 * @param array $message
 *   An array containing the email details.
 * @param array $params
 *   An array of parameters to be used in the email.
 */
function unilevelmlm_mail($key, &$message, $params) {
  $options = array(
    'langcode' => $message['langcode'],
  );
  
  switch ($key) {
    case 'ump_registration':
      $headers = array(
        'MIME-Version' => '1.0',
        'Content-Type' => 'text/html; charset=UTF-8; format=flowed',
        'Content-Transfer-Encoding' => '8Bit',
        'X-Mailer' => 'Drupal'
      );
      foreach ($headers as $key => $value) {
        $message['headers'][$key] = $value;
      }
      $message['from'] = \Drupal::config('system.site')->get('mail');
      $message['subject'] = t('Your mail subject Here: @title', array('@title' => $params['title']), $options);
      $message['body'][] = $params['message'];
      break;
    case 'ump_user_withdrawal_request':
      $headers = array(
        'MIME-Version' => '1.0',
        'Content-Type' => 'text/html; charset=UTF-8; format=flowed',
        'Content-Transfer-Encoding' => '8Bit',
        'X-Mailer' => 'Drupal'
      );
      foreach ($headers as $key => $value) {
        $message['headers'][$key] = $value;
      }
       
      $message['from'] = \Drupal::config('system.site')->get('mail');
      $message['subject'] = t('User withdrawal request: @title', array('@title' => $params['title']), $options);
      $message['body'][] = $params['message'];
      break;
    case 'ump_admin_transfer_amount':
      $headers = array(
        'MIME-Version' => '1.0',
        'Content-Type' => 'text/html; charset=UTF-8; format=flowed',
        'Content-Transfer-Encoding' => '8Bit',
        'X-Mailer' => 'Drupal'
      );
      foreach ($headers as $key => $value) {
        $message['headers'][$key] = $value;
      }
      $message['from'] = \Drupal::config('system.site')->get('mail');
      $message['subject'] = t('Your mail subject Here: @title', array('@title' => $params['title']), $options);
      $message['body'][] = $params['message'];
      break;
    case 'user_join_ump':
      $headers = array(
        'MIME-Version' => '1.0',
        'Content-Type' => 'text/html; charset=UTF-8; format=flowed',
        'Content-Transfer-Encoding' => '8Bit',
        'X-Mailer' => 'Drupal'
      );
      foreach ($headers as $key => $value) {
        $message['headers'][$key] = $value;
      }
      $message['from'] = \Drupal::config('system.site')->get('mail');
      $message['subject'] = t('Your mail subject Here: @title', array('@title' => $params['title']), $options);
      $message['body'][] = $params['message'];
      break;
  }
}
:start_line:75
-------
/**
 * Sends an email using the Drupal mail manager.
 *
 * @param string $ump_key
 *   The email key.
 * @param string $toemail
 *   The recipient's email address.
 * @param string $title
 *   The email title.
 * @param string $msg
 *   The email message body.
 */
function ump_send_mail_function($ump_key='', $toemail='',$title='',$msg=''){
  $mailManager = \Drupal::service('plugin.manager.mail');
  $module = 'unilevelmlm';
  $key =$ump_key; // Replace with Your key
                    $to=$toemail ;
                    $params['title'] = $title;
                    $params['message'] = $msg;
                    $langcode = \Drupal::currentUser()->getPreferredLangcode();
                    $send = true;
                    $result = $mailManager->mail($module, $key, $to, $langcode, $params, NULL, $send);
                    if ($result['result'] != true) {
                    $message = t('There was a problem sending your email notification to @email.', array('@email' => $to));
                  }
                  return;
}
:start_line:90
-------
/**
 * Retrieves a list of e-pins.
 *
 * @return array
 *   An array of e-pin data.
 */
function get_epins() {
  $res = array();
  $connection = \Drupal::service('database');
  $result = $connection->query("SELECT e.*,(SELECT COUNT(*) FROM {ump_epins} as ep WHERE ep.master_id = e.id) as total,(SELECT COUNT(*) FROM {ump_epins} as eq WHERE eq.master_id = e.id AND eq.status!=0) as total_used  FROM {ump_epin_master} as e");
  $res = $result->fetchAll();
  $ret = [];
 foreach ($res as $row) {   
  //  $delete = Url::fromUserInput('/admin/config/unilevelmlm/settings/generate_epins/delete/' . $row->id, array('attributes' => array('onclick' => "return confirm('Are you Sure')")));
  //  $edit = Url::fromUserInput('/admin/config/unilevelmlm/settings/generate_epins/edit/' . $row->id);
   $view = Url::fromUserInput('/admin/config/unilevelmlm/settings/display_epins/viewallepins/' . $row->id);     
  //  $edit_link = Link::fromTextAndUrl(t('Edit'), $edit)->toString();
  //  $delete_link = Link::fromTextAndUrl(t('Delete'), $delete)->toString();
   $view_link = Link::fromTextAndUrl(t('View'), $view)->toString();   
  //  $mainLink = t('@linkApprove  @linkReject @linkdisplay' , array('@linkApprove' => $edit_link, '@linkReject' => $delete_link, '@linkdisplay'=>$view_link));
   $mainLink = t('@linkdisplay' , array('@linkdisplay'=>$view_link));

  $ret[] = [
    'id' => $row->id,
    'epin_name' => $row->epin_name,
    'epin_price'=>$row->epin_price,
    'type'=>$row->type,
    'sold'=>$row->total_used.'/'.$row->total,
    'create_date'=>date_format(date_create($row->create_date), ' jS M Y'),      
    'action' => $mainLink,		 
    ];
  }
  return $ret;
}
:start_line:118
-------
/**
 * Retrieves a report of users.
 *
 * @return array
 *   An array of user report data.
 */
function get_user_report(){
  $res = array();
  $connection = \Drupal::service('database');
  
  $result = $connection->query("SELECT uu.user_id as user_id, (SELECT sum(up.join_commission_amount) FROM ump_payout as up where up.user_id=uu.user_id) as join_commission , (SELECT sum(up.referral_commission_amount) FROM ump_payout as up where up.user_id=uu.user_id) as referral_commission , (SELECT sum(up.level_commission_amount) FROM ump_payout as up where up.user_id=uu.user_id) as level_commission , (SELECT sum(up.total_amount) FROM ump_payout as up where up.user_id=uu.user_id) as total_amount from ump_user as uu");
  $res = $result->fetchAll();
     
  $ret = [];
 foreach ($res as $row) { 
   
  //  $delete = Url::fromUserInput('/admin/config/unilevelmlm/settings/generate_epins/delete/' . $row->id, array('attributes' => array('onclick' => "return confirm('Are you Sure')")));
  //  $edit = Url::fromUserInput('/admin/config/unilevelmlm/settings/generate_epins/edit/' . $row->id);
   $view = Url::fromUserInput('/admin/config/unilevelmlm/settings/user_report/' . $row->user_id);     
  //  $edit_link = Link::fromTextAndUrl(t('Edit'), $edit)->toString();
  //  $delete_link = Link::fromTextndUrl(t('Delete'), $delete)->toString();
   $view_link = Link::fromTextAndUrl(t('View'), $view)->toString();   
  //  $mainLink = t('@linkApprove  @linkReject @linkdisplay' , array('@linkApprove' => $edit_link, '@linkReject' => $delete_link, '@linkdisplay'=>$view_link));
   $mainLink = t('@linkdisplay' , array('@linkdisplay'=>$view_link));

  $ret[] = [
    'user_id' => $row->user_id,
    'user_name' => ump_get_child_user_name_by_id($row->user_id),
    'join_commission'=>($row->join_commission>0)?$row->join_commission:'0.0',
    'referral_commission'=>($row->referral_commission>0)?$row->referral_commission:'0.0',    
    'total_amount'=>($row->total_amount>0)?$row->total_amount:'0.0',        
    'action' => $mainLink,		 
    ];
  }  
  return $ret;

}

:start_line:194
-------
/**
 * Retrieves a report of payouts.
 *
 * @return array
 *   An array of payout report data.
 */
function get_payout_report(){

  $res = array();
  $connection = \Drupal::service('database');
  
  $result = $connection->query("SELECT * FROM {ump_payout}");
  $res = $result->fetchAll();
     
  $ret = [];
 foreach ($res as $row) {  
 
  //  $delete = Url::fromUserInput('/admin/config/unilevelmlm/settings/generate_epins/delete/' . $row->id, array('attributes' => array('onclick' => "return confirm('Are you Sure')")));
  //  $edit = Url::fromUserInput('/admin/config/unilevelmlm/settings/generate_epins/edit/' . $row->id);
   $view = Url::fromUserInput('/admin/config/unilevelmlm/settings/pay_report/' . $row->id);     
  //  $edit_link = Link::fromTextAndUrl(t('Edit'), $edit)->toString();
  //  $delete_link = Link::fromTextAndUrl(t('Delete'), $delete)->toString();
   $view_link = Link::fromTextAndUrl(t('View'), $view)->toString();   
  //  $mainLink = t('@linkApprove  @linkReject @linkdisplay' , array('@linkApprove' => $edit_link, '@linkReject' => $delete_link, '@linkdisplay'=>$view_link));
   $mainLink = t('@linkdisplay' , array('@linkdisplay'=>$view_link));

  $ret[] = [
    'payout_id' => $row->id,
    'user_name' => ump_get_child_user_name_by_id($row->user_id),
    'join_commission'=>($row->join_commission_amount>0)?$row->join_commission_amount:'0.0',
    'referral_commission'=>($row->referral_commission_amount>0)?$row->referral_commission_amount:'0.0',     
    'total_amount'=>$row->total_amount,        
    'action' => $mainLink,		 
    ];
  }  
  return $ret;

}

:start_line:227
-------
/**
 * Retrieves all e-pins for a given master ID.
 *
 * @param int $id
 *   The master ID.
 *
 * @return array
 *   An array of e-pin data.
 */
function get_all_epins($id) {
  $res = array();
  $connection = \Drupal::service('database');
  $results = $connection->select('ump_epins', 'ue')
  ->extend('\Drupal\Core\Database\Query\PagerSelectExtender');
  $results->fields('ue');
  $results->condition('ue.master_id',$id);
  $res = $results->execute()->fetchAll();
  $ret = [];
 foreach ($res as $row) {   
  //  $delete = Url::fromUserInput('/admin/config/unilevelmlm/settings/generate_epins/delete/' . $row->id, array('attributes' => array('onclick' => "return confirm('Are you Sure')")));
  //  $edit = Url::fromUserInput('/admin/config/unilevelmlm/settings/generate_epins/edit/' . $row->id);        
  //  $edit_link = Link::fromTextAndUrl(t('Edit'), $edit)->toString();
  //  $delete_link = Link::fromTextAndUrl(t('Delete'), $delete)->toString();     
  //  $mainLink = t('@linkApprove  @linkReject @linkdisplay' , array('@linkApprove' => $edit_link, '@linkReject' => $delete_link));
   $ret[] = [     
    'epin_no' => $row->epin_no,
    'type' => $row->type,
    'price' => $row->price,
    'date_generated' => $row->date_generated,
    'user_key' => $row->user_id,
    'date_used' => ($row->date_used!=NULL)?date_format(date_create($row->date_used), ' jS M Y'):'0000-00-00 00:00:00',
    'status'=>$row->status   	 
    ];
  }
  return $ret;
} 
:start_line:255
-------
/**
 * Retrieves a list of withdrawals.
 *
 * @return array
 *   An array of withdrawal data.
 */
function withdrawal_list() {
  $res = array();
  $connection = \Drupal::service('database');
  $result = $connection->select('ump_withdrawal', 'uw')
  ->fields('uw', array('id','user_id', 'withdrawal_initiated_date','payment_processed', 'payment_processed_date','amount'))
  ->execute()->fetchAll();
  
  
  
          
  $ret = [];  
 foreach ($result as $row) {   
  //  $delete = Url::fromUserInput('/admin/config/unilevelmlm/settings/generate_epins/delete/' . $row->id, array('attributes' => array('onclick' => "return confirm('Are you Sure')")));
  //  $edit = Url::fromUserInput('/admin/config/unilevelmlm/settings/generate_epins/edit/' . $row->id);
   $view = Url::fromUserInput('/admin/config/unilevelmlm/settings/withdrawal/transfer_amount/'.$row->user_id.'/'.$row->id);     
  //  $edit_link = Link::fromTextAndUrl(t('Edit'), $edit)->toString();
  //  $delete_link = Link::fromTextAndUrl(t('Delete'), $delete)->toString();
  if($row->payment_processed=='0')
   $view_link = Link::fromTextAndUrl(t('Transfer'), $view)->toString();   
   else
   $view_link = Link::fromTextAndUrl(t('View'), $view)->toString();    
   
  //  $mainLink = t('@linkApprove  @linkReject @linkdisplay' , array('@linkApprove' => $edit_link, '@linkReject' => $delete_link, '@linkdisplay'=>$view_link));
   $mainLink = t('@linkdisplay' , array('@linkdisplay'=>$view_link));

  $ret[] = [
    'id' => $row->id,
    'user_id' => $row->user_id,
    'user_name' => ump_get_child_user_name_by_id($row->user_id),
    'withdrawal_initiated_date'=> $row->withdrawal_initiated_date,
    'payment_processed_date'=>$row->payment_processed_date,
    'amount'=>$row->amount,          
    'action' => $mainLink,		 
    ];
  }
  return $ret;
}

:start_line:292
-------
/**
 * Retrieves the first user.
 *
 * @return int
 *   The number of first users.
 */
function get_first_user(){
  $connection = \Drupal::service('database');
  $results = $connection->select('ump_user', 'uu')
  ->extend('\Drupal\Core\Database\Query\PagerSelectExtender');
  $results->fields('uu');
  $results->condition('uu.sponsor_key','0');
  $results->condition('uu.parent_key','0');
  $res = $results->execute()->fetchAll();
  // print_r($res);die;
  $num_row=count($res);
  return $num_row;
}

:start_line:305
-------
/**
 * Generates a unique key.
 *
 * @return string
 *   A unique key.
 */
function ump_generateKey()
{
        $characters = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];

        $keys = array();

        $length = 9;

        while (count($keys) < $length) {
            $x = mt_rand(0, count($characters) - 1);
            if (!in_array($x, $keys))
                $keys[] = $x;
        }

        // extract each key from array
        $random_chars = '';
        foreach ($keys as $key)
            $random_chars .= $characters[$key];

        // display random key
        return $random_chars;
}

 

function unilevelmlm_page_attachments(&$attachments) {
  $current_user=\Drupal::currentUser();
  $attachments['#attached']['drupalSettings']['unilevelmlm']['earning'] = ump_get_earning_detail_for_graph($current_user->id());
  $attachments['#attached']['drupalSettings']['unilevelmlm']['payout'] = ump_get_donugt_data($current_user->id());  
   
  $attachments['#attached']['drupalSettings']['unilevelmlm']['genealogy'] = get_data_for_dv($current_user->id());  
}

 
function check_user_name_esixt($user_name=''){
   
  $connection = \Drupal::service('database');   
  $num_row=$connection->query("SELECT count(*) FROM {users_field_data} WHERE name=:user_name",[':user_name'=>$user_name])->fetchField();    
  return $num_row;  
   
}
function check_email($user_email=''){
  return \Drupal::entityQuery('user')
  ->condition('mail', $user_email)
  ->execute();
}
 
function ump_sponsor_exist_function($sponsor_name='')
{   
  $connection = \Drupal::service('database');  
  $sponsor_id = $connection->query("SELECT uid FROM {users_field_data} WHERE name=:sponsor_name",['sponsor_name'=>$sponsor_name])->fetchField();
  $paid = $connection->query("SELECT COUNT(*) FROM {ump_user} WHERE user_id=:sponsor_id AND payment_status=:payment_status",['sponsor_id'=>$sponsor_id,'payment_status'=>'1'])->fetchField();
  $referrals = $connection->query("SELECT COUNT(*) FROM {ump_user} WHERE sponsor_key=:sponsor_id",['sponsor_id'=>$sponsor_id])->fetchField();
  $config_refferal = \Drupal::config('unilevelmlm.general')->get('ump_referrals');    
  if(!empty($sponsor_id) && ($config_refferal > $referrals) && !empty($paid)) { 
    return 1;    
  } else {    
      return 0;          
    }
}
function ump_check_epins($epin_no='', $ump_sponsor='')
{ 
    $connection = \Drupal::service('database'); 
    $ePin_active = \Drupal::config('unilevelmlm.general')->get('activate_epin');   
   $json=['status'=>true];
    
   if($ePin_active){
     $query = $connection->select('ump_epins', 'ue');
     $query->fields('ue', ['user_id', 'epin_no']);
     $query->innerJoin('ump_epin_master', 'uem', 'ue.master_id = uem.id');
     $query->condition('uem.epin_name', $epin_no);
     $check_epin_data = $query->execute()->fetchObject();
     if ($check_epin_data) {
        $hashed_epin = $check_epin_data->epin_no;
        $user_id = $check_epin_data->user_id;
     }
     
     $query = $connection->select('ump_epins', 'ue');
     $query->fields('ue');
     $query->innerJoin('ump_epin_master', 'uem', 'ue.master_id = uem.id');
     $query->condition('uem.epin_name', $epin_no);
     $query->condition('ue.status', 0);
     $check_epin_a = $query->execute()->fetchObject();
     $query = $connection->select('ump_epin_requests', 'uer');
     $query->fields('uer');
     $query->condition('uer.assigned_epin', $epin_no);
     $query->condition('uer.status', 1);
     $check_epin_buy = $query->execute()->fetchObject();
     if (!empty($check_epin_buy)) {
       $query_sponsor = $connection->select('users_field_data', 'ufd');
       $query_sponsor->addField('ufd', 'uid');
       $query_sponsor->condition('ufd.name', $ump_sponsor);
       $user_id = $query_sponsor->execute()->fetchField();
       $user_name = ump_get_child_user_name_by_id($check_epin_buy->$user_id);
       
      }
    $name = '';
    
    if(!empty($check_epin_buy))
    {
       if ($check_epin_buy->user_id !== $user_id) {
         $json['status'] = false;
              $json['message'] = t('this ePin only can  Used under other User Please try another ePin');
          } else {
              $json['status'] = true;
              $json['message'] = t('Congratulation!This ePin is avaiable.');
            }
      } else{
            if ($check_epin_data && password_verify($epin_no, $hashed_epin)) {
              $json['status'] = false;
              $json['message'] = t('ePin Already Used by Please try another ePin');
            } else {
              if (empty($epin_no)) {
                $json['status'] = false;
                $json['message'] = t('ePin could not be empty.');
              } else if (!empty($check_epin_a)) {
                 if ($check_epin_data && password_verify($epin_no, $hashed_epin)) {
                    $json['status'] = true;
                    $json['message'] = t('Congratulation!This ePin is avaiable.');
                 }else{
                    $json['status'] = false;
                    $json['message'] = t('Your ePin is invalid');
                 }
               
              } else if ((empty($check_epin_data)) && (empty($check_epin_a))) {
                $json['status'] = false;
                $json['message'] = t('Your ePin is invalid');
              }
            }
          }
        }else{
          $json['status'] = true;
          $json['message'] = t('Epin is not activate');
        }         
        $epin_price = 0;
        $query = $connection->select('ump_epin_master', 'uem');
        $query->fields('uem', ['epin_price']);
        $query->condition('uem.epin_name', $epin_no);
        $check_epin_master = $query->execute()->fetchField();
        if(!empty($check_epin_master)){
          $epin_price = $check_epin_master;
        }
        // Check for rebate/discount rules
        $query = $connection->select('rebate_discount_rule', 'rdr');
        $query->fields('rdr');
        $query->condition('rdr.applies_to', 'epin');
        $query->condition('rdr.status', 1);
        $rebate_discount_rule = $query->execute()->fetchObject();

        if ($rebate_discount_rule) {
          // Calculate discounted price
          if ($rebate_discount_rule->discount_type == 'percentage') {
            $discount_amount = ($epin_price * $rebate_discount_rule->discount_amount) / 100;
            $discounted_price = $epin_price - $discount_amount;
          } else {
            $discounted_price = $epin_price - $rebate_discount_rule->discount_amount;
          }

          $json['discounted_price'] = $discounted_price;
        } else {
          $json['discounted_price'] = $epin_price;
        }

        return $json;
}

function ump_user_fronend_regitration(){

}

function ump_get_parent_key($sponsor_id)
{     
  $config_refferal = \Drupal::config('unilevelmlm.general')->get('ump_referrals');
  $connection=\Drupal::srvice('database');
    if (!empty($sponsor_id)) {
        $query = $connection->select('ump_user', 'uu');
        $query->fields('uu');
        $query->condition('uu.parent_key', $sponsor_id);
        $users = $query->execute()->fetchAll();

        foreach ($users as $key => $user) {
            $query_referrals = $connection->select('ump_user', 'uu');
            $query_referrals->addExpression('COUNT(*)', 'count');
            $query_referrals->condition('uu.parent_key', $user->user_id);
            $referrals = $query_referrals->execute()->fetchField();
            if ($config_refferal > $referrals) {
                $parent_key = $user->user_id;
                return $parent_key;
            } else {                 
                $sponsor_id = $user->user_id;
                $parent_key =   ump_get_parent_key($sponsor_id);
                return $parent_key;
            }
        }
    }
}

function ump_insert_hirerchyrecord($user_id, $n_row)
{
  $connection=\Drupal::service('database');
  
  
  $ump_no_of_level = \Drupal::config('unilevelmlm.general')->get('ump_no_of_levels');
  $parentUserkey[0] = $user_id;
  for ($i = 1; $i <= $ump_no_of_level; $i++) {
    $parentUserkey[$i] = ump_returnMemberParentkey($parentUserkey[$i - 1]);
        if ($parentUserkey[$i] == 0 || $parentUserkey[$i] == '') {
          break;
        } else {             
            $connection->insert('ump_hierarchy')->fields([
              'parent_id'=>"$parentUserkey[$i]",
              'child_id'=>"$user_id",
              'level'=>"$i",
              'n_row'=>"$n_row"
              ])->execute();
              $query_level = $connection->select('ump_user', 'uu');
              $query_level->addField('uu', 'level');
              $query_level->condition('uu.user_id', $parentUserkey[$i]);
              $user_level = $query_level->execute()->fetchField();

              if ($user_level < $i) {
                $query = $connection->update('ump_user');
                $query->fields([
                  'level'=>"$i"
                  ]);
                $query->condition('user_id',$parentUserkey[$i],'=');
                $query->execute();
                }
              }
            }
            
}

function ump_returnMemberParentkey($user_id)
{
    $connection=\Drupal::service('database');
    $query_parent = $connection->select('ump_user', 'uu');
    $query_parent->addField('uu', 'parent_key');
    $query_parent->condition('uu.user_id', $user_id);
    $parent_key = $query_parent->execute()->fetchField();
    return $parent_key;
}

function ump_distibute_epin_commission($epin_data, $user_id){   
  $joincommission=distribute_join_commission($epin_data, $user_id);   
  $referralcommission = distribute_referral_commission($epin_data, $user_id);   
  $total_distributed_commission = $joincommission+$referralcommission;
  $total_left_amount = $epin_data->price - $total_distributed_commission;
  insert_left_amount($total_left_amount, $user_id);

}

function distribute_join_commission($epin_data='', $user_id='')
{
  $amount = 0;
  $connection=\Drupal::service('database');  
  $ump_join_commission = \Drupal::config('unilevelmlm.payout')->get('ump_join_commission');
  $ump_join_commission_type = \Drupal::config('unilevelmlm.payout')->get('ump_join_commission_type');
  if ($ump_join_commission_type == 'percent') {
    $join_commission_amount = ($epin_data->price * $ump_join_commission)/100;
  } else {
    $join_commission_amount = $ump_join_commission;
  }
  $query_user = $connection->select('ump_user', 'uu');
  $query_user->fields('uu');
  $query_user->condition('uu.user_id', $user_id);
  $row = $query_user->execute()->fetchObject();
  $sponsor_key = $row->sponsor_key;
  $user_id = $row->user_id;
  $current_date= date('Y-m-d h:i:s', \Drupal::time()->getCurrentTime());
  if ($sponsor_key != 0) {
    $sponsor_id = $sponsor_key;
    $amount = $join_commission_amount;      
        $query = $connection->insert('ump_commission');
        $query->fields([
          'date_notified'=>$current_date,
          'parent_id'=>$user_id,
          'child_id'=>$user_id,
          'amount'=>$join_commission_amount,
          'payout_id'=>'0',
          'comm_type'=>'1'
        ]);
        $query->execute();
    }
    return $amount;
}

function distribute_referral_commission($epin_data, $user_id)
{
  $amount = 0;
  $connection=\Drupal::service('database');  
  $ump_referral_commission = \Drupal::config('unilevelmlm.payout')->get('ump_referral_commission');
  $ump_referral_commission_type = \Drupal::config('unilevelmlm.payout')->get('ump_referral_commission_type');
  $current_date= date('Y-m-d h:i:s', \Drupal::time()->getCurrentTime());   
  if ($ump_referral_commission_type == 'percent') {
      $referral_commission_amount = ($epin_data->price * $ump_referral_commission) / 100;
    } else {
        $referral_commission_amount = $ump_referral_commission;
    }
    $query_user = $connection->select('ump_user', 'uu');
    $query_user->fields('uu');
    $query_user->condition('uu.user_id', $user_id);
    $row = $query_user->execute()->fetchObject();
    $sponsor_key = $row->sponsor_key;
    $child_id = $row->user_id;
    if ($sponsor_key != 0) {
        $sponsor_id = $sponsor_key;
        $amount += $referral_commission_amount;
        $query = $connection->insert('ump_commission');
        $query->fields([
          'date_notified'=>$current_date,
          'parent_id'=>$sponsor_id,
          'child_id'=>$user_id,
          'amount'=>$referral_commission_amount,
          'payout_id'=>'0',
          'comm_type'=>'3'
        ]);
        $query->execute();
    }
    return $amount;
} 

function insert_left_amount($left_amount, $user_id)
{
  $connection=\Drupal::service('database');

  $current_date= date('Y-m-d h:i:s', \Drupal::time()->getCurrentTime());
    $first_user_id = $connection->query("SELECT user_id FROM {ump_user} WHERE parent_key='0' AND sponsor_key='0' ")->fetchField();     
    $query_insert = $connection->insert('ump_commission');
    $query_insert->fields([
      'date_notified'=>$current_date,
      'parent_id'=>$first_user_id,
      'child_id'=>$user_id,
      'amount'=>$left_amount,
      'payout_id'=>'0',
      'comm_type'=>'5'
    ]);
    $query_insert->execute();
}

function unilevelmlm_theme($existing, $type, $theme, $path) {   
  return array(
      'unilevelmlm_template' => array(
          'variables' => array('ump_user_name' => NULL, 'join_commission' => NULL, 'referral_commision' => NULL, 'join_total_amount'=>NULL,'ref_total_amount'=>NULL)
        ),
        'unilevelmlm_payout_template' => array(
          'variables' => array('data_array' => NULL)
        ),
        'unilevelmlm_genealogy_template' => array(
          'variables' => array('genealogy' => NULL)
        ),
        'unilevelmlm_dashboard_template' => array(
          'variables' => array('dashboard_data' => NULL, 'menu_array'=>NULL,'current_user_name'=>NULL,'total_earning_week'=>NULL,'earning_week_percent'=>NULL, 'total_withdrawal'=>NULL,'withdrawal_week_percent'=>NULL,
          'total_downlines'=>NULL,'user_week_percent'=>NULL,'total_bonus'=>NULL,'bonus_week_percent'=>NULL,'logout'=>NULL, 'profile_image'=>NULL)
        ),
        'unilevelmlm_frontpayout_template' => array(
          'variables' => array('menu_array'=>NULL,'current_user_name'=>NULL,'payout_detail' => NULL,'logout'=>NULL, 'profile_image'=>NULL)
        ),
        'unilevelmlm_joincomm_template' => array(
          'variables' => array('menu_array'=>NULL,'current_user_name'=>NULL,'join_commission' => NULL,'logout'=>NULL, 'profile_image'=>NULL)
        ),
        'unilevelmlm_refcomm_template' => array(
          'variables' => array('menu_array'=>NULL,'current_user_name'=>NULL,'ref_commission' => NULL,'logout'=>NULL, 'profile_image'=>NULL)
        ),
        
        'unilevelmlm_personalinfo_template' => array(
          'variables' => array('menu_array'=>NULL,'current_user_name'=>NULL,'user_info' => NULL,'logout'=>NULL, 'profile_image'=>NULL)
        ),
        'unilevelmlm_bank_template' => array(
          'variables' => array('menu_array'=>NULL,'current_user_name'=>NULL,'user_bank_details' => NULL,'logout'=>NULL, 'profile_image'=>NULL)
        ),        
        'unilevelmlm_withdrawalamount_template' => array(
          'variables' => array('menu_array'=>NULL,'current_user_name'=>NULL,'total_amount'=>NULL,'processed_amount'=>NULL, 'pending_amount'=>NULL,'remaining_balance'=>NULL,'min_limit'=>NULL, 'max_limit'=>NULL,'logout'=>NULL, 'profile_image'=>NULL)
        ),
        'unilevelmlm_admin_transferamount_template' => array(
          'variables' => array('data'=>NULL,'user_name'=>NULL,'bank'=>NULL, 'withdrawal_history'=>NULL)
        ),
        'unilevelmlm_userreport_template' => array(
          'variables' => array('profile_info'=>NULL,'user_info'=>NULL,'email'=>NULL,'ump_phone'=>NULL,'join_commission'=>NULL,'referral_commission'=>NULL)
        ),
        'unilevelmlm_payoutreport_template' => array(
          'variables' => array('join_commission'=>NULL,'referral_commission'=>NULL)
        ),
         
        'unilevelmlm_frontgenealogy_template' => array(
          'variables' => array('menu_array'=>NULL,'current_user_name'=>NULL,'logout'=>NULL, 'profile_image'=>NULL)
        ),
        'unilevelmlm_umpjoin_template' => array(
          'variables' => array('ump_user'=>NULL,'user_login'=>NULL,'site_url'=>NULL,'logout'=>NULL, 'profile_image'=>NULL)
        ),        
  );
}
function get_all_ump_users(){
  $connection=\Drupal::service('database');   
  $query = $connection->select('ump_user', 'uu');
  $query->fields('uu', ['user_id']); 
  $all_users = $query->execute()->fetchAll();
  return $all_users;    

}
 

function ump_eligibility_check_for_commission($user_id)
{
    
  $connection=\Drupal::service('database');
  $ump_no_of_personal_referrals = \Drupal::config('unilevelmlm.eligibility')->get('ump_no_of_personal_referrals');
  $personal_referrers = 0;
  $query = $connection->select('ump_user', 'uu');
  $query->addExpression('COUNT(*)', 'count');
  $query->condition('uu.sponsor_key', $user_id);
  $query->condition('uu.payment_status', '1');
  $personal_referrers = $query->execute()->fetchField();

    if ($ump_no_of_personal_referrals>=0) {
        if ($personal_referrers >= $ump_no_of_personal_referrals) {
          return true;
        } else {
            return false;
        }
    } else {
        return false;
    }
}

function ump_user_join_commission_for_commission($user_id)
{
  $connection=\Drupal::service('database');
  $query = $connection->select('ump_commission', 'uc');
  $query->fields('uc');
  $query->condition('uc.parent_id', $user_id);
  $query->condition('uc.child_id', $user_id);
  $query->condition('uc.comm_type', '1');
  $query->condition('uc.payout_id', '0');
  $query->condition('uc.status', '0');
  $join_commission = $query->execute()->fetchAll();
  return (!empty($join_commission)) ? $join_commission : '0';
}
function ump_user_referral_commission_for_commission($user_id)
{
   
    $connection=\Drupal::service('database');
    $query = $connection->select('ump_commission', 'uc');
    $query->fields('uc');
    $query->condition('uc.parent_id', $user_id);
    $query->condition('uc.comm_type', '3');
    $query->condition('uc.payout_id', '0');
    $query->condition('uc.status', '0');
    $referral_commission = $query->execute()->fetchAll();
  
      return (!empty($referral_commission)) ? $referral_commission : '0';
  }
 

function ump_user_join_commission_for_commission_amount($user_id)
{
  $connection=\Drupal::service('database');
  $join_commission = $connection->query("SELECT sum(amount) FROM {ump_commission} WHERE parent_id=:parent_id AND child_id=:child_id AND comm_type=:comm_type AND payout_id=:payout_id AND status=:status",[':parent_id'=>$user_id,':child_id'=>$user_id,':comm_type'=>'1',':payout_id'=>'0',':status'=>'0'])->fetchField();
  return (!empty($join_commission)) ? $join_commission : '0';
}
function ump_user_referral_commission_for_commission_amount($user_id)
{
   
    $connection=\Drupal::service('database');
    $referral_commission = $connection->query("SELECT sum(amount) FROM {ump_commission} WHERE parent_id=:parent_id AND comm_type=:comm_type AND payout_id=:payout_id AND status=:status",[':parent_id'=>$user_id, ':comm_type'=>'3',':payout_id'=>'0',':status'=>'0'])->fetchField();
 
    return (!empty($referral_commission)) ? $referral_commission : '0';
}
 
function ump_get_child_user_name_by_id($user_id)
{
  $cache = \Drupal::cache();
  $cid = 'unilevelmlm:child_user_name:' . $user_id;
  if ($cache->get($cid)) {
    $user_name = $cache->get($cid)->data;
    return $user_name;
  }
  $connection=\Drupal::service('database');
  $query = $connection->select('users_field_data', 'ufd');
  $query->addField('ufd', 'name');
  $query->condition('ufd.uid', $user_id);
  $user_name = $query->execute()->fetchField();
  $cache->set($cid, $user_name, time() + (60 * 5));
    return $user_name;
}

function ump_distribute_commission_function()
{
    $connection=\Drupal::service('database');
    $all_users=get_all_ump_users();
    $totalamount = 0; 
    $json['status']=false;
    $ump_key='ump_distribution';
    $msg=t('Your Amount has been Distributed');
    $title=t('Distributed Amount');
    if ($all_users>0) {       
                
                foreach ($all_users as $key => $row) {
                  if(ump_eligibility_check_for_commission($row->user_id)) {                                        
                    $join_commission = ump_user_join_commission_for_commission_amount($row->user_id);
                    $referral_commission = ump_user_referral_commission_for_commission_amount($row->user_id);                                    
                    $totalamount = $join_commission + $referral_commission;                     
                    if (!empty($totalamount) && ($totalamount != 0)) {
                 
                        $query = $connection->update('ump_commission');
                        $query->fields([
                          'status'=>"1"
                          ]);
                        $query->condition('child_id',$row->user_id,'=');
                        $query->condition('payout_id','0','=');
                        $query->condition('comm_type','1','=');
                        $query->execute();
                         $query = $connection->update('ump_commission');
                         $query->fields([
                          'status'=>"1"
                          ]);
                          $query->condition('parent_id',$row->user_id,'=');
                          $query->condition('payout_id','0','=');
                          $query->condition('comm_type','3','=');
                          $query->execute();
                        }
                      }                  
                      $json['status']=true;
                      $json['message']=t("Commission is Successfully Ditributed");
                }     
          }
          echo json_encode($json);die;
  }
function ump_user_join_commission_for_payout($user_id)
{
    $connection=\Drupal::service('database');
    $query = $connection->select('ump_commission', 'uc');
    $query->addExpression('SUM(amount)', 'total');
    $query->condition('uc.parent_id', $user_id);
    $query->condition('uc.comm_type', '1');
    $query->condition('uc.payout_id', '0');
    $query->condition('uc.status', '1');
    $join_commission = $query->execute()->fetchField();
    return ($join_commission > 0) ? $join_commission : '0';
}
function ump_user_referral_commission_for_payout($user_id)
{
    $connection=\Drupal::service('database');
    $referral_commission = $connection->query("SELECT SUM(amount) as total FROM {ump_commission} WHERE parent_id='".$user_id."' AND comm_type='3' AND payout_id='0' AND status='1'")->fetchField();
    return ($referral_commission > 0) ? $referral_commission : '0';
}
 
function ump_user_left_amount_for_payout($user_id)
{
    $connection=\Drupal::service('database');
    $left_amount = $connection->query("SELECT SUM(amount) as total FROM {ump_commission} WHERE child_id='".$user_id."' AND comm_type='5' AND payout_id='0' AND status='1'")->fetchField();
    return ($left_amount > 0) ? $left_amount : '0';
}
 
function ump_run_payout_display_functions()
{
  $connection=\Drupal::service('database');     
  $all_users=get_all_ump_users();
    // $results = $wpdb->get_results($sql);
    $ump_service = \Drupal::config('unilevelmlm.payout');
    $totalamount = 0;
    $displayDataArray = array();
    $join_commission = 0;     
    $referral_commission = 0;    
    $level_commission = 0;    
    if ($all_users) {
        $i = 0;
        
        foreach ($all_users as $key => $row) {
          if (ump_eligibility_check_for_commission($row->user_id)) {                
            $join_commission = ump_user_join_commission_for_payout($row->user_id);                
            $referral_commission = ump_user_referral_commission_for_payout($row->user_id);                              
            $left_amount = ump_user_left_amount_for_payout($row->user_id);            
            $totalamount = $join_commission + $referral_commission;                            
            if ($totalamount != 0) {                                               
                        $displayDataArray[$key]['id'] = $row->user_id;
                        $displayDataArray[$key]['username'] = ump_get_child_user_name_by_id($row->user_id);                        
                        $displayDataArray[$key]['join_commission'] = $join_commission;
                        $displayDataArray[$key]['direct_refferal_commission'] = $referral_commission;                                                
                        $displayDataArray[$key]['total_commission'] = number_format($totalamount,2);                        
                        $displayDataArray[$key]['net_amount'] = $totalamount;
                        $i++;
                    }
                }
            }       
    }

    return $displayDataArray;
}

function ump_run_payout_functions()
{
  $connection=\Drupal::service('database');
  $payout_data = ump_run_payout_display_functions();    
  $payout_settings = \Drupal::config('unilevelmlm.payout');
  $json['status']=false;
   
  if(!empty($payout_data)) {
   
    $current_date= date('Y-m-d', \Drupal::time()->getCurrentTime());                
                    
    foreach($payout_data as $key => $pay) {      
      if (ump_eligibility_check_for_commission($pay['id'])) {
              $query = $connection->insert('ump_payout');
              $query->fields([
                'user_id' => $pay['id'],
                'join_commission_amount' => $pay['join_commission'],
                'referral_commission_amount' => $pay['direct_refferal_commission'],
                'level_commission_amount' => '0',
                'total_commission' => $pay['total_commission'],
                'total_amount' => $pay['net_amount'],
                'regular_bonus_amount' => '0',
                'deduction_amount' => '0',
                'date' => $current_date,
              ]);
              $payout_id = $query->execute();

                if (!empty($payout_id) && $payout_id > 0) {

                      $query = $connection->update('ump_commission');
                        $query->fields([
                          'payout_id'=>$payout_id,
                          'status'=>'2'
                          ]);
                          $query->condition('parent_id', $pay['id'],'=');
                          $query->condition('payout_id','0','=');
                          $query->condition('comm_type','1','=');
                          $query->condition('status','1','=');
                          $query->execute();

                        $query = $connection->update('ump_commission');
                        $query->fields([
                          'payout_id'=>$payout_id,
                          'status'=>'2'
                          ]);
                          $query->condition('parent_id', $pay['id'],'=');
                          $query->condition('payout_id','0','=');
                          $query->condition('comm_type','3','=');
                          $query->condition('status','1','=');
                          $query->execute();
                }
                 
              }
            }         
            $json['status']=true;
            $json['message']="Payout is Run";    
          } 
          // ump_send_payout_mail(43,20);
    return $json;
} 
function get_site_url(){
  return Url::fromRoute('<front>', [], ['absolute' => TRUE])->toString(); 
}
function get_menu_array()
{
  $cache = \Drupal::cache();
  $cid = 'unilevelmlm:menu_array';
  if ($cache->get($cid)) {
    $data_menu_array = $cache->get($cid)->data;
    return $data_menu_array;
  }
    // $general_settings = get_option('ump_general_settings');
    // $variable = get_option('ump_manage_variable');

    $menu_array = array();
    $menu_subarray = array();
    $menu_array[] = array(
        'icon' => 'fas fa-tachometer-alt',
        'title' => t('Dashboard'),
        'link' =>  get_site_url().'ump-dashboard',
    );
     
    // $menu_subarray['epin'][] = array(
    //   'title' =>   'Requested Epins' ,
    //   'link' =>  get_site_url().'requested_epin',
    // );
    // $menu_subarray['epin'][] = array(
    //   'title' =>  'Recieved Epins' ,
    //   'link' => get_site_url().'received_epin',
    // );
    // $menu_array[] = array(
    //     'icon' => 'fas fa-key',
    //     'title' =>  'Epins' ,
    //     'link' =>  get_site_url().'my_epin',
    //     'id' => 'my_epins',
    //     'sub_list' => $menu_subarray['epin'],
    // );
    $menu_array[] = array(
        'icon' => 'fas fa-network-wired',
        'title' => t('Genealogy'),
        'link' => get_site_url().'ump-downlines',
    );

    $menu_subarray['payout'][] = array(
        'title' => t('Payout List'),
        'link' =>  get_site_url().'payout-details',
    );
    
    $menu_subarray['payout'][] = array(
        'title' => t('Join Commission'),
        'link' => get_site_url().'join-commission',
    );
     
    $menu_subarray['payout'][] = array(
        'title' =>  t('Referral Commission'),
        'link' =>   get_site_url().'ref-commission'
    );
    $admin_menus = \Drupal::entityTypeManager()->getStorage('menu_link_content')->loadByProperties(array('menu_name' => 'unilevelmlm-menu'));
  $data_menu_array=array();
  foreach ($admin_menus as $admin_menu) {
    $data_menu_array[]=array('title'=>$admin_menu->title->value,'url'=>$admin_menu->link->uri);
  }
  $cache->set($cid, $data_menu_array, time() + (60 * 60));
  return  $data_menu_array;
}
    $menu_array[] = array(
        'icon' => 'fas fa-money-bill-wave',
        'title' => t('Payout Details'),
        'id' => 'payout_details',
        'link' =>  get_site_url().'payout-details',
        'sub_list' => $menu_subarray['payout'],
    );

    $menu_array[] = array(
        'icon' => 'fas fa-user',
        'title' => t('Acount Details'),
        'id' => 'account_details',
        'link' => '',
        'sub_list' => array(
            array(
                'title'=> t('Personal Info'),
                'link' => get_site_url().'account-detail'
            ),
            array(
                'title' => t('Bank Details'),
                'link' =>  get_site_url().'bank-detail'
            ),
        )
    );
    $menu_array[] = array(
        'icon' => 'fas fa-piggy-bank',
        'title' => t('Withdraw Amount'),
        'link' =>  get_site_url().'withdrawal-amount',
    );
    // $menu_array[] = array(
    //     'icon' => 'fas fa-sign-in-alt',
    //     'title' => 'Registeration',
    //     'link' =>   get_site_url().'ump_registration',
    // );
    // $menu_array[] = array(
    //     'icon' = 'fas fa-thumbs-up',
    //     'title' = __('Join Us',
    //     'link' = get_url_ump('join-network'),
    // );


    $menu_array[] = array(
        'icon' => 'fas fa-sign-out-alt',
        'title' => t('Log-out'),
        'link' =>  get_site_url().'user/logout',
    );
    return $menu_array;
}
// function ump_price_card($price, $type = NULL)
// {    
//   $connection=\Drupal::service('database');
//   $currency = $connection->query("SELECT * FROM {ump_currency} WHERE iso3='".$setting['ump_currency']."'")->fetchObject();
//   if (!empty($currency->symbol)) {
//       return $currency->symbol.$price;
//   } else {
//       return $price ;
//   }     
// }
function get_currency_data(){
  $connection=\Drupal::service('database');
  $query = $connection->select('ump_currency', 'uc');
  $query->addField('uc', 'iso3');
  return $query->execute()->fetchAll();
}
function get_total_earning($user_id){
  $cache = \Drupal::cache();
  $cid = 'unilevelmlm:total_earning:' . $user_id;
  if ($cache->get($cid)) {
    $total_earning = $cache->get($cid)->data;
    return $total_earning;
  }
  $connection=\Drupal::service('database');
  $total_earning = $connection->query("SELECT SUM(total_amount) FROM {ump_payout} WHERE user_id=:user_id",[':user_id'=>$user_id])->fetchField();
  $cache->set($cid, $total_earning, time() + (60 * 5));
  return $total_earning;
}
function get_total_earing_week($user_id){
  $cache = \Drupal::cache();
  $cid = 'unilevelmlm:total_earing_week:' . $user_id;
  if ($cache->get($cid)) {
    $total_earing_week = $cache->get($cid)->data;
    return $total_earing_week;
  }
  $connection=\Drupal::service('database');
  $query = $connection->select('ump_payout', 'up');
  $query->addExpression('SUM(total_amount)', 'total');
  $query->condition('user_id', $user_id);
  $query->where('DATE(date) > (NOW() - INTERVAL 7 DAY)');
  $total_earing_week = $query->execute()->fetchField();
  $cache->set($cid, $total_earing_week, time() + (60 * 5));
  return $total_earing_week;
}

function get_withdrawa_amount_earning($user_id){
  $cache = \Drupal::cache();
  $cid = 'unilevelmlm:withdrawa_amount_earning:' . $user_id;
  if ($cached = $cache->get($cid)) {
    $withdrawa_amount_earning = $cached->data;
    return $withdrawa_amount_earning;
  }
  $connection=\Drupal::service('database');
  $withdrawa_amount_earning = $connection->query("SELECT SUM(amount) FROM {ump_withdrawal} WHERE user_id=:user_id",[':user_id'=>$user_id])->fetchField();
  $cache->set($cid, $withdrawa_amount_earning, time() + (60 * 5));
  return $withdrawa_amount_earning;
}
function get_withdrawa_week($user_id){
  $connection=\Drupal::service('database');
  return $connection->query("SELECT SUM(amount) FROM {ump_withdrawal} WHERE user_id='".$user_id."' AND DATE(withdrawal_initiated_date) > (NOW() - INTERVAL 7 DAY)")->fetchField();
}

function get_total_downlines($user_id){
  $cache = \Drupal::cache();
  $cid = 'unilevelmlm:total_downlines:' . $user_id;
  if ($cached = $cache->get($cid)) {
    $total_downlines = $cached->data;
    return $total_downlines;
  }
  $connection=\Drupal::service('database');
  $total_downlines = $connection->query("SELECT COUNT(*) FROM {ump_user} WHERE sponsor_key=:user_id",[':user_id'=>$user_id])->fetchField();
  $cache->set($cid, $total_downlines, time() + (60 * 5));
  return $total_downlines;
}
function get_total_downlines_week($user_id){
  $cache = \Drupal::cache();
  $cid = 'unilevelmlm:total_downlines_week:' . $user_id;
  if ($cached = $cache->get($cid)) {
    $total_downlines_week = $cached->data;
    return $total_downlines_week;
  }
  $connection=\Drupal::service('database');
  $total_downlines_week = $connection->query("SELECT COUNT(*) FROM {ump_user} WHERE sponsor_key='".$user_id."' AND DATE(payment_date) > (NOW() - INTERVAL 7 DAY)")->fetchField();
  $cache->set($cid, $total_downlines_week, time() + (60 * 5));
  return $total_downlines_week;
}

function ump_get_earning_detail_for_graph($user_id)
{
    $connection=\Drupal::service('database');     
    $jsonern = '';     
    $months_array = array('0' => 1, '1' => 2, '2' => 3, '3' => 4, '4' => 5, '5' => 6, '6' => 7, '7' => 8, '8' => 9, '9' => 10, '10' => 11, '11' => 12);
    $Year_array = date('Y');
    $data = array();
    $earningdata = array();
    foreach ($months_array as $key => $months_array) {

        $earning = $connection->query("SELECT SUM(total_amount) FROM {ump_payout} WHERE user_id='" . $user_id . "' AND MONTH(date)='" . $months_array . "' AND YEAR(date)='" . $Year_array . "'")->fetchField();

        $earningdata[] = array($months_array, ($earning) ? $earning : 0);
    }
    foreach ($earningdata as $key => $value) {
        //echo date("l F Y h:i:s");
        if ($key == 11) {
            $jsonern .= '"' . $key . '":"' . $value[1] . '"';
        } else {
            $jsonern .= '"' . $key . '":"' . $value[1] . '",';
        }
    }
    return $jsonern;
}

function ump_get_donugt_data($user_id)
{
  $connection=\Drupal::service('database');    
    $payout = $connection->query("SELECT SUM(total_commission) as total ,SUM(referral_commission_amount) as ref_commission , SUM(join_commission_amount) as join_commission ,SUM(level_commission_amount) as level_commission, SUM(regular_bonus_amount) as ragular_bonus FROM {ump_payout} WHERE user_id='".$user_id."'")->fetchObject();
    if (!empty($payout->total)) {
        $payout->ref_commission =  (100 * $payout->ref_commission) / $payout->total;
        $payout->join_commission =(100 * $payout->join_commission) / $payout->total;
        $payout->level_commission =(100 * $payout->level_commission) / $payout->total;
        $payout->ragular_bonus =(100 * $payout->ragular_bonus) / $payout->total;         
    }
    return $payout;
}

function ump_get_payout_detail_list($user_id)
{
  $connection=\Drupal::service('database');
  return $connection->query("SELECT * FROM {ump_payout} WHERE user_id=:user_id",[':user_id'=>$user_id])->fetchAll();
}
function ump_first_user_created()
{
  $connection=\Drupal::service('database');
    $user_count = $connection->query("SELECT count(*) FROM {ump_user}")->fetchField();
    if ($user_count > 0) {
        return true;
    } else {
        return false;
    }
}
function ump_get_user_add_link($user_id)
{
    $link = get_site_url().'ump_registration_by_referral/'.$user_id;
     return $link;
}
function get_data_for_dv($user_id = NULL)
{
  $connection=\Drupal::service('database');
  $current_user=\Drupal::currentUser();
    $members = array();
    // $general_settings = get_option('ump_general_settings', true);
    // $depth = (isset($general_settings['ump_no_of_levels'])) ? $general_settings['ump_no_of_levels'] : 5;
    $roles = \Drupal::currentUser()->getRoles();
    $childs_array = array();
    $is_admin = false;
     
    if(ump_first_user_created()){
      
      
      if (in_array('administrator', $roles)) {
        
        $is_admin = true;
        
            $root_user = $connection->query("SELECT * FROM {ump_user} where parent_key=:parent_key",[':parent_key'=>'0'])->fetchObject();
            $n_row = 0;
            $earning = $connection->query("SELECT SUM(total_commission) FROM {ump_payout} WHERE user_id=:user_id",[':user_id'=>$root_user->user_id])->fetchField();
            $downlines = $connection->query("SELECT COUNT(*) FROM {ump_user} WHERE sponsor_key=:user_id",[':user_id'=>$root_user->user_id])->fetchField();
            $total_with = $connection->query("SELECT SUM(amount) FROM  {ump_withdrawal} WHERE user_id=:user_id",[':user_id'=>$root_user->user_id])->fetchField();


          } else if (in_array('ump_user', $roles)) {
            $is_admin = false;
            if (!empty($user_id)) {                 
              $root_user = $connection->query("SELECT * FROM {ump_user} where user_id=:user_id",[':user_id'=>$user_id])->fetchObject();
              $n_row = (!empty($root_user->n_row))?$root_user->n_row:0;             
              
              } else {  
                $user_id = \Drupal::currentUser()->id();               
                $root_user = $wpdb->query("SELECT * FROM {ump_user} where user_id=:user_id",[':user_id'=>$user_id])->fetchObject();
                $n_row = (!empty($root_user->n_row))?$root_user->n_row:0;
              }
              // $n_row = $n_row + $depth;
              // $n_row = $n_row ;              
              $parent = ump_get_child_user_name_by_id($root_user->parent_key);
              $sponsor = ump_get_child_user_name_by_id($root_user->sponsor_key);
              $earning = $connection->query("SELECT SUM(total_commission) FROM {ump_payout} WHERE user_id=:user_id",[':user_id'=>$root_user->user_id])->fetchField();
              $downlines = $connection->query("SELECT COUNT(*) FROM {ump_user} WHERE sponsor_key=:user_id",[':user_id'=>$root_user->user_id])->fetchField();
              $total_with = $connection->query("SELECT SUM(amount) FROM  {ump_withdrawal} WHERE user_id=:user_id",[':user_id'=>$root_user->user_id])->fetchField();
              $link = ump_get_user_add_link($root_user->user_id);
            }else{
              $root_user = $connection->query("SELECT * FROM {ump_user} where parent_key=:parent_key",[':parent_key'=>'0'])->fetchObject();
              $n_row = 0;
              $earning = $connection->query("SELECT SUM(total_commission) FROM {ump_payout} WHERE user_id=:user_id",[':user_id'=>$root_user->user_id])->fetchField();
              $downlines = $connection->query("SELECT COUNT(*) FROM {ump_user} WHERE sponsor_key=:user_id",[':user_id'=>$root_user->user_id])->fetchField();
              $total_with = $connection->query("SELECT SUM(amount) FROM  {ump_withdrawal} WHERE user_id=:user_id",[':user_id'=>$root_user->user_id])->fetchField();
              $sponsor = ump_get_child_user_name_by_id($root_user->sponsor_key);
              $link = ump_get_user_add_link($root_user->user_id);
            }
             
            $members = array(
              "id" => $root_user->user_id,
              "name" => ump_get_child_user_name_by_id($root_user->user_id),
              "imageUrl" =>ump_get_profile_picture($root_user->user_id),
              "userData" => array(
                'sponsor'       => (!empty($sponsor)) ? ucwords($sponsor) : t('No'),
                'parent'        => (!empty($parent)) ? ucwords( $parent) : t('No'),
                'level'         => ($root_user->level),
                'total_earning' => ($earning) ? str_replace('.00', '', round($earning)) . '<sup>+</sup>' : 0,
                'total_widral'  => ($total_with) ? str_replace('.00', '',  round($total_with)) . '<sup>+</sup>' :  0,
                'downlines'     =>  $downlines,
                'is_admin'      =>  $is_admin,
                'link'      =>  (!empty($link))?$link:'',
                
              ),               
              "profileUrl" =>'user_report/'.$root_user->user_id,
              "isLoggedUser" => ($current_user->id() == $root_user->user_id) ? true : false,
              "userKey" => $root_user->user_id,
              "children" => ump_get_childs_data_FOR_DV($root_user->user_id, $n_row, $is_admin)
            );
            
            return $members;
          }  
        }
        
         
function ump_get_childs_data_FOR_DV($user_id, $n_row, $is_admin = NULL)
{
  $connection=\Drupal::service('database');
  $childs_array = array();
  $user_childs = $connection->query("SELECT * FROM {ump_user} where parent_key=:user_id AND n_row >= :n_row", [':user_id'=>$user_id, ':n_row'=>$n_row])->fetchAll();
   
  if (empty($user_childs)) {
    $childs_array = '';
  } else {
    foreach ($user_childs as $keys => $child) {
      $earning = $connection->query("SELECT SUM(total_commission) FROM {ump_payout} WHERE user_id=:user_id",[':user_id'=>$child->user_id])->fetchField();
            $downlines = $connection->query("SELECT COUNT(*) FROM {ump_user} WHERE sponsor_key=:user_id",[':user_id'=>$child->user_id])->fetchField();
            $total_with = $connection->query("SELECT SUM(amount) FROM  {ump_withdrawal} WHERE user_id=:user_id",[':user_id'=>$child->user_id])->fetchField();
            $parent = ump_get_child_user_name_by_id($child->parent_key);
            
            $sponsor = ump_get_child_user_name_by_id($child->sponsor_key);
            $link = ump_get_user_add_link($child->user_id);            
            // $viewlink = (isset($current_user->caps['administrator']) && $current_user->caps['administrator'] == 1) ? 'admin.php?page=ump-user-geonology&user_id=' . $child->user_id : '?user_id=' . $child->user_id;
          
            $childs_array[$keys] = array(
                "id" => $child->user_id,
                "name" => ump_get_child_user_name_by_id($child->user_id),
                "imageUrl" =>ump_get_profile_picture($child->user_id),
                "userData" => array(
                    'sponsor'       => (!empty($sponsor)) ? ucwords($sponsor) : t('No'),
                    'parent'        => (!empty($parent)) ? ucwords( $parent) : t('No'),
                    'level'         => ($child->level),
                    'total_earning' => ($earning) ? str_replace('.00', '', round($earning)) . '<sup>+</sup>' : 0,
                    'total_widral'  => ($total_with) ? str_replace('.00', '', round($total_with)) . '<sup>+</sup>' : 0,
                    'downlines'     =>  $downlines,
                    'is_admin'      =>  $is_admin,
                    'link'          =>  $link,
                    'view_more_link'          =>'',
                    // 'view_more_status'          => ($n_row == $child->n_row) ? '1' : '0',
                    // $viewlink                    
                ),
                "profileUrl" => 'user_report/'.$child->user_id,
                "isLoggedUser" => false,
                "userKey" => $child->user_id,
                "children" => ump_get_childs_data_FOR_DV($child->user_id, $n_row, $is_admin)
            );
        }
    }

    return $childs_array;
}

function ump_join_commission_by_userId($user_id)
{
    $connection=\Drupal::service('database');
    $user_payout_data = $connection->query("SELECT * FROM {ump_commission} WHERE child_id=:user_id AND comm_type=:comm_type",[':user_id'=>$user_id,':comm_type'=>'1'])->fetchAll();
    return $user_payout_data;
}

function ump_ref_commission_by_userId($user_id)
{
    $connection=\Drupal::service('database');
    $user_payout_data = $connection->query("SELECT * FROM {ump_commission} WHERE parent_id=:user_id AND comm_type=:comm_type",[':user_id'=>$user_id,':comm_type'=>'3'])->fetchAll();
    return $user_payout_data;
}

 
 
function ump_join_commission_by_payoutId($payout_id)
{
    $connection=\Drupal::service('database');
    $user_payout_data = $connection->query("SELECT * FROM {ump_commission} WHERE payout_id=:payout_id AND comm_type=:comm_type",[':payout_id'=>$payout_id,':comm_type'=>'1'])->fetchAll();
    return $user_payout_data;
}

function ump_ref_commission_by_payoutId($payout_id)
{
    $connection=\Drupal::service('database');
    $user_payout_data = $connection->query("SELECT * FROM {ump_commission} WHERE payout_id=:payout_id AND comm_type=:comm_type",[':payout_id'=>$payout_id,':comm_type'=>'3'])->fetchAll();
    return $user_payout_data;
}

 

function ump_getUserInfoByuserid($user_id)
{
  $connection=\Drupal::service('database');    
    $user = $connection->query("SELECT * FROM {ump_user} WHERE user_id=:user_id",[':user_id'=>$user_id])->fetchAll();

    return $user;
}

function ump_user_data_by_user_id($user_id)
{
  $connection=\Drupal::service('database');    
    $user = $connection->query("SELECT * FROM {ump_user} WHERE user_id=:user_id",[':user_id'=>$user_id])->fetchObject();

    return $user;
}

function ump_get_user_bank_details($user_id)
{
  $connection=\Drupal::service('database');    
  $account_details = $connection->query("SELECT * FROM {ump_account_detail} WHERE user_id=:user_id",[':user_id'=>$user_id])->fetchObject();
  return $account_details;
}
function insert_user_bank_details($account_holder_name='',$account_number='',$bank_name='',$branch='',$ifsc_code='', $contact_no='', $user_id=''){
  $connection=\Drupal::service('database');
  $json = array();
  $error = array();
  if (empty($account_holder_name)) {
    $error['err_account_holder_name'] = 'Please enter account holder name';
  }
  if (empty($account_number)) {
      $error['err_account_number'] = 'Please enter account number';
  }
  if (empty($bank_name)) {
      $error['err_bank_name'] = 'Please enter bank name';
  }
  if (empty($branch)) {
      $error['err_branch'] = 'Please enter Branch name';
  }
  if (empty($ifsc_code)) {
      $error['err_ifsc_code'] = 'Please enter IFSC Code';
  } 
  if (empty($contact_no)) {
      $error['err_contact_number'] = 'Please Contact Number Code';
  } 

    if (empty($error)) {
      $num_deleted = $connection->delete('ump_account_detail')
      ->condition('user_id', $user_id)
      ->execute();      
      $result = $connection->insert('ump_account_detail')
      ->fields([
        'user_id' => $user_id,
        'account_holder' => $account_holder_name,
        'account_number'=>$account_number,
        'bank_name'=>$bank_name,
        'branch'=>$branch,
        'ifsc_code'=>$ifsc_code,
        'contact_no'=>$contact_no         
        ])
        ->execute();
        
      $json['status'] = true;
      $json['message'] = 'Your account has been updated successfully';
      echo json_encode($json);
      die();
  } else {
      $json['status'] = false;
      $json['error'] = $error;
      echo json_encode($json);
      die();
  }
}

function get_user_total_amount($user_id=''){
  $connection=\Drupal::service('database');
  return $connection->query("SELECT SUM(total_amount) AS total FROM {ump_payout} WHERE user_id=:user_id",[':user_id'=>$user_id])->fetchField();
   
}

function get_user_processed_amount($user_id=''){
  $connection=\Drupal::service('database');
  return $connection->query("SELECT SUM(amount) FROM {ump_withdrawal} WHERE user_id=:user_id AND payment_processed=:payment_processed",[':user_id'=>$user_id,'payment_processed'=>'1'])->fetchField();
   
}

function get_user_pending_amount($user_id=''){  
  $connection=\Drupal::service('database');
  return $connection->query("SELECT SUM(amount) AS total FROM {ump_withdrawal} WHERE user_id=:user_id AND payment_processed=:payment_processed",[':user_id'=>$user_id,'payment_processed'=>'0'])->fetchField();
   
}
function get_user_requested_amount($user_id=''){  
  $connection=\Drupal::service('database');
  return $connection->query("SELECT SUM(amount) AS total FROM {ump_withdrawal} WHERE user_id=:user_id",[':user_id'=>$user_id])->fetchField();
   
}

function get_email_by_user_id($user_id=''){  
  $connection=\Drupal::service('database');
  return $connection->query("SELECT mail FROM {users_field_data} WHERE uid=:user_id",[':user_id'=>$user_id])->fetchField();
   
}

function get_user_info_by_user_id($user_id=''){  
  $connection=\Drupal::service('database');
  return $connection->query("SELECT * FROM {users_field_data} WHERE uid=:user_id",[':user_id'=>$user_id])->fetchObject();
   
}
function ump_user_bank_detail($user_id)
{
  $connection=\Drupal::service('database');
  return $connection->query("SELECT * FROM {ump_account_detail} WHERE user_id=:user_id",[':user_id'=>$user_id])->fetchObject();
}
function ump_withdrwal_amount_request_function($amount, $user_id)
{   
    $jsonarray = array();
    $withdrawal_min_limit = \Drupal::config('unilevelmlm.general')->get('ump_withdrawal_min_limit');
    $withdrawal_max_limit = \Drupal::config('unilevelmlm.general')->get('ump_withdrawal_max_limit');    
    $bank_confirm = ump_user_bank_detail($user_id);     
    $total_balance = get_user_total_amount($user_id);
    $requested_amount = get_user_requested_amount($user_id);
    $remaining_balance = $total_balance - $requested_amount;     
    if (empty($amount)) {
        $jsonarray['message'] = t("Sorry  Amount could not be empty");
        $jsonarray['status'] = false;
        echo json_encode($jsonarray);
        die();
    }
    if (($amount <= $withdrawal_max_limit && $amount >= $withdrawal_min_limit && $amount <= $remaining_balance) && !empty($bank_confirm)) {        
        ump_withdrawal_process($amount, $user_id);
        $jsonarray['message'] = t("your request is submitted successfully");
        $jsonarray['status'] = true;
        ump_payment_processed_mail($user_id, $requested_amount, $remaining_balance, $total_balance, $amount);         
    } else if (empty($bank_confirm)) {
        $jsonarray['message'] = t("Sorry Please update Your Bank Details");
        $jsonarray['status'] = false;
    } else {
        $jsonarray['message'] = t("Sorry you entered wrong Amount please check");
        $jsonarray['status'] = false;
    }
    echo json_encode($jsonarray);
    die();
}
function ump_withdrawal_process($amount, $user_id)
{
    $connection=\Drupal::service('database');
    $comment = 'Withdrawal Request Initiated';
    $current_date= date('Y-m-d h:i:s', \Drupal::time()->getCurrentTime());        
    $connection->insert('ump_withdrawal')->fields([
      'user_id'=>$user_id,
      'amount'=>$amount,
      'withdrawal_initiated'=>'1',
      'withdrawal_initiated_comment'=>$comment,
      'withdrawal_initiated_date'=>$current_date,
      'payment_processed'=>'0'
    ])->execute();
}

function get_withdrawal_detail_by_wid($user_id='', $id='')
{
  $connection=\Drupal::service('database');
  $withdrawals = $connection->query("SELECT * FROM {ump_withdrawal} WHERE user_id=:user_id AND id=:id",[':user_id'=>$user_id,':id'=>$id])->fetchObject();
  return $withdrawals;
}

function ump_get_withdrawal_history($user_id)
{
  $connection=\Drupal::service('database');
  $withdrawal_history = $connection->query("SELECT * FROM {ump_withdrawal} WHERE user_id=:user_id",[':user_id'=>$user_id])->fetchAll();
  return $withdrawal_history;   
}

function ump_withdrwal_request_approve_function($amount='',$user_id='',$id='',$ump_withdrawal_payment_mode='',$ump_withdrawal_transaction_id='')
{
     
    $connection=\Drupal::service('database'); 
    $error = array();  
    $ump_key='ump_admin_transfer_amount';
    $title=t('Transfer Amount') ;
    $msg=t('Your Amount will be transfer Successfully and your transfer amount is '.$amount);
    $withdrawal = get_withdrawal_detail_by_wid($user_id, $id);
    $bank = ump_user_bank_detail($user_id);
    $bank_name = $bank->bank_name;
    $bank_account = $bank->account_number; 

    $current_date= date('Y-m-d h:i:s', \Drupal::time()->getCurrentTime());   
    if (empty($amount)) {
        $error['status'] = false;
        $error['error'] = t('Request amount is incorrect please check!!');
    }
    if ($amount !== $withdrawal->amount) {
        $error['status'] = false;
        $error['error'] = t('Request amount is incorrect please check!!');
    }
    if (empty($ump_withdrawal_payment_mode)) {
        $error['status'] = false;
        $error['error'] = t('Please choose payment Mode!!');
    }
    if (empty($ump_withdrawal_transaction_id)) {
        $error['status'] = false;
        $error['error'] = t("Transaction Id can't be Blank!!");
    }
    if (empty($error)) {         
        $connection->update('ump_withdrawal')->fields([
        'transaction_id'=>$ump_withdrawal_transaction_id,
        'payment_mode'=>$ump_withdrawal_payment_mode,
        'user_bank_name'=>$bank_name,
        'user_bank_account_no'=>$bank_account,
        'payment_processed'=>'1',
        'payment_processed_date'=>$current_date
        ])->condition('user_id',$user_id,'=')
        ->condition('id',$id,'=')
        ->execute(); 
        $error['status'] = true;
        $error['message'] = t("Payment successfully updated!!!");
        $total_balance = get_user_total_amount($user_id);
        $requested_amount = get_user_requested_amount($user_id);
        $remaining_balance = $total_balance - $requested_amount;
        ump_withdrwal_pay($user_id, $id, $amount, $remaining_balance);      
      }
      echo json_encode($error);       
    die();
}

function ump_get_profile_picture($user_id)
{
  $cache = \Drupal::cache();
  $cid = 'unilevelmlm:profile_picture:' . $user_id;
  if ($cache->get($cid)) {
    $user_image = $cache->get($cid)->data;
    return $user_image;
  }
  $connection=\Drupal::service('database');
  $status =$connection->query("SELECT payment_status FROM {ump_user} WHERE user_id=:user_id",[':user_id'=>$user_id])->fetchField();
  $module_handler = \Drupal::service('module_handler');
  $module_path = $module_handler->getModule('unilevelmlm')->getPath();
  $user = \Drupal\user\Entity\User::load($user_id);
  if(!empty($user)){
    if (!$user->user_picture->isEmpty()) {
      $displayImg = file_create_url($user->user_picture->entity->getFileUri());
	  $cache->set($cid, $displayImg, time() + (60 * 5));
      return $displayImg;
    }else{
      
      if ($status == 1) {
		  $user_image = $GLOBALS['base_url']. '/'.$module_path.'/images/user_paid.png';
		  $cache->set($cid, $user_image, time() + (60 * 5));
          return  $user_image;
      } else {
		  $user_image = $GLOBALS['base_url']. '/'.$module_path.'/images/user.png';
		  $cache->set($cid, $user_image, time() + (60 * 5));
          return   $user_image;
      }
    }
  }else{
	  $cache->set($cid, '', time() + (60 * 5));
    return '';
  }
}

function get_user_downlines($user_id)
{ 
  $connection=\Drupal::service('database');    
  return $connection->query("SELECT count(*) FROM {ump_user} WHERE sponsor_key=:user_id",[':user_id'=>$user_id])->fetchField();
        
}

function get_user_info($user_id)
{ 
  $connection=\Drupal::service('database');    
  return $connection->query("SELECT * FROM {ump_user} WHERE user_id=:user_id",[':user_id'=>$user_id])->fetchObject();
        
}
function get_phone_number($user_id)
{ 
  $connection=\Drupal::service('database');    
  return $connection->query("SELECT contact_no FROM {ump_account_detail} WHERE user_id=:user_id",[':user_id'=>$user_id])->fetchField();
        
}
function get_user_email_by_id($user_id)
{ 
  $connection=\Drupal::service('database');    
  return $connection->query("SELECT mail FROM {users_field_data} WHERE uid=:user_id",[':user_id'=>$user_id])->fetchField();        
} 
function get_usable_data($ump_commission=array()){
    $i=0;
    $commission=[];
    foreach($ump_commission as $value){
      $commission[$i]->id=$value->id;
      $commission[$i]->parent_name=ump_get_child_user_name_by_id($value->parent_id);
      $commission[$i]->child_name=ump_get_child_user_name_by_id($value->child_id);
      $commission[$i]->amount=$value->amount;
      $commission[$i]->date_notified=date_format(date_create($value->date_notified), ' jS M Y');
      $i++;
    }
    return $commission;  
}
 
function ump_calculate_bonus_commission($user_id)
{

    $connection=\Drupal::service('database');  
    //get the eligibility for bonus

    $ump_bonuses = \Drupal::config('unilevelmlm.regularbonus');
    $ump_no_of_row_bonus = \Drupal::config('unilevelmlm.general')->get('ump_no_of_row_of_bonus');
     
    $returndata = array();
    //count total direct referrals
    $bonus_members = $connection->query("SELECT COUNT(*) AS bonus_members  FROM {ump_user} WHERE sponsor_key =:user_id AND payment_status=:payment_status",[':user_id'=>$user_id,':payment_status'=>'1'])->fetchField();
    $parent_id = $user_id;
    $get_max_id = $connection->query("SELECT MAX(direct_referrals) FROM {ump_bonus} WHERE parent_id =:parent_id AND bonus_type=:bonus_type",[':parent_id'=>$parent_id,'bonus_type'=>'1'])->fetchField();
    $total_points = 0;
    if (!empty($ump_no_of_row_bonus)) {
        for($i=1;$i<=$ump_no_of_row_bonus;$i++)
        {          
          $total_points = $ump_bonuses->get('ump_no_of_'.$i.'_bonus_referral');
          if (!empty($bonus_members) && ($bonus_members >= $ump_bonuses->get('ump_no_of_'.$i.'_bonus_referral')) && ($get_max_id < $total_points)) {
              $returndata[] = array('id' => $parent_id, 'name' => ump_get_child_user_name_by_id($parent_id), 'referrals' => $ump_bonuses->get('ump_no_of_'.$i.'_bonus_referral'), 'amount' => $ump_bonuses->get('ump_bonus_'.$i.'_commission'));
          }
        }
    }
    return $returndata;
}

function ump_user_bonus_commission_for_payout($user_id)
{
  $connection=\Drupal::service('database'); 
  $bonus_commission = $connection->query("SELECT SUM(amount) as total FROM {ump_bonus} WHERE parent_id=:user_id AND bonus_type=:bonus_type AND payout_id=:payout_id",[':user_id'=>$user_id,':bonus_type'=>'1',':payout_id'=>'0'])->fetchField();
   
  return ($bonus_commission>0)?$bonus_commission:'0';
   
}

function check_ump_user($user_id)
{
    $connection=\Drupal::service('database');
    return $connection->query("SELECT user_id FROM {ump_user} WHERE user_id =:user_id",[':user_id'=>$user_id])->fetchField();
     
} 

function ump_get_info_by_mail($mail_name)
{
  $connection=\Drupal::service('database');
    return  $connection->query("SELECT * FROM {users_field_data} WHERE mail=:mail_name",[':mail_name'=>$mail_name])->fetchObject();
}
 
function ump_total_payout_amounts($user_id)
{  
  $connection=\Drupal::service('database');  
  $result = $connection->query("SELECT SUM(total_commission) as total_commission, SUM(referral_commission_amount) as referral_commission_amount, SUM(join_commission_amount) as join_commission_amount, SUM(level_commission_amount) as level_commission_amount, SUM(regular_bonus_amount) as regular_bonus_amount, SUM(total_amount) as total_amount FROM {ump_payout} WHERE user_id=:user_id",[':user_id'=>$user_id])->fetchAll();  
  return $result;   
}

function ump_get_payout_data_by_payout_id($payout_id)
{
    $connection=\Drupal::service('database'); 
    return $connection->query("SELECT * FROM {ump_payout} WHERE id=:payout_id",[':payout_id'=>$payout_id])->fetchObject();
}

 

function ump_epin_genarate($number){

  $characters = array("0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z");
  $keys = array();
  while (count($keys) < $number) {
      $x = random_int(0, count($characters) - 1);
      if (!in_array($x, $keys))
          $keys[] = $x;
  }
  $random_chars = '';
  foreach ($keys as $key)
      $random_chars .= $characters[$key];
      return $random_chars;
  }
 
function ump_register_mail_function($user_id){
      $key='ump_registration'; 
      $message = t('New User is Registered now and emialid is ') ;
      $ump_user_email=get_email_by_user_id($user_id); 
      $user_name=ump_get_child_user_name_by_id($user_id);      
      $title=t(' New Registration on Ump ');                         
      $message .= $ump_user_email;       
      $message .= t(' and user name is ');
      $message .= $user_name;
      $admin_email=\Drupal::config('system.site')->get('mail');       
      ump_send_mail_function($key,$ump_user_email,$title,$message);
      ump_send_mail_function($key,$admin_email,$title,$message);          
    
}

function ump_join_mail_function($user_id){
   
  $key='user_join_ump';
  $message = t('You Have Join Ump Mlm Plan Now You are Mlm User ');        
  $ump_user_email=get_email_by_user_id($user_id);  
  $message .= $ump_user_email;
  $title=t('Join Us Ump'); 
  ump_send_mail_function($key,$ump_user_email,$title,$message);
  $admin_email=\Drupal::config('system.site')->get('mail');   
  ump_send_mail_function($key,$admin_email,$title,$message);          
   
}

// function ump_send_payout_mail($user_id, $payout_id)
// {
//     $key='ump_run_payout';     
//     $message = t('Your payout has been run ');        
//     $ump_user_email=get_email_by_user_id($user_id);  
//     $message .= $ump_user_email;
//     $title=t('Payout'); 
//     ump_send_mail_function($key,$ump_user_email,$title,$message);    
// }

function ump_payment_processed_mail($user_id, $request_amount, $remaining_balance, $total_balance, $amount)
{
    $key='ump_user_withdrawal_request'; 
    $message = t('Your withdrawal request has been Sent ');   
    $user_name=ump_get_child_user_name_by_id($user_id);     
    $ump_user_email=get_email_by_user_id($user_id);  
    $message .= $ump_user_email;
    $message .= t(' Your requested Amount is ');
    $message .=  $amount;
    $message .= t(' and user name is ');
    $message .= $user_name;
    $title=t('Withdrawal'); 
    ump_send_mail_function($key,$ump_user_email,$title,$message);   
     
     
}

function ump_withdrwal_pay($user_id, $id, $withdrwal_amount=NULL, $cur_amount=NULL)
{
    $connection=\Drupal::service('database');    
    $key='ump_admin_transfer_amount';
    $user_name=ump_get_child_user_name_by_id($user_id);    
    $message = t('Your  requested amount is transfer and email id is ');        
    $ump_user_email=get_email_by_user_id($user_id);  
    $message .= $ump_user_email;     
    $message .= t(' and amount is ');
    $message .= $withdrwal_amount;
    $message .= t(' and user name is ');
    $message .= $user_name;
    $title=t('Transfer Amount'); 
    // $withdrawal_pay = $connection->query("SELECT * FROM {ump_withdrawal} WHERE user_id=:user_id AND id=:id",[':user_id'=>$user_id, ':id'=>$id])->fetchObject();  
       
    ump_send_mail_function($key,$ump_user_email,$title,$message);  
     
}

function get_user_uri($user_id){
  $connection = \Drupal::service('database');        
  $result = $connection->query("SELECT * FROM {file_usage} where id=:user_id",[':user_id'=>$user_id])->fetchObject();
  if(!empty($result)){
    $uri= $connection->query("SELECT * FROM {file_managed} where fid=:fid AND uid=:id",[':fid'=>$result->fid,':id'=>$result->id])->fetchObject(); 
    return (!empty($uri))?$uri:'';
  }else{
    return '';
  }
}
 

?>
/**
 * Implements hook_form_alter().
 */
function unilevelmlm_form_alter(&$form, \Drupal\Core\Form\FormStateInterface $form_state, $form_id) {
  if ($form_id == 'node_product_edit_form' || $form_id == 'node_product_form') {
    $form['product_image'] = [
      '#type' => 'managed_file',
      '#title' => t('Product Image'),
      '#upload_location' => 'public://products/',
      '#upload_validators' => [
        'file_validate_extensions' => ['gif png jpg jpeg'],
        'file_validate_size' => [5 * 1024 * 1024],
      ],
    ];

    $form['actions']['submit']['#submit'][] = 'unilevelmlm_node_product_form_submit';
  }
}

/**
 * Submit handler for node_product_form and node_product_edit_form.
 */
function unilevelmlm_node_product_form_submit(&$form, \Drupal\Core\Form\FormStateInterface $form_state) {
  $file = $form_state->getValue('product_image');
  if (!empty($file[0])) {
    // Load the file entity.
    $file = \Drupal\file\Entity\File::load($file[0]);
    // Set the file permanent.
    $file->setPermanent();
    $file->save();
    // Save the file URI to the node.
    $node = $form_state->getFormObject()->getEntity();
    $node->set('field_product_image', $file->getFileUri());
  }
}
/**
 * Implements hook_form_alter().
 */
function unilevelmlm_form_alter(&$form, \Drupal\Core\Form\FormStateInterface &$form_state, $form_id) {
  if ($form_id == 'node_product_edit_form' || $form_id == 'node_product_form') {
    $form['rebate_discount_rules'] = [
      '#type' => 'entity_autocomplete',
      '#title' => t('Rebate/Discount Rules'),
      '#description' => t('Select the rebate/discount rules to apply to this product.'),
      '#target_type' => 'rebate_discount_rule', // Replace with your entity type name
      '#selection_settings' => [
        'target_bundles' => ['rebate_discount_rule'], // Replace with your bundle name if needed
      ],
      '#tags' => TRUE,
    ];
  }
}