{{attach_library('unilevelmlm/unilevelmlm')}}
<div class="let-container let-border-left let-border-right">
    <div class="let-content ">
        <h5 class="let-text-info">{{'Payout Report' |t}} {{user_name}} </h5>
        <hr />
        <div class="let-col-md-12 let-m-0">
            <div class="let-card shadow let-mb-4">
                <div class="let-card-header ump-bg let-py-3">
                    <h6 class="let-m-0 let-font-weight-bold ">{{'Join Commissions' |t}}</h6>
                </div>
                <div class="let-card-body">
                    <div class="let-table-responsive">
                        <table class="let-table let-table-bordered let-table-striped" id="join_dataTable" width="100%"
                            cellspacing="0">
                            <thead>
                                <tr>
                                    <th>{{'#ID' |t}}</th>
                                    <th>{{'User Name' |t}}</th>
                                    <th>{{'Child Name' |t}}</th>
                                    <th>{{'Amount' |t}}</th>
                                    <th>{{'Date' |t}}</th>
                                </tr>
                            </thead>
                            <tfoot>
                                <tr>
                                    <th>{{'#ID' |t}}</th>
                                    <th>{{'User Name' |t}}</th>
                                    <th>{{'Child Name' |t}}</th>
                                    <th>{{'Amount' |t}}</th>
                                    <th>{{'Date' |t}}</th>

                                </tr>
                            </tfoot>
                            <tbody>

                                {% for value in join_commission %}
                                <tr>
                                    <td>{{value.id}}</td>
                                    <td>{{value.parent_name}}</td>
                                    <td>{{value.child_name}}</td>
                                    <td>{{value.amount}}</td>
                                    <td>{{value.date_notified}}</td>

                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="let-col-md-12 let-m-0">
            <div class="let-card shadow let-mb-4">
                <div class="let-card-header ump-bg let-py-3">
                    <h6 class="let-m-0 let-font-weight-bold ">{{'Referral Commissions' |t}}</h6>
                </div>
                <div class="let-card-body">
                    <div class="let-table-responsive">
                        <table class="let-table let-table-bordered let-table-striped" id="join_dataTable" width="100%"
                            cellspacing="0">
                            <thead>
                                <tr>
                                    <th>{{'#ID' |t}}</th>
                                    <th>{{'User Name' |t}}</th>
                                    <th>{{'Child Name' |t}}</th>
                                    <th>{{'Amount' |t}}</th>
                                    <th>{{'Date' |t}}</th>
                                </tr>
                            </thead>
                            <tfoot>
                                <tr>
                                    <th>{{'#ID' |t}}</th>
                                    <th>{{'User Name' |t}}</th>
                                    <th>{{'Child Name' |t}}</th>
                                    <th>{{'Amount' |t}}</th>
                                    <th>{{'Date' |t}}</th>

                                </tr>
                            </tfoot>
                            <tbody>

                                {% for value in referral_commission %}
                                <tr>
                                    <td>{{value.id}}</td>
                                    <td>{{value.parent_name}}</td>
                                    <td>{{value.child_name}}</td>
                                    <td>{{value.amount}}</td>
                                    <td>{{value.date_notified}}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>       
    </div>
</div>