{#
/**
* @file
* Default theme implementation to display the file entity as an audio tag.
*
* Available variables:
* - attributes: An array of HTML attributes, intended to be added to the
*   audio tag.
* - files: And array of files to be added as sources for the audio tag. Each
*   element is an array with the following elements:
*   - file: The full file object.
*   - source_attributes: An array of HTML attributes for to be added to the
*     source tag.
*
* @ingroup themeable
*/
#}
{{ attach_library('claro/classy.file') }}
<audio {{ attributes }}>
  {% for file in files %}
    <source {{ file.source_attributes }} />
  {% endfor %}
</audio>
