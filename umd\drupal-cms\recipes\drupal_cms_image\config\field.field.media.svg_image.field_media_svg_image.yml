langcode: en
status: true
dependencies:
  config:
    - field.storage.media.field_media_svg_image
    - media.type.svg_image
  module:
    - image
id: media.svg_image.field_media_svg_image
field_name: field_media_svg_image
entity_type: media
bundle: svg_image
label: Image
description: ''
required: true
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: '[date:custom:Y]-[date:custom:m]'
  file_extensions: 'svg'
  max_filesize: ''
  max_resolution: ''
  min_resolution: ''
  alt_field: true
  alt_field_required: true
  title_field: false
  title_field_required: false
  default_image:
    uuid: null
    alt: ''
    title: ''
    width: null
    height: null
field_type: image
