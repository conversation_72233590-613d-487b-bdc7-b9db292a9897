<!-- withdrawal req -->
{{attach_library('unilevelmlm/unilevelmlm')}}
<div class="let-card shadow let-m-3">
    <div class="let-card-header ump-bg let-py-3">
        <h6 class="let-m-0 let-font-weight-bold ">
            {{'Withdrawal Request' |t}}
        </h6>
    </div>
    <div class="let-card-body">
        <div class="let-col-md-12">
            <table class="let-table let-table-bordered let-table-striped">
                <thead>
                    <tr class="let-text-center">
                        <th>{{'User Name' |t}}</th>
                        <th>{{'Amount' |t}}</th>
                        <th>{{'Request Date' |t}}</th>
                    </tr>
                </thead>

                <tbody class="let-text-center">
                    <tr>
                        <td>{{user_name}}</td>
                        <td>{{data.amount}}</td>
                        <td>{{data.withdrawal_initiated_date}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
<!-- withdrawal req -->
<div class="let-col-md-12 let-row let-m-0">
    <div class="let-col-md-5 let-pl-0">
        <div class="let-card shadow ">
            <div class="let-card-header ump-bg let-py-3">
                <h6 class="let-m-0 let-font-weight-bold ">
                    {{'Account details' |t}}
                </h6>
            </div>
            <div class="let-card-body">
                <div class="let-table-responsive">
                    <table class=" let-table let-table-bordered let-table-striped">
                        <thead>
                            <tr class="ump-bg let-text-white ">
                                <th colspan="2" class="let-p-08"> {{'Bank Details' |t}} </th>
                            </tr>
                        </thead>
                        <tbody class="let-text-center">
                            <tr>
                                <th class="let-align-middle">{{'Account Holder' |t}}</th>
                                <td>{{bank.account_holder}}</td>
                            </tr>
                            <tr>
                                <th class="let-align-middle">{{'Account Number' |t}}</th>
                                <td>{{bank.account_number}}</td>
                            </tr>
                            <tr>
                                <th class="let-align-middle">{{'Bank Name' |t}}</th>
                                <td>{{bank.bank_name}}</td>
                            </tr>
                            <tr>
                                <th class="let-align-middle">{{'Branch Name' |t}}</th>
                                <td>{{bank.branch}}</td>
                            </tr>
                            <tr>
                                <th class="let-align-middle">{{'IFSC Code' |t}}</th>
                                <td>{{bank.ifsc_code}}</td>
                            </tr>
                            <tr>
                                <th class="let-align-middle">{{'Mobile' |t}}</th>
                                <td>{{bank.contact_no}}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="let-col-md-7 let-pr-0">
        <div class="let-loader-layer" id="admin_transfer_amount_loader">
            <div class="let-pic-loader"></div>
        </div>
        <div class="let-card shadow ">
            <div class="let-card-header ump-bg let-py-3">
                <h6 class="let-m-0 let-font-weight-bold ">
                    {{'Payment form' |t}}
                </h6>
            </div>
            <div class="let-card-body">
                <form method="POST" action="" id="umw_payment_form">
                    <div class="let-table-responsive">
                        <table class="let-table let-table-bordered">
                            <thead>
                                <tr class="ump-bg let-text-white ">
                                    <th colspan="2" class="let-p-08">{{'Payment Form' |t}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <th class="let-align-middle let-text-center">
                                        {{'Request Amount' |t}}
                                    </th>
                                    <td>
                                        <input type="number" value="{{data.amount}}" name="ump_withdrawal_pay_amount"
                                            readonly="true" class="let-form-control">
                                        <input type="hidden" value="{{data.user_id}}" name="user_id"
                                            class="let-form-control">
                                        <input type="hidden" value="{{data.id}}" name="id" class="let-form-control">

                                    </td>
                                </tr>
                                <tr>
                                    <th class="let-align-middle let-text-center">
                                        {{'Payment Mode' |t}}
                                    </th>
                                    <td>
                                        <select class="let-form-control let-w-ma" name="ump_withdrawal_payment_mode">
                                            <option value="">{{'Choose Mode' |t}} </option>
                                            <option value="paypal">{{'Paypal' |t}} </option>
                                            <option value="bank_transfer">{{'Bank Transfer' |t}}</option>
                                        </select>

                                    </td>
                                </tr>
                                <tr>
                                    <th class="let-align-middle let-text-center">
                                        {{'Transaction id' |t}}
                                    </th>
                                    <td>
                                        <input type="text" name="ump_withdrawal_transaction_id"
                                            class="let-form-control">
                                    </td>
                                </tr>
                            </tbody>
                            <tfoot>
                                <tr class="let-alert-success">
                                    <td colspan="2">
                                        <div class="let-text-center let-text-black">
                                            {{'Here you can only update the transaction Details (You need to do
                                            transaction manually' |t}}
                                        </div>
                                    </td>
                                </tr>
                                {% if data.transaction_id is not empty and data.payment_processed=='1' %}
                                <tr>
                                    <td colspan="2" class="let-text-center">
                                        <span class="let-alert let-alert-success"> {{'Payment already Updated'
                                            |t}}</span>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="2" class="let-text-center">
                                        <button type="submit" class="let-btn let-btn-primary ">{{'Submit' |t}} </button>
                                    </td>
                                </tr>
                                {% endif %}
                            </tfoot>
                        </table>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!-- withdrawal req -->
<!-- withdrawal history -->

<div class="let-col-md-12 let-pl-0">
    <div class="let-card shadow let-m-2">
        <div class="let-card-header ump-bg let-py-3">
            <h6 class="let-m-0 let-font-weight-bold ">
               {{' Withdrawals History' |t}}
            </h6>
        </div>
        <div class="let-card-body">
            <div class="let-col-md-12  let-p-2 umw-tp">
                <div class="let-table-responsive">
                    <table class="let-table let-table-bordered let-table-striped" id="ump_withdrawals_history"
                        cellspacing="0" width="100%">
                        <thead class="">
                            <tr class="let-text-center">
                                <td>{{ "Amount" |t }}</td>
                                <td>{{'Intiated Date' |t}}</td>
                                <td>{{'Processed Date' |t }}</td>
                                <td>{{'Payment Mode' |t }}</td>
                                <td>{{'Transaction Id' |t }}</td>
                                <td>{{'Bank Name' |t }}</td>
                                <td>{{'Account No' |t }}</td>
                                <td>{{'Status' |t }}</td>
                            </tr>
                        </thead>
                        <tbody>
                            {% if withdrawal_history %}
                            {% for value in withdrawal_history %}
                            <tr class="let-text-center">
                                <td class="let-align-middle">{{value.amount}}</td>
                                <td class="let-align-middle">{{value.withdrawal_initiated_date}}</td>
                                <td class="let-align-middle">{{value.payment_processed_date}}</td>
                                <td class="let-align-middle">{{value.payment_mode}}</td>
                                <td class="let-align-middle">{{value.transaction_id}}</td>
                                <td class="let-align-middle">{{value.user_bank_name}}</td>
                                <td class="let-align-middle">{{value.user_bank_account_no}}</td>
                                <td class="let-align-middle">{{value.payment_processed}}</td>
                            </tr>
                            {% endfor %}
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- withdrawal history -->