langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_geofield
    - node.type.event
  module:
    - geocoder_field
    - geofield
third_party_settings:
  geocoder_field:
    method: geocode
    weight: 0
    field: field_event__location_address
    skip_not_empty_value: false
    disabled: false
    hidden: false
    providers:
      - nominatim
    dumper: wkt
    delta_handling: s_to_m
    failure:
      handling: preserve
      status_message: true
      log: true
id: node.event.field_geofield
field_name: field_geofield
entity_type: node
bundle: event
label: Geofield
description: ''
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: geofield
