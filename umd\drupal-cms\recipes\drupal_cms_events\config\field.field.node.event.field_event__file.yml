langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_event__file
    - media.type.document
    - node.type.event
id: node.event.field_event__file
field_name: field_event__file
entity_type: node
bundle: event
label: File
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      document: document
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
