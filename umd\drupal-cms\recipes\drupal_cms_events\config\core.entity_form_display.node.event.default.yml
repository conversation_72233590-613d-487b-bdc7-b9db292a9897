langcode: en
status: true
dependencies:
  config:
    - field.field.node.event.field_content
    - field.field.node.event.field_event__date
    - field.field.node.event.field_description
    - field.field.node.event.field_featured_image
    - field.field.node.event.field_event__file
    - field.field.node.event.field_geofield
    - field.field.node.event.field_event__link
    - field.field.node.event.field_event__location_address
    - field.field.node.event.field_event__location_name
    - field.field.node.event.field_tags
    - node.type.event
    - workflows.workflow.basic_editorial
  module:
    - address
    - link
    - media_library
    - path
    - smart_date
    - text
    - content_moderation
    - scheduler
id: node.event.default
targetEntityType: node
bundle: event
mode: default
content:
  field_content:
    type: text_textarea
    weight: 13
    region: content
    settings:
      rows: 9
      placeholder: ''
    third_party_settings: {  }
  created:
    type: datetime_timestamp
    weight: 2
    region: content
    settings: {  }
    third_party_settings: {  }
  field_event__date:
    type: smartdate_inline
    weight: 5
    region: content
    settings:
      modal: false
      default_duration: 60
      default_duration_increments: |-
        30
        60|1 hour
        90
        120|2 hours
        custom
      show_extra: false
      hide_date: true
      allday: true
      remove_seconds: false
      duration_overlay: true
      separator: to
    third_party_settings: {  }
  field_description:
    type: string_textarea
    weight: 12
    region: content
    settings:
      rows: 5
      placeholder: ''
    third_party_settings: {  }
  field_featured_image:
    type: media_library_widget
    weight: 3
    region: content
    settings:
      media_types: {  }
    third_party_settings: {  }
  field_event__file:
    type: media_library_widget
    weight: 15
    region: content
    settings:
      media_types: {  }
    third_party_settings: {  }
  field_event__link:
    type: link_default
    weight: 14
    region: content
    settings:
      placeholder_url: ''
      placeholder_title: ''
    third_party_settings: {  }
  field_event__location_address:
    type: address_default
    weight: 11
    region: content
    settings:
      wrapper_type: fieldset
    third_party_settings: {  }
  field_event__location_name:
    type: string_textfield
    weight: 10
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  field_tags:
    type: tagify_entity_reference_autocomplete_widget
    weight: 16
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 8
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  moderation_state:
    type: moderation_state_default
    weight: -7
    region: content
    settings: {  }
    third_party_settings: {  }
  path:
    type: path
    weight: 7
    region: content
    settings: {  }
    third_party_settings: {  }
  promote:
    type: boolean_checkbox
    weight: 4
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  publish_on:
    type: datetime_timestamp_no_default
    weight: -5
    region: content
    settings: {  }
    third_party_settings: {  }
  publish_status:
    type: scheduler_moderation
    weight: -4
    region: content
    settings: {  }
    third_party_settings: {  }
  scheduler_settings:
    weight: -6
    region: content
    settings: {  }
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 9
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  sticky:
    type: boolean_checkbox
    weight: 6
    region: content
    settings:
      display_label: true
    third_party_settings: {  }
  title:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 1
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
  unpublish_on:
    type: datetime_timestamp_no_default
    weight: -3
    region: content
    settings: {  }
    third_party_settings: {  }
hidden:
  field_geofield: true
  publish_state: true
  unpublish_state: true
