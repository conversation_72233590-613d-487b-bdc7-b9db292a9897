langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.media.16_9_medium
    - core.entity_view_mode.media.16_9_small
    - core.entity_view_mode.media.1_1_square_medium
    - core.entity_view_mode.media.1_1_square_small
    - core.entity_view_mode.media.3_4_medium
    - core.entity_view_mode.media.3_4_small
    - core.entity_view_mode.media.4_3_medium
    - core.entity_view_mode.media.4_3_small
    - core.entity_view_mode.media.9_16_medium
    - core.entity_view_mode.media.9_16_small
    - core.entity_view_mode.media.medium
    - core.entity_view_mode.media.small
  module:
    - linkit
    - media
name: Content
format: content_format
weight: 0
filters:
  filter_align:
    id: filter_align
    provider: filter
    status: true
    weight: 0
    settings: {  }
  filter_autop:
    id: filter_autop
    provider: filter
    status: true
    weight: 0
    settings: {  }
  filter_caption:
    id: filter_caption
    provider: filter
    status: true
    weight: 0
    settings: {  }
  filter_html:
    id: filter_html
    provider: filter
    status: true
    weight: -10
    settings:
      allowed_html: '<br> <p class="text-align-left text-align-center text-align-right text-align-justify"> <h2 class="text-align-left text-align-center text-align-right text-align-justify"> <h3 class="text-align-left text-align-center text-align-right text-align-justify"> <h4 class="text-align-left text-align-center text-align-right text-align-justify"> <h5 class="text-align-left text-align-center text-align-right text-align-justify"> <h6 class="text-align-left text-align-center text-align-right text-align-justify"> <strong> <em> <blockquote> <a href data-entity-type data-entity-uuid data-entity-substitution> <ul> <ol reversed start> <li> <table> <tr> <td rowspan colspan> <th rowspan colspan> <thead> <tbody> <tfoot> <caption> <drupal-media data-entity-type data-entity-uuid alt data-view-mode data-caption data-align>'
      filter_html_help: false
      filter_html_nofollow: false
  filter_htmlcorrector:
    id: filter_htmlcorrector
    provider: filter
    status: true
    weight: 10
    settings: {  }
  filter_image_lazy_load:
    id: filter_image_lazy_load
    provider: filter
    status: true
    weight: 15
    settings: {  }
  filter_url:
    id: filter_url
    provider: filter
    status: true
    weight: 0
    settings:
      filter_url_length: 72
  linkit:
    id: linkit
    provider: linkit
    status: true
    weight: 0
    settings:
      title: false
  media_embed:
    id: media_embed
    provider: media
    status: true
    weight: 100
    settings:
      default_view_mode: medium
      allowed_view_modes:
        16_9_medium: 16_9_medium
        16_9_small: 16_9_small
        1_1_square_medium: 1_1_square_medium
        1_1_square_small: 1_1_square_small
        3_4_medium: 3_4_medium
        3_4_small: 3_4_small
        4_3_medium: 4_3_medium
        4_3_small: 4_3_small
        9_16_medium: 9_16_medium
        9_16_small: 9_16_small
        medium: medium
        small: small
      allowed_media_types: {  }
