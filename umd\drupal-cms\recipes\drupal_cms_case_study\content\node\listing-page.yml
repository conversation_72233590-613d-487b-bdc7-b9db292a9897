_meta:
  version: '1.0'
  entity_type: node
  uuid: e5247df7-3f19-46c6-bfda-88f26db57a37
  bundle: page
  default_langcode: en
default:
  revision_uid:
    -
      target_id: 1
  status:
    -
      value: true
  uid:
    -
      target_id: 1
  title:
    -
      value: 'Case studies'
  created:
    -
      value: 1734416005
  promote:
    -
      value: true
  sticky:
    -
      value: false
  revision_translation_affected:
    -
      value: true
  moderation_state:
    -
      value: published
  path:
    -
      alias: /case-studies
      langcode: en
  field_description:
    -
      value: 'Listing page for case studies.'
  layout_builder__layout:
    -
      section:
        layout_id: layout_onecol
        layout_settings:
          label: ''
        components:
          541922dc-a8c8-4b74-9cdd-88a1f5d053dd:
            uuid: 541922dc-a8c8-4b74-9cdd-88a1f5d053dd
            region: content
            configuration:
              id: 'field_block:node:page:field_content'
              label_display: '0'
              context_mapping:
                entity: layout_builder.entity
              formatter:
                type: text_default
                label: hidden
                settings: {  }
                third_party_settings: {  }
            weight: 2
            additional: {  }
          261b00fc-c08b-46ea-bfa5-40464dc7f794:
            uuid: 261b00fc-c08b-46ea-bfa5-40464dc7f794
            region: content
            configuration:
              id: 'extra_field_block:node:page:content_moderation_control'
              label_display: '0'
              context_mapping:
                entity: layout_builder.entity
            weight: 0
            additional: {  }
          3e00468d-735d-4adf-aa2b-8182af870467:
            uuid: 3e00468d-735d-4adf-aa2b-8182af870467
            region: content
            configuration:
              id: 'views_block:case_studies-all'
              label: ''
              label_display: 0
              provider: views
              views_label: ''
              items_per_page: none
              context_mapping: {  }
            weight: 5
            additional: {  }
          379459f5-ae09-4f63-bc29-1370f7af6460:
            uuid: 379459f5-ae09-4f63-bc29-1370f7af6460
            region: content
            configuration:
              id: 'field_block:node:page:field_tags'
              label: Tags
              label_display: 0
              provider: layout_builder
              formatter:
                label: inline
                type: entity_reference_label
                settings:
                  link: 1
                third_party_settings: {  }
              context_mapping:
                entity: layout_builder.entity
                view_mode: view_mode
            weight: 4
            additional: {  }
          fe77cbf5-**************-0164fe32e6e3:
            uuid: fe77cbf5-**************-0164fe32e6e3
            region: content
            configuration:
              id: 'field_block:node:page:field_featured_image'
              label: 'Featured image'
              label_display: 0
              provider: layout_builder
              formatter:
                label: hidden
                type: entity_reference_entity_view
                settings:
                  view_mode: hero
                third_party_settings: {  }
              context_mapping:
                entity: layout_builder.entity
                view_mode: view_mode
            weight: 1
            additional: {  }
        third_party_settings: {  }
