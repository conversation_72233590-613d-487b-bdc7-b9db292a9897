{#
/**
 * @file
 * Theme override to display a node.
 *
 * Available variables:
 * - node: The node entity with:
 *   - nid: The node ID.
 *   - title: The node title.
 *   - created: The time the node was created.
 *   - changed: The time the node was last updated.
 *   - body: The node body, if it exists.
 *   - field_product_image: The product image, if it exists.
 *   - field_product_description: The product description, if it exists.
 *   - field_product_category: The product category, if it exists.
 * - label: The node label.
 * - content: All node items. Use {{ content }} to print them all,
 *   or print a subset such as {{ content.field_example }}. Use
 *   {{ content|without('field_example') }} to temporarily suppress the printing
 *   of one field.
 * - author_picture: The node author user entity, rendered using the "user_picture" display.
 * - metadata: Metadata for this node.
 */
#}
<article{{ attributes.addClass('node', 'node--type-' ~ node.bundle|clean_class) }}>
  <header>
    {{ title_prefix }}
    {% if label and not page %}
      <h2{{ title_attributes }}>
        <a href="{{ url }}" rel="bookmark">{{ label }}</a>
      </h2>
    {% endif %}
    {{ title_suffix }}

    {% if display_submitted %}
      {{ metadata }}
    {% endif %}
  </header>
  <div{{ content_attributes }}>
    {% if content.field_product_image %}
      {{ content.field_product_image }}
    {% endif %}
    {% if content.field_product_description %}
      {{ content.field_product_description }}
    {% endif %}
    {% if content.field_product_category %}
      {{ content.field_product_category }}
    {% endif %}
    {{ content|without('field_product_image', 'field_product_description', 'field_product_category') }}
  </div>
</article>