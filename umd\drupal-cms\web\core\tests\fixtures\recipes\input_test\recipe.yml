name: Input Test
recipes:
  # Depend on another recipe in order to prove that we can collect input
  # from recipes that depend on other recipes.
  - no_extensions
install:
  - node
input:
  owner:
    data_type: string
    description: 'The name of the site owner.'
    constraints:
      Regex:
        pattern: '/hack/i'
        match: false
        message: "I don't think you should be owning sites."
    form:
      '#type': textfield
      '#title': "Site owner's name"
    default:
      source: value
      value: 'Dries Buytaert'
  break_stuff:
    data_type: boolean
    description: 'Should mischief be allowed on this site? Not recommended.'
    form:
      '#type': checkbox
      '#title': 'Allow mischief'
    default:
      source: value
      value: false
  node_type:
    data_type: string
    description: 'The ID of a node type to create'
    default:
      source: value
      value: 'test'
config:
  actions:
    node.type.${node_type}:
      createIfNotExists:
        name: Test Content Type
    system.site:
      simpleConfigUpdate:
        name: "${owner}'s Turf"
