/**
 * @file
 * Table select styles for Claro.
 *
 * @see tableselect.js
 */

td.checkbox,
th.checkbox {
  text-align: center;
}

tr.selected td {
  background-color: var(--color-bgblue-active);
}

.views-bulk-actions {
  position: relative;
  display: flex;
  flex: 1;
  flex-wrap: wrap;
  padding: var(--space-m) 2rem;
  color: var(--color-white);
  border: var(--details-border-size) solid var(--details-border-color);
  border-radius: 4px;
  background-color: var(--color-text);

  &[data-drupal-sticky-vbo="true"] {
    position: sticky;
    z-index: calc(var(--dropbutton-widget-z-index) + 1);
    bottom: var(--drupal-displace-offset-bottom, 0);
    animation: fade-in-bottom 320ms 1 forwards;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
  }

  &.views-form__header--bypass-animation {
    animation: none;
  }
}

@supports (position: sticky) {
  .views-bulk-actions[data-drupal-sticky-vbo="true"] {
    position: sticky;
  }
}

@keyframes fade-in-bottom {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}

.views-bulk-actions__item {
  align-self: center;
  margin-block: 0 var(--space-s);
  margin-inline: 0 var(--space-l);
  &:last-of-type {
    margin-inline-end: 0;
  }
  & .form-element {
    &:hover {
      border: var(--input-border-size) solid var(--input-border-color);
      box-shadow: none;
    }
  }
  & .button--primary:not(:disabled, .is-disabled) {
    background: var(--color-blue-400);
    &:hover {
      background: var(--color-blue-500);
    }
  }
  & .form-item__label {
    display: inline;
    padding-inline-end: var(--space-xs);
    &::after {
      content: ":";
    }
  }
  & .form-element--type-select {
    min-height: 2rem;
    padding-block: calc(0.5rem - 1px);
    padding-inline: calc(1rem - 1px) calc(2.25rem - 1px);
    font-size: var(--font-size-xs);
    line-height: 1rem;
  }
  & input,
  & .button {
    margin-block: 0;
  }
}
.views-bulk-actions__item--status {
  width: 100%;
  white-space: nowrap;
  font-size: var(--font-size-xs);
  font-weight: bold;
}

.views-field__skip-to-bulk-actions {
  display: block;
  white-space: nowrap;
  font-size: var(--font-size-xs);
}

@media screen and (min-width: 61rem) {
  .views-bulk-actions {
    flex-wrap: nowrap;
  }
  .views-bulk-actions__item {
    margin-bottom: 0;
  }
  .views-bulk-actions__item--status {
    width: auto;
  }
}

.ui-autocomplete {
  z-index: 501;
}
