langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_featured_image
    - media.type.image
    - node.type.event
id: node.event.field_featured_image
field_name: field_featured_image
entity_type: node
bundle: event
label: 'Featured image'
description: 'Include an image. This appears as the image in search engine results.'
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:media'
  handler_settings:
    target_bundles:
      image: image
    sort:
      field: _none
      direction: ASC
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
